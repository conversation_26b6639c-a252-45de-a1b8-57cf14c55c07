<?php foreach ($companyTabList as $tabKey => $tab) {?>
<div class="site-item <?=(intval($tab['activityId']) == intval($activityId) ? 'active' : '')?> <?=$tab['activityChildStatus'] == 4 ? ' is-end' : ''?>" data-specialId="<?=$tab['specialId']?>" data-activityId="<?=$tab['activityId']?>">
    <?php if(($tab['activityChildStatus'] == 2)):?>
    <div class="status await-start-status"><?=$tab['activityChildStatusText']?></div>
    <?php endif?>
    <?php if(($tab['activityChildStatus'] == 3 )):?>
    <div class="status start-status"><?=$tab['activityChildStatusText']?></div>
    <?php endif?>
    <?php if(($tab['activityChildStatus'] == 4 )):?>
    <div class="status end-status"><?=$tab['activityChildStatusText']?></div>
    <?php endif?>
    <div class="name"><?=$tab['activityName']?></div>
    <div class="date"><?=$tab['dateText']?></div>
</div>
<?php } ?>