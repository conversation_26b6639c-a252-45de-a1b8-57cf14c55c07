<!DOCTYPE html>
<!-- saved from url=(0034)https://boshihou.gaoxiaojob.com/12 -->
<html lang="en">
<script src="/404_files/hm.js"></script>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404</title>
    <link rel="stylesheet" href="/404_files/animate.min.css">
    <link rel="stylesheet" href="/404_files/404.css">
    <style type="text/css">
        .zhmMask {
            z-index: 999999999;
            background-color: #000;
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            opacity: 0.8;
        }

        .zhm_wrap-box {
            z-index: 1000000000;
            position: fixed;;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -200px);
            width: 300px;
            color: #555;
            background-color: #fff;
            border-radius: 5px;
            overflow: hidden;
            font: 16px numFont, PingFangSC-Regular, Tahoma, Microsoft Yahei, sans-serif !important;
            font-weight: 400 !important;
        }

        .zhm_setWrapLi {
            margin: 0px;
            padding: 0px;
        }

        .zhm_setWrapLi li {
            background-color: #fff;
            border-bottom: 1px solid #eee;
            margin: 0px !important;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            list-style: none;
        }

        .zhm_setWrapLiContent {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .zhm_setWrapSave {
            position: absolute;
            top: -2px;
            right: 10px;
            font-size: 24px;
            cursor: pointer
        }

        .zhm_iconSetFoot {
            position: absolute;
            bottom: 0px;
            padding: 10px 20px;
            width: 100%;
            z-index: 1000000009;
            background: #fef9ef;
        }

        .zhm_iconSetFootLi {
            margin: 0px;
            padding: 0px;
        }

        .zhm_iconSetFootLi li {
            display: inline-flex;
            padding: 0px 2px;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }

        .zhm_iconSetFootLi li a {
            color: #555;
        }

        .zhm_iconSetFootLi a:hover {
            color: #fe6d73;
        }

        .zhm_iconSetPage {
            z-index: 1000000001;
            position: absolute;
            top: 0px;
            left: 300px;
            background: #fff;
            width: 300px;
            height: 100%;
        }

        .zhm_iconSetUlHead {
            padding: 0px;
            margin: 0px;
        }

        .zhm_iconSetPageHead {
            border-bottom: 1px solid #ccc;
            height: 40px;
            line-height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fe6d73;
            color: #fff;
            font-size: 15px;
        }

        .zhm_iconSetPageLi {
            margin: 0px;
            padding: 0px;
        }

        .zhm_iconSetPageLi li {
            list-style: none;
            padding: 8px 20px;
            border-bottom: 1px solid #eee;
        }

        .zhm_zhihuSetPage {
            z-index: 1000000002;
            position: absolute;
            top: 0px;
            left: 300px;
            background: #fff;
            width: 300px;
            height: 100%;
        }

        .zhm_iconSetPageInput {
            display: flex !important;
            justify-content: space-between;
            align-items: center;
        }

        .zhihuSetPageLi {
            margin: 0px;
            padding: 0px;
            height: 258px;
            overflow-y: scroll;
        }

        .zhihuSetPageContent {
            display: flex !important;
            justify-content: space-between;
            align-items: center;
        }

        .zhm_circular {
            width: 40px;
            height: 20px;
            border-radius: 16px;
            transition: .3s;
            cursor: pointer;
            box-shadow: 0 0 3px #999 inset;
        }

        .zhm_round-button {
            width: 20px;
            height: 20px;;
            border-radius: 50%;
            box-shadow: 0 1px 5px rgba(0, 0, 0, .5);
            transition: .3s;
            position: relative;
        }

        .zhm_back {
            border: solid #FFF;
            border-width: 0 3px 3px 0;
            display: inline-block;
            padding: 3px;
            transform: rotate(135deg);
            -webkit-transform: rotate(135deg);
            margin-left: 10px;
            cursor: pointer;
        }

        .zhm_to-right {
            margin-left: 20px;
            display: inline-block;
            padding: 3px;
            transform: rotate(-45deg);
            -webkit-transform: rotate(-45deg);
            cursor: pointer;

        }

        .zhm_iconSetSave {
            font-size: 24px;
            cursor: pointer;
            margin-right: 5px;
            margin-bottom: 4px;
            color: #FFF;
        }

        .zhm_set_page {
            z-index: 1000000003;
            position: absolute;
            top: 0px;
            left: 300px;
            background: #fff;
            width: 300px;
            height: 100%;
        }

        .zhm_set_page_header {
            border-bottom: 1px solid #ccc;
            height: 40px;
            line-height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fe6d73;
            color: #fff;
            font-size: 15px;
        }

        .zhm_set_page_content {
            display: flex !important;
            justify-content: space-between;
            align-items: center;
        }

        .zhm_set_page_list {
            margin: 0px;
            padding: 0px;
            height: 220px;
            overflow-y: scroll;
        }

        .zhm_set_page_list::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 0px; /*高宽分别对应横竖滚动条的尺寸*/
            height: 1px;
        }

        .zhm_set_page_list::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 2px;
            background-color: #fe6d73;
        }

        .zhm_set_page_list::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: #ededed;
            border-radius: 10px;
        }

        .zhm_set_page_list li {
            /*border-bottom:1px solid #ccc;*/
            padding: 12px 20px;
            display: block;
            border-bottom: 1px solid #eee;
        }

        li:last-child {
            border-bottom: none;
        }

        .zhm_scroll {
            overflow-y: scroll !important;
        }

        .zhm_scroll::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 0px; /*高宽分别对应横竖滚动条的尺寸*/
            height: 1px;
        }

        .zhm_scroll::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 2px;
            background-color: #fe6d73;
        }

        .zhm_scroll::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: #ededed;
            border-radius: 10px;
        }

        /*-form-*/
        :root {
            --base-color: #434a56;
            --white-color-primary: #f7f8f8;
            --white-color-secondary: #fefefe;
            --gray-color-primary: #c2c2c2;
            --gray-color-secondary: #c2c2c2;
            --gray-color-tertiary: #676f79;
            --active-color: #227c9d;
            --valid-color: #c2c2c2;
            --invalid-color: #f72f47;
            --invalid-icon: url("data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%3E%20%3Cpath%20d%3D%22M13.41%2012l4.3-4.29a1%201%200%201%200-1.42-1.42L12%2010.59l-4.29-4.3a1%201%200%200%200-1.42%201.42l4.3%204.29-4.3%204.29a1%201%200%200%200%200%201.42%201%201%200%200%200%201.42%200l4.29-4.3%204.29%204.3a1%201%200%200%200%201.42%200%201%201%200%200%200%200-1.42z%22%20fill%3D%22%23f72f47%22%20%2F%3E%3C%2Fsvg%3E");
        }

        .zhm_text-input {
            font-size: 16px;
            position: relative;
            right: 0px;
            z-index: 0;
        }

        .zhm_text-input__body {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: transparent;
            border: 1px solid var(--gray-color-primary);
            border-radius: 3px;
            height: 1.7em;
            line-height: 1.7;
            overflow: hidden;
            padding: 2px 1em;
            text-overflow: ellipsis;
            transition: background-color 0.3s;
            width: 55%;
            font-size: 14px;
            box-sizing: initial;
        }

        .zhm_text-input__body:-ms-input-placeholder {
            color: var(--gray-color-secondary);
        }

        .zhm_text-input__body::-moz-placeholder {
            color: var(--gray-color-secondary);
        }

        .zhm_text-input__body::placeholder {
            color: var(--gray-color-secondary);
        }

        .zhm_text-input__body[data-is-valid] {
            padding-right: 1em;

        }

        .zhm_text-input__body[data-is-valid=true] {
            border-color: var(--valid-color);
        }

        .zhm_text-input__body[data-is-valid=false] {
            border-color: var(--invalid-color);
            box-shadow: inset 0 0 0 1px var(--invalid-color);
        }

        .zhm_text-input__body:focus {
            border-color: var(--active-color);
            box-shadow: inset 0 0 0 1px var(--active-color);
            outline: none;
        }

        .zhm_text-input__body:-webkit-autofill {
            transition-delay: 9999s;
            -webkit-transition-property: background-color;
            transition-property: background-color;
        }

        .zhm_text-input__validator {
            background-position: right 0.5em center;
            background-repeat: no-repeat;
            background-size: 1.5em;
            display: inline-block;
            height: 100%;
            left: 0;
            position: absolute;
            top: 0;
            width: 100%;
            z-index: -1;
        }

        .zhm_text-input__body[data-is-valid=false] + .zhm_text-input__validator {
            background-image: var(--invalid-icon);
        }

        .zhm_select-box {
            box-sizing: inherit;
            font-size: 16px;
            position: relative;
            transition: background-color 0.5s ease-out;
            width: 90px;
        }

        .zhm_select-box::after {
            border-color: var(--gray-color-secondary) transparent transparent transparent;
            border-style: solid;
            border-width: 6px 4px 0;
            bottom: 0;
            content: "";
            display: inline-block;
            height: 0;
            margin: auto 0;
            pointer-events: none;
            position: absolute;
            right: -72px;
            top: 0;
            width: 0;
            z-index: 1;
        }

        .zhm_select-box__body {
            box-sizing: initial;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: transparent;
            border: 1px solid var(--gray-color-primary);
            border-radius: 3px;
            cursor: pointer;
            height: 1.7em;
            line-height: 1.7;
            padding-left: 1em;
            padding-right: calc(1em + 16px);
            width: 140%;
            font-size: 14px;
            padding-top: 2px;
            padding-bottom: 2px;
        }

        .zhm_select-box__body[data-is-valid=true] {
            border-color: var(--valid-color);
            box-shadow: inset 0 0 0 1px var(--valid-color);
        }

        .zhm_select-box__body[data-is-valid=false] {
            border-color: var(--invalid-color);
            box-shadow: inset 0 0 0 1px var(--invalid-color);
        }

        .zhm_select-box__body.focus-visible {
            border-color: var(--active-color);
            box-shadow: inset 0 0 0 1px var(--active-color);
            outline: none;
        }

        .zhm_select-box__body:-webkit-autofill {
            transition-delay: 9999s;
            -webkit-transition-property: background-color;
            transition-property: background-color;
        }

        .zhm_textarea__body {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            background-color: transparent;
            border: 1px solid var(--gray-color-primary);
            border-radius: 0;
            box-sizing: initial;
            font: inherit;
            left: 0;
            letter-spacing: inherit;
            overflow: hidden;
            padding: 1em;
            position: absolute;
            resize: none;
            top: 0;
            transition: background-color 0.5s ease-out;
            width: 100%;
        }

        .zhm_textarea__body:only-child {
            position: relative;
            resize: vertical;
        }

        .zhm_textarea__body:focus {
            border-color: var(--active-color);
            box-shadow: inset 0 0 0 1px var(--active-color);
            outline: none;
        }

        .zhm_textarea__body[data-is-valid=true] {
            border-color: var(--valid-color);
            box-shadow: inset 0 0 0 1px var(--valid-color);
        }

        .zhm_textarea__body[data-is-valid=false] {
            border-color: var(--invalid-color);
            box-shadow: inset 0 0 0 1px var(--invalid-color);
        }

        .zhm_textarea ._dummy-box {
            border: 1px solid;
            box-sizing: border-box;
            min-height: 240px;
            overflow: hidden;
            overflow-wrap: break-word;
            padding: 1em;
            visibility: hidden;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .zhm_toLeftMove {
            nimation: moveToLeft 0.5s infinite;
            -webkit-animation: moveToLeft 0.5s infinite; /*Safari and Chrome*/
            animation-iteration-count: 1;
            animation-fill-mode: forwards;
        }

        @keyframes moveToLeft {
            from {
                left: 200px;
            }
            to {
                left: 0px;
            }
        }

        @-webkit-keyframes moveToLeft /*Safari and Chrome*/
        {
            from {
                left: 200px;
            }
            to {
                left: 0px;
            }
        }

        .zhm_toRightMove {
            nimation: moveToRight 2s infinite;
            -webkit-animation: moveToRight 2s infinite; /*Safari and Chrome*/
            animation-iteration-count: 1;
            animation-fill-mode: forwards;
        }

        @keyframes moveToRight {
            from {
                left: 0px;
            }
            to {
                left: 2000px;
            }
        }

        @-webkit-keyframes moveToRight /*Safari and Chrome*/
        {
            from {
                left: 0px;
            }
            to {
                left: 200px;
            }
        }
    </style>
    <style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        z-index: 2147483647;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .immersive-translate-attach-loading::after {
        content: " ";

        --loading-color: #f78fb6;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        display: block;
        margin: 12px auto;
        position: relative;
        color: white;
        left: -100px;
        box-sizing: border-box;
        animation: immersiveTranslateShadowRolling 1.5s linear infinite;

        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-2000%, -50%);
        z-index: 100;
    }

    .immersive-translate-loading-spinner {
        vertical-align: middle !important;
        width: 10px !important;
        height: 10px !important;
        display: inline-block !important;
        margin: 0 4px !important;
        border: 2px rgba(221, 244, 255, 0.6) solid !important;
        border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
        border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
        border-radius: 50% !important;
        padding: 0 !important;
        -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
        animation: immersive-translate-loading-animation 0.6s infinite linear !important;
    }

    @-webkit-keyframes immersive-translate-loading-animation {
        from {
            -webkit-transform: rotate(0deg);
        }

        to {
            -webkit-transform: rotate(359deg);
        }
    }

    @keyframes immersive-translate-loading-animation {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(359deg);
        }
    }

    .immersive-translate-input-loading {
        --loading-color: #f78fb6;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        display: block;
        margin: 12px auto;
        position: relative;
        color: white;
        left: -100px;
        box-sizing: border-box;
        animation: immersiveTranslateShadowRolling 1.5s linear infinite;
    }

    @keyframes immersiveTranslateShadowRolling {
        0% {
            box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
            0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
        }

        12% {
            box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
            0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
        }

        25% {
            box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
            0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
        }

        36% {
            box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
            100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
        }

        50% {
            box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
            110px 0 var(--loading-color), 100px 0 var(--loading-color);
        }

        62% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
            120px 0 var(--loading-color), 110px 0 var(--loading-color);
        }

        75% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
            130px 0 var(--loading-color), 120px 0 var(--loading-color);
        }

        87% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
            200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
        }

        100% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
            200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
        }
    }

    .immersive-translate-toast {
        display: flex;
        position: fixed;
        z-index: 2147483647;
        left: 0;
        right: 0;
        top: 1%;
        width: fit-content;
        padding: 12px 20px;
        margin: auto;
        overflow: auto;
        background: #fef6f9;
        box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
        font-size: 15px;
        border-radius: 8px;
        color: #333;
    }

    .immersive-translate-toast-content {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .immersive-translate-toast-hidden {
        margin: 0 20px 0 72px;
        text-decoration: underline;
        cursor: pointer;
    }

    .immersive-translate-toast-close {
        color: #666666;
        font-size: 20px;
        font-weight: bold;
        padding: 0 10px;
        cursor: pointer;
    }

    @media screen and (max-width: 768px) {
        .immersive-translate-toast {
            top: 0;
            padding: 12px 0px 0 10px;
        }

        .immersive-translate-toast-content {
            flex-direction: column;
            text-align: center;
        }

        .immersive-translate-toast-hidden {
            margin: 10px auto;
        }
    }

    .immersive-translate-modal {
        display: none;
        position: fixed;
        z-index: 2147483647;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgb(0, 0, 0);
        background-color: rgba(0, 0, 0, 0.4);
        font-size: 15px;
    }

    .immersive-translate-modal-content {
        background-color: #fefefe;
        margin: 10% auto;
        padding: 40px 24px 24px;
        border: 1px solid #888;
        border-radius: 10px;
        width: 80%;
        max-width: 270px;
        font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
        "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
        "Segoe UI Symbol", "Noto Color Emoji";
        position: relative;
    }

    @media screen and (max-width: 768px) {
        .immersive-translate-modal-content {
            margin: 50% auto !important;
        }
    }

    .immersive-translate-modal .immersive-translate-modal-content-in-input {
        max-width: 500px;
    }

    .immersive-translate-modal-content-in-input .immersive-translate-modal-body {
        text-align: left;
        max-height: unset;
    }

    .immersive-translate-modal-title {
        text-align: center;
        font-size: 16px;
        font-weight: 700;
        color: #333333;
    }

    .immersive-translate-modal-body {
        text-align: center;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        word-break: break-all;
        margin-top: 24px;
    }

    @media screen and (max-width: 768px) {
        .immersive-translate-modal-body {
            max-height: 250px;
            overflow-y: auto;
        }
    }

    .immersive-translate-close {
        color: #666666;
        position: absolute;
        right: 16px;
        top: 16px;
        font-size: 20px;
        font-weight: bold;
    }

    .immersive-translate-close:hover,
    .immersive-translate-close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }

    .immersive-translate-modal-footer {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 24px;
    }

    .immersive-translate-btn {
        width: fit-content;
        color: #fff;
        background-color: #ea4c89;
        border: none;
        font-size: 16px;
        margin: 0 8px;
        padding: 9px 30px;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .immersive-translate-btn:hover {
        background-color: #f082ac;
    }

    .immersive-translate-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .immersive-translate-btn:disabled:hover {
        background-color: #ea4c89;
    }

    .immersive-translate-cancel-btn {
        /* gray color */
        background-color: rgb(89, 107, 120);
    }

    .immersive-translate-cancel-btn:hover {
        background-color: hsl(205, 20%, 32%);
    }

    .immersive-translate-action-btn {
        background-color: transparent;
        color: #ea4c89;
        border: 1px solid #ea4c89;
    }

    .immersive-translate-btn svg {
        margin-right: 5px;
    }

    .immersive-translate-link {
        cursor: pointer;
        user-select: none;
        -webkit-user-drag: none;
        text-decoration: none;
        color: #007bff;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    .immersive-translate-primary-link {
        cursor: pointer;
        user-select: none;
        -webkit-user-drag: none;
        text-decoration: none;
        color: #ea4c89;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    .immersive-translate-modal input[type="radio"] {
        margin: 0 6px;
        cursor: pointer;
    }

    .immersive-translate-modal label {
        cursor: pointer;
    }

    .immersive-translate-close-action {
        position: absolute;
        top: 2px;
        right: 0px;
        cursor: pointer;
    }

    .imt-image-status {
        background-color: rgba(0, 0, 0, 0.5) !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 16px !important;
    }

    .imt-image-status img,
    .imt-image-status svg,
    .imt-img-loading {
        width: 28px !important;
        height: 28px !important;
        margin: 0 0 8px 0 !important;
        min-height: 28px !important;
        min-width: 28px !important;
    }

    .imt-img-loading {
        background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
        background-size: 28px 28px;
        animation: image-loading-rotate 1s linear infinite !important;
    }

    .imt-image-status span {
        color: var(--bg-2, #fff) !important;
        font-size: 14px !important;
        line-height: 14px !important;
        font-weight: 500 !important;
        font-family: "PingFang SC", Arial, sans-serif !important;
    }

    @keyframes image-loading-rotate {
        from {
            transform: rotate(360deg);
        }
        to {
            transform: rotate(0deg);
        }
    }
    </style>
</head>

<body>
<div class="wapper">
    <div class="left-bottom"></div>
    <div class="right-top"></div>
    <div class="main">
        <div class="animate__animated animate__shakeY animate__infinite animation"></div>
    </div>
    <div class="button">
        <a onclick="goHome()" href="javascript:;">返回首页</a>
    </div>
</div>


<script>
    function goHome() {
        window.location.replace('/');
    }


</script>
<script>
    var _hmt = _hmt || []
    ;(function () {
        var hm = document.createElement('script')
        hm.src = 'https://hm.baidu.com/hm.js?978ebde3d51305d6426ecbae9e693030'
        var s = document.getElementsByTagName('script')[0]
        s.parentNode.insertBefore(hm, s)
    })()
</script>
<script src="chrome-extension://cdnlggbebabeoneeglhadpagegmflmbc/content/start_scriptBus.js"></script>
<script src="chrome-extension://cdnlggbebabeoneeglhadpagegmflmbc/content/scriptBus.js"></script>
<script></script>
<div id="eagle-drag-images" style="position: fixed; top: -100000px;"></div>
</body>
<div id="immersive-translate-popup" style="all: initial">
    <template shadowrootmode="open">
        <style>@charset "UTF-8";
        /*!
 * Pico.css v1.5.6 (https://picocss.com)
 * Copyright 2019-2022 - Licensed under MIT
 */
        /**
 * Theme: default
 */
        #mount {
            --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
            "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
            "Segoe UI Symbol", "Noto Color Emoji";
            --line-height: 1.5;
            --font-weight: 400;
            --font-size: 16px;
            --border-radius: 0.25rem;
            --border-width: 1px;
            --outline-width: 3px;
            --spacing: 1rem;
            --typography-spacing-vertical: 1.5rem;
            --block-spacing-vertical: calc(var(--spacing) * 2);
            --block-spacing-horizontal: var(--spacing);
            --grid-spacing-vertical: 0;
            --grid-spacing-horizontal: var(--spacing);
            --form-element-spacing-vertical: 0.75rem;
            --form-element-spacing-horizontal: 1rem;
            --nav-element-spacing-vertical: 1rem;
            --nav-element-spacing-horizontal: 0.5rem;
            --nav-link-spacing-vertical: 0.5rem;
            --nav-link-spacing-horizontal: 0.5rem;
            --form-label-font-weight: var(--font-weight);
            --transition: 0.2s ease-in-out;
            --modal-overlay-backdrop-filter: blur(0.25rem);
        }

        @media (min-width: 576px) {
            #mount {
                --font-size: 17px;
            }
        }

        @media (min-width: 768px) {
            #mount {
                --font-size: 18px;
            }
        }

        @media (min-width: 992px) {
            #mount {
                --font-size: 19px;
            }
        }

        @media (min-width: 1200px) {
            #mount {
                --font-size: 20px;
            }
        }

        @media (min-width: 576px) {
            #mount > header,
            #mount > main,
            #mount > footer,
            section {
                --block-spacing-vertical: calc(var(--spacing) * 2.5);
            }
        }

        @media (min-width: 768px) {
            #mount > header,
            #mount > main,
            #mount > footer,
            section {
                --block-spacing-vertical: calc(var(--spacing) * 3);
            }
        }

        @media (min-width: 992px) {
            #mount > header,
            #mount > main,
            #mount > footer,
            section {
                --block-spacing-vertical: calc(var(--spacing) * 3.5);
            }
        }

        @media (min-width: 1200px) {
            #mount > header,
            #mount > main,
            #mount > footer,
            section {
                --block-spacing-vertical: calc(var(--spacing) * 4);
            }
        }

        @media (min-width: 576px) {
            article {
                --block-spacing-horizontal: calc(var(--spacing) * 1.25);
            }
        }

        @media (min-width: 768px) {
            article {
                --block-spacing-horizontal: calc(var(--spacing) * 1.5);
            }
        }

        @media (min-width: 992px) {
            article {
                --block-spacing-horizontal: calc(var(--spacing) * 1.75);
            }
        }

        @media (min-width: 1200px) {
            article {
                --block-spacing-horizontal: calc(var(--spacing) * 2);
            }
        }

        dialog > article {
            --block-spacing-vertical: calc(var(--spacing) * 2);
            --block-spacing-horizontal: var(--spacing);
        }

        @media (min-width: 576px) {
            dialog > article {
                --block-spacing-vertical: calc(var(--spacing) * 2.5);
                --block-spacing-horizontal: calc(var(--spacing) * 1.25);
            }
        }

        @media (min-width: 768px) {
            dialog > article {
                --block-spacing-vertical: calc(var(--spacing) * 3);
                --block-spacing-horizontal: calc(var(--spacing) * 1.5);
            }
        }

        a {
            --text-decoration: none;
        }

        a.secondary,
        a.contrast {
            --text-decoration: underline;
        }

        small {
            --font-size: 0.875em;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            --font-weight: 700;
        }

        h1 {
            --font-size: 2rem;
            --typography-spacing-vertical: 3rem;
        }

        h2 {
            --font-size: 1.75rem;
            --typography-spacing-vertical: 2.625rem;
        }

        h3 {
            --font-size: 1.5rem;
            --typography-spacing-vertical: 2.25rem;
        }

        h4 {
            --font-size: 1.25rem;
            --typography-spacing-vertical: 1.874rem;
        }

        h5 {
            --font-size: 1.125rem;
            --typography-spacing-vertical: 1.6875rem;
        }

        [type="checkbox"],
        [type="radio"] {
            --border-width: 2px;
        }

        [type="checkbox"][role="switch"] {
            --border-width: 3px;
        }

        thead th,
        thead td,
        tfoot th,
        tfoot td {
            --border-width: 3px;
        }

        :not(thead, tfoot) > * > td {
            --font-size: 0.875em;
        }

        pre,
        code,
        kbd,
        samp {
            --font-family: "Menlo", "Consolas", "Roboto Mono", "Ubuntu Monospace",
            "Noto Mono", "Oxygen Mono", "Liberation Mono", monospace,
            "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        }

        kbd {
            --font-weight: bolder;
        }

        [data-theme="light"],
        #mount:not([data-theme="dark"]) {
            --background-color: #fff;
            --background-light-green: #F5F7F9;
            --color: hsl(205deg, 20%, 32%);
            --h1-color: hsl(205deg, 30%, 15%);
            --h2-color: #24333e;
            --h3-color: hsl(205deg, 25%, 23%);
            --h4-color: #374956;
            --h5-color: hsl(205deg, 20%, 32%);
            --h6-color: #4d606d;
            --muted-color: hsl(205deg, 10%, 50%);
            --muted-border-color: hsl(205deg, 20%, 94%);
            --primary: hsl(195deg, 85%, 41%);
            --primary-hover: hsl(195deg, 90%, 32%);
            --primary-focus: rgba(16, 149, 193, 0.125);
            --primary-inverse: #fff;
            --secondary: hsl(205deg, 15%, 41%);
            --secondary-hover: hsl(205deg, 20%, 32%);
            --secondary-focus: rgba(89, 107, 120, 0.125);
            --secondary-inverse: #fff;
            --contrast: hsl(205deg, 30%, 15%);
            --contrast-hover: #000;
            --contrast-focus: rgba(89, 107, 120, 0.125);
            --contrast-inverse: #fff;
            --mark-background-color: #fff2ca;
            --mark-color: #543a26;
            --ins-color: #388e3c;
            --del-color: #c62828;
            --blockquote-border-color: var(--muted-border-color);
            --blockquote-footer-color: var(--muted-color);
            --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
            --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
            --form-element-background-color: transparent;
            --form-element-border-color: hsl(205deg, 14%, 68%);
            --form-element-color: var(--color);
            --form-element-placeholder-color: var(--muted-color);
            --form-element-active-background-color: transparent;
            --form-element-active-border-color: var(--primary);
            --form-element-focus-color: var(--primary-focus);
            --form-element-disabled-background-color: hsl(205deg, 18%, 86%);
            --form-element-disabled-border-color: hsl(205deg, 14%, 68%);
            --form-element-disabled-opacity: 0.5;
            --form-element-invalid-border-color: #c62828;
            --form-element-invalid-active-border-color: #d32f2f;
            --form-element-invalid-focus-color: rgba(211, 47, 47, 0.125);
            --form-element-valid-border-color: #388e3c;
            --form-element-valid-active-border-color: #43a047;
            --form-element-valid-focus-color: rgba(67, 160, 71, 0.125);
            --switch-background-color: hsl(205deg, 16%, 77%);
            --switch-color: var(--primary-inverse);
            --switch-checked-background-color: var(--primary);
            --range-border-color: hsl(205deg, 18%, 86%);
            --range-active-border-color: hsl(205deg, 16%, 77%);
            --range-thumb-border-color: var(--background-color);
            --range-thumb-color: var(--secondary);
            --range-thumb-hover-color: var(--secondary-hover);
            --range-thumb-active-color: var(--primary);
            --table-border-color: var(--muted-border-color);
            --table-row-stripped-background-color: #f6f8f9;
            --code-background-color: hsl(205deg, 20%, 94%);
            --code-color: var(--muted-color);
            --code-kbd-background-color: var(--contrast);
            --code-kbd-color: var(--contrast-inverse);
            --code-tag-color: hsl(330deg, 40%, 50%);
            --code-property-color: hsl(185deg, 40%, 40%);
            --code-value-color: hsl(40deg, 20%, 50%);
            --code-comment-color: hsl(205deg, 14%, 68%);
            --accordion-border-color: var(--muted-border-color);
            --accordion-close-summary-color: var(--color);
            --accordion-open-summary-color: var(--muted-color);
            --card-background-color: var(--background-color);
            --card-border-color: var(--muted-border-color);
            --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(27, 40, 50, 0.01698),
            0.0335rem 0.067rem 0.402rem rgba(27, 40, 50, 0.024),
            0.0625rem 0.125rem 0.75rem rgba(27, 40, 50, 0.03),
            0.1125rem 0.225rem 1.35rem rgba(27, 40, 50, 0.036),
            0.2085rem 0.417rem 2.502rem rgba(27, 40, 50, 0.04302),
            0.5rem 1rem 6rem rgba(27, 40, 50, 0.06),
            0 0 0 0.0625rem rgba(27, 40, 50, 0.015);
            --card-sectionning-background-color: #fbfbfc;
            --dropdown-background-color: #fbfbfc;
            --dropdown-border-color: #e1e6eb;
            --dropdown-box-shadow: var(--card-box-shadow);
            --dropdown-color: var(--color);
            --dropdown-hover-background-color: hsl(205deg, 20%, 94%);
            --modal-overlay-background-color: rgba(213, 220, 226, 0.7);
            --progress-background-color: hsl(205deg, 18%, 86%);
            --progress-color: var(--primary);
            --loading-spinner-opacity: 0.5;
            --tooltip-background-color: var(--contrast);
            --tooltip-color: var(--contrast-inverse);
            --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
            --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
            --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(198, 40, 40)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
            --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
            --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
            --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(56, 142, 60)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
            --float-ball-more-button-border-color: #F6F6F6;
            --float-ball-more-button-background-color: #FCFCFC;
            --float-ball-more-button-svg-color: #6C6F73;
            color-scheme: light;
            --service-bg-hover: #F7FAFF;
        }

        @media only screen and (prefers-color-scheme: dark) {
            #mount:not([data-theme="light"]) {
                --background-color: #11191f;
                --background-light-green: #141e26;
                --color: hsl(205deg, 16%, 77%);
                --h1-color: hsl(205deg, 20%, 94%);
                --h2-color: #e1e6eb;
                --h3-color: hsl(205deg, 18%, 86%);
                --h4-color: #c8d1d8;
                --h5-color: hsl(205deg, 16%, 77%);
                --h6-color: #afbbc4;
                --muted-color: hsl(205deg, 10%, 50%);
                --muted-border-color: #1f2d38;
                --primary: hsl(195deg, 85%, 41%);
                --primary-hover: hsl(195deg, 80%, 50%);
                --primary-focus: rgba(16, 149, 193, 0.25);
                --primary-inverse: #fff;
                --secondary: hsl(205deg, 15%, 41%);
                --secondary-hover: hsl(205deg, 10%, 50%);
                --secondary-focus: rgba(115, 130, 140, 0.25);
                --secondary-inverse: #fff;
                --contrast: hsl(205deg, 20%, 94%);
                --contrast-hover: #fff;
                --contrast-focus: rgba(115, 130, 140, 0.25);
                --contrast-inverse: #000;
                --mark-background-color: #d1c284;
                --mark-color: #11191f;
                --ins-color: #388e3c;
                --del-color: #c62828;
                --blockquote-border-color: var(--muted-border-color);
                --blockquote-footer-color: var(--muted-color);
                --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
                --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
                --form-element-background-color: #11191f;
                --form-element-border-color: #374956;
                --form-element-color: var(--color);
                --form-element-placeholder-color: var(--muted-color);
                --form-element-active-background-color: var(
                        --form-element-background-color
                );
                --form-element-active-border-color: var(--primary);
                --form-element-focus-color: var(--primary-focus);
                --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
                --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
                --form-element-disabled-opacity: 0.5;
                --form-element-invalid-border-color: #b71c1c;
                --form-element-invalid-active-border-color: #c62828;
                --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
                --form-element-valid-border-color: #2e7d32;
                --form-element-valid-active-border-color: #388e3c;
                --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
                --switch-background-color: #374956;
                --switch-color: var(--primary-inverse);
                --switch-checked-background-color: var(--primary);
                --range-border-color: #24333e;
                --range-active-border-color: hsl(205deg, 25%, 23%);
                --range-thumb-border-color: var(--background-color);
                --range-thumb-color: var(--secondary);
                --range-thumb-hover-color: var(--secondary-hover);
                --range-thumb-active-color: var(--primary);
                --table-border-color: var(--muted-border-color);
                --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
                --code-background-color: #18232c;
                --code-color: var(--muted-color);
                --code-kbd-background-color: var(--contrast);
                --code-kbd-color: var(--contrast-inverse);
                --code-tag-color: hsl(330deg, 30%, 50%);
                --code-property-color: hsl(185deg, 30%, 50%);
                --code-value-color: hsl(40deg, 10%, 50%);
                --code-comment-color: #4d606d;
                --accordion-border-color: var(--muted-border-color);
                --accordion-active-summary-color: var(--primary);
                --accordion-close-summary-color: var(--color);
                --accordion-open-summary-color: var(--muted-color);
                --card-background-color: #141e26;
                --card-border-color: var(--card-background-color);
                --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
                0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
                0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
                0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
                0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
                0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
                --card-sectionning-background-color: #18232c;
                --dropdown-background-color: hsl(205deg, 30%, 15%);
                --dropdown-border-color: #24333e;
                --dropdown-box-shadow: var(--card-box-shadow);
                --dropdown-color: var(--color);
                --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
                --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
                --progress-background-color: #24333e;
                --progress-color: var(--primary);
                --loading-spinner-opacity: 0.5;
                --tooltip-background-color: var(--contrast);
                --tooltip-color: var(--contrast-inverse);
                --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
                --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
                --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
                --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
                --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
                --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
                --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
                --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
                --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
                --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
                --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
                --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
                color-scheme: dark;
                --service-bg-hover: #22292F;
            }
        }

        [data-theme="dark"] {
            --background-color: #11191f;
            --background-light-green: #141e26;
            --color: hsl(205deg, 16%, 77%);
            --h1-color: hsl(205deg, 20%, 94%);
            --h2-color: #e1e6eb;
            --h3-color: hsl(205deg, 18%, 86%);
            --h4-color: #c8d1d8;
            --h5-color: hsl(205deg, 16%, 77%);
            --h6-color: #afbbc4;
            --muted-color: hsl(205deg, 10%, 50%);
            --muted-border-color: #1f2d38;
            --primary: hsl(195deg, 85%, 41%);
            --primary-hover: hsl(195deg, 80%, 50%);
            --primary-focus: rgba(16, 149, 193, 0.25);
            --primary-inverse: #fff;
            --secondary: hsl(205deg, 15%, 41%);
            --secondary-hover: hsl(205deg, 10%, 50%);
            --secondary-focus: rgba(115, 130, 140, 0.25);
            --secondary-inverse: #fff;
            --contrast: hsl(205deg, 20%, 94%);
            --contrast-hover: #fff;
            --contrast-focus: rgba(115, 130, 140, 0.25);
            --contrast-inverse: #000;
            --mark-background-color: #d1c284;
            --mark-color: #11191f;
            --ins-color: #388e3c;
            --del-color: #c62828;
            --blockquote-border-color: var(--muted-border-color);
            --blockquote-footer-color: var(--muted-color);
            --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
            --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
            --form-element-background-color: #11191f;
            --form-element-border-color: #374956;
            --form-element-color: var(--color);
            --form-element-placeholder-color: var(--muted-color);
            --form-element-active-background-color: var(--form-element-background-color);
            --form-element-active-border-color: var(--primary);
            --form-element-focus-color: var(--primary-focus);
            --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
            --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
            --form-element-disabled-opacity: 0.5;
            --form-element-invalid-border-color: #b71c1c;
            --form-element-invalid-active-border-color: #c62828;
            --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
            --form-element-valid-border-color: #2e7d32;
            --form-element-valid-active-border-color: #388e3c;
            --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
            --switch-background-color: #374956;
            --switch-color: var(--primary-inverse);
            --switch-checked-background-color: var(--primary);
            --range-border-color: #24333e;
            --range-active-border-color: hsl(205deg, 25%, 23%);
            --range-thumb-border-color: var(--background-color);
            --range-thumb-color: var(--secondary);
            --range-thumb-hover-color: var(--secondary-hover);
            --range-thumb-active-color: var(--primary);
            --table-border-color: var(--muted-border-color);
            --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
            --code-background-color: #18232c;
            --code-color: var(--muted-color);
            --code-kbd-background-color: var(--contrast);
            --code-kbd-color: var(--contrast-inverse);
            --code-tag-color: hsl(330deg, 30%, 50%);
            --code-property-color: hsl(185deg, 30%, 50%);
            --code-value-color: hsl(40deg, 10%, 50%);
            --code-comment-color: #4d606d;
            --accordion-border-color: var(--muted-border-color);
            --accordion-active-summary-color: var(--primary);
            --accordion-close-summary-color: var(--color);
            --accordion-open-summary-color: var(--muted-color);
            --card-background-color: #141e26;
            --card-border-color: var(--card-background-color);
            --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
            0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
            0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
            0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
            0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
            0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
            --card-sectionning-background-color: #18232c;
            --dropdown-background-color: hsl(205deg, 30%, 15%);
            --dropdown-border-color: #24333e;
            --dropdown-box-shadow: var(--card-box-shadow);
            --dropdown-color: var(--color);
            --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
            --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
            --progress-background-color: #24333e;
            --progress-color: var(--primary);
            --loading-spinner-opacity: 0.5;
            --tooltip-background-color: var(--contrast);
            --tooltip-color: var(--contrast-inverse);
            --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
            --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
            --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
            --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
            --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
            --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
            --icon-share: url("data:image/svg+xml;charset=utf-8;base64,PHN2ZyB3aWR0aD0nMjInIGhlaWdodD0nMjInIHZpZXdCb3g9JzAgMCAyMiAyMicgZmlsbD0nbm9uZScgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJz48cGF0aCBkPSdNMTcuOTM0OCA3LjY0ODQ0QzE5Ljg5NDEgNy42NDg0NCAyMS40ODU1IDYuMDU0NjkgMjEuNDg1NSA0LjA5NzY2QzIxLjQ4NTUgMi4xNDA2MiAxOS44OTE4IDAuNTQ2ODc1IDE3LjkzNDggMC41NDY4NzVDMTUuOTc1NCAwLjU0Njg3NSAxNC4zODQgMi4xNDA2MiAxNC4zODQgNC4wOTc2NkMxNC4zODQgNC4yOTkyMiAxNC40MDA0IDQuNDkzNzUgMTQuNDMzMiA0LjY4NTk0TDYuMzIzODMgOC4zNTM5MUM1LjcwOTc3IDcuODQ1MzEgNC45MjIyNyA3LjU0MDYyIDQuMDY0NDUgNy41NDA2MkMyLjEwNTA4IDcuNTQwNjIgMC41MTM2NzIgOS4xMzQzOCAwLjUxMzY3MiAxMS4wOTE0QzAuNTEzNjcyIDEzLjA0ODQgMi4xMDc0MiAxNC42NDIyIDQuMDY0NDUgMTQuNjQyMkM0LjgzMzIgMTQuNjQyMiA1LjU0NTcgMTQuMzk2MSA2LjEyNjk1IDEzLjk4MTNMMTEuNDk0MSAxNi45OTUzQzExLjQxNjggMTcuMjg1OSAxMS4zNzcgMTcuNTg4MyAxMS4zNzcgMTcuOTAyM0MxMS4zNzcgMTkuODYxNyAxMi45NzA3IDIxLjQ1MzEgMTQuOTI3NyAyMS40NTMxQzE2Ljg4NzEgMjEuNDUzMSAxOC40Nzg1IDE5Ljg1OTQgMTguNDc4NSAxNy45MDIzQzE4LjQ3ODUgMTUuOTQzIDE2Ljg4NDggMTQuMzUxNiAxNC45Mjc3IDE0LjM1MTZDMTMuOTU3NCAxNC4zNTE2IDEzLjA3ODUgMTQuNzQzIDEyLjQzNjMgMTUuMzczNEw3LjMyMjI3IDEyLjUwNDdDNy41MDk3NyAxMi4wNzExIDcuNjE1MjMgMTEuNTk1MyA3LjYxNTIzIDExLjA5MzhDNy42MTUyMyAxMC42ODEyIDcuNTQ0OTIgMTAuMjg3NSA3LjQxNjAyIDkuOTE5NTNMMTUuMjIzIDYuMzg3NUMxNS44NzQ2IDcuMTU2MjUgMTYuODQ5NiA3LjY0ODQ0IDE3LjkzNDggNy42NDg0NFpNNC4wNjQ0NSAxMi43Njk1QzMuMTQxMDIgMTIuNzY5NSAyLjM4ODY3IDEyLjAxNzIgMi4zODg2NyAxMS4wOTM4QzIuMzg4NjcgMTAuMTcwMyAzLjE0MTAyIDkuNDE3OTcgNC4wNjQ0NSA5LjQxNzk3QzQuOTg3ODkgOS40MTc5NyA1Ljc0MDIzIDEwLjE3MDMgNS43NDAyMyAxMS4wOTM4QzUuNzQwMjMgMTIuMDE3MiA0Ljk4Nzg5IDEyLjc2OTUgNC4wNjQ0NSAxMi43Njk1Wk0xNC45Mjc3IDE2LjIyNjZDMTUuODUxMiAxNi4yMjY2IDE2LjYwMzUgMTYuOTc4OSAxNi42MDM1IDE3LjkwMjNDMTYuNjAzNSAxOC44MjU4IDE1Ljg1MTIgMTkuNTc4MSAxNC45Mjc3IDE5LjU3ODFDMTQuMDA0MyAxOS41NzgxIDEzLjI1MiAxOC44MjU4IDEzLjI1MiAxNy45MDIzQzEzLjI1MiAxNi45Nzg5IDE0LjAwMiAxNi4yMjY2IDE0LjkyNzcgMTYuMjI2NlpNMTcuOTM0OCAyLjQxOTUzQzE4Ljg1ODIgMi40MTk1MyAxOS42MTA1IDMuMTcxODcgMTkuNjEwNSA0LjA5NTMxQzE5LjYxMDUgNS4wMTg3NSAxOC44NTgyIDUuNzcxMDkgMTcuOTM0OCA1Ljc3MTA5QzE3LjAxMTMgNS43NzEwOSAxNi4yNTkgNS4wMTg3NSAxNi4yNTkgNC4wOTUzMUMxNi4yNTkgMy4xNzE4NyAxNy4wMTEzIDIuNDE5NTMgMTcuOTM0OCAyLjQxOTUzWicgZmlsbD0nI0I2QjZCNicvPjwvc3ZnPiA=");
            color-scheme: dark;
        }

        progress,
        [type="checkbox"],
        [type="radio"],
        [type="range"] {
            accent-color: var(--primary);
        }

        /**
 * Document
 * Content-box & Responsive typography
 */
        *,
        *::before,
        *::after {
            box-sizing: border-box;
            background-repeat: no-repeat;
        }

        ::before,
        ::after {
            text-decoration: inherit;
            vertical-align: inherit;
        }

        :where(#mount) {
            -webkit-tap-highlight-color: transparent;
            -webkit-text-size-adjust: 100%;
            -moz-text-size-adjust: 100%;
            text-size-adjust: 100%;
            background-color: var(--background-color);
            color: var(--color);
            font-weight: var(--font-weight);
            font-size: var(--font-size);
            line-height: var(--line-height);
            font-family: var(--font-family);
            text-rendering: optimizeLegibility;
            overflow-wrap: break-word;
            cursor: default;
            -moz-tab-size: 4;
            -o-tab-size: 4;
            tab-size: 4;
        }

        /**
 * Sectioning
 * Container and responsive spacings for header, main, footer
 */
        main {
            display: block;
        }

        #mount {
            width: 100%;
            margin: 0;
        }

        #mount > header,
        #mount > main,
        #mount > footer {
            width: 100%;
            margin-right: auto;
            margin-left: auto;
            padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
        }

        @media (min-width: 576px) {
            #mount > header,
            #mount > main,
            #mount > footer {
                max-width: 510px;
                padding-right: 0;
                padding-left: 0;
            }
        }

        @media (min-width: 768px) {
            #mount > header,
            #mount > main,
            #mount > footer {
                max-width: 700px;
            }
        }

        @media (min-width: 992px) {
            #mount > header,
            #mount > main,
            #mount > footer {
                max-width: 920px;
            }
        }

        @media (min-width: 1200px) {
            #mount > header,
            #mount > main,
            #mount > footer {
                max-width: 1130px;
            }
        }

        /**
* Container
*/
        .container,
        .container-fluid {
            width: 100%;
            margin-right: auto;
            margin-left: auto;
            padding-right: var(--spacing);
            padding-left: var(--spacing);
        }

        @media (min-width: 576px) {
            .container {
                max-width: 510px;
                padding-right: 0;
                padding-left: 0;
            }
        }

        @media (min-width: 768px) {
            .container {
                max-width: 700px;
            }
        }

        @media (min-width: 992px) {
            .container {
                max-width: 920px;
            }
        }

        @media (min-width: 1200px) {
            .container {
                max-width: 1130px;
            }
        }

        /**
 * Section
 * Responsive spacings for section
 */
        section {
            margin-bottom: var(--block-spacing-vertical);
        }

        /**
* Grid
* Minimal grid system with auto-layout columns
*/
        .grid {
            grid-column-gap: var(--grid-spacing-horizontal);
            grid-row-gap: var(--grid-spacing-vertical);
            display: grid;
            grid-template-columns: 1fr;
            margin: 0;
        }

        @media (min-width: 992px) {
            .grid {
                grid-template-columns: repeat(auto-fit, minmax(0%, 1fr));
            }
        }

        .grid > * {
            min-width: 0;
        }

        /**
 * Horizontal scroller (<figure>)
 */
        figure {
            display: block;
            margin: 0;
            padding: 0;
            overflow-x: auto;
        }

        figure figcaption {
            padding: calc(var(--spacing) * 0.5) 0;
            color: var(--muted-color);
        }

        /**
 * Typography
 */
        b,
        strong {
            font-weight: bolder;
        }

        sub,
        sup {
            position: relative;
            font-size: 0.75em;
            line-height: 0;
            vertical-align: baseline;
        }

        sub {
            bottom: -0.25em;
        }

        sup {
            top: -0.5em;
        }

        address,
        blockquote,
        dl,
        figure,
        form,
        ol,
        p,
        pre,
        table,
        ul {
            margin-top: 0;
            margin-bottom: var(--typography-spacing-vertical);
            color: var(--color);
            font-style: normal;
            font-weight: var(--font-weight);
            font-size: var(--font-size);
        }

        a,
        [role="link"] {
            --color: var(--primary);
            --background-color: transparent;
            outline: none;
            background-color: var(--background-color);
            color: var(--color);
            -webkit-text-decoration: var(--text-decoration);
            text-decoration: var(--text-decoration);
            transition: background-color var(--transition), color var(--transition),
            box-shadow var(--transition), -webkit-text-decoration var(--transition);
            transition: background-color var(--transition), color var(--transition),
            text-decoration var(--transition), box-shadow var(--transition);
            transition: background-color var(--transition), color var(--transition),
            text-decoration var(--transition), box-shadow var(--transition),
            -webkit-text-decoration var(--transition);
        }

        a:is([aria-current], :hover, :active, :focus),
        [role="link"]:is([aria-current], :hover, :active, :focus) {
            --color: var(--primary-hover);
            --text-decoration: underline;
        }

        a:focus,
        [role="link"]:focus {
            --background-color: var(--primary-focus);
        }

        a.secondary,
        [role="link"].secondary {
            --color: var(--secondary);
        }

        a.secondary:is([aria-current], :hover, :active, :focus),
        [role="link"].secondary:is([aria-current], :hover, :active, :focus) {
            --color: var(--secondary-hover);
        }

        a.secondary:focus,
        [role="link"].secondary:focus {
            --background-color: var(--secondary-focus);
        }

        a.contrast,
        [role="link"].contrast {
            --color: var(--contrast);
        }

        a.contrast:is([aria-current], :hover, :active, :focus),
        [role="link"].contrast:is([aria-current], :hover, :active, :focus) {
            --color: var(--contrast-hover);
        }

        a.contrast:focus,
        [role="link"].contrast:focus {
            --background-color: var(--contrast-focus);
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-top: 0;
            margin-bottom: var(--typography-spacing-vertical);
            color: var(--color);
            font-weight: var(--font-weight);
            font-size: var(--font-size);
            font-family: var(--font-family);
        }

        h1 {
            --color: var(--h1-color);
        }

        h2 {
            --color: var(--h2-color);
        }

        h3 {
            --color: var(--h3-color);
        }

        h4 {
            --color: var(--h4-color);
        }

        h5 {
            --color: var(--h5-color);
        }

        h6 {
            --color: var(--h6-color);
        }

        :where(address, blockquote, dl, figure, form, ol, p, pre, table, ul)
        ~ :is(h1, h2, h3, h4, h5, h6) {
            margin-top: var(--typography-spacing-vertical);
        }

        hgroup,
        .headings {
            margin-bottom: var(--typography-spacing-vertical);
        }

        hgroup > *,
        .headings > * {
            margin-bottom: 0;
        }

        hgroup > *:last-child,
        .headings > *:last-child {
            --color: var(--muted-color);
            --font-weight: unset;
            font-size: 1rem;
            font-family: unset;
        }

        p {
            margin-bottom: var(--typography-spacing-vertical);
        }

        small {
            font-size: var(--font-size);
        }

        :where(dl, ol, ul) {
            padding-right: 0;
            padding-left: var(--spacing);
            -webkit-padding-start: var(--spacing);
            padding-inline-start: var(--spacing);
            -webkit-padding-end: 0;
            padding-inline-end: 0;
        }

        :where(dl, ol, ul) li {
            margin-bottom: calc(var(--typography-spacing-vertical) * 0.25);
        }

        :where(dl, ol, ul) :is(dl, ol, ul) {
            margin: 0;
            margin-top: calc(var(--typography-spacing-vertical) * 0.25);
        }

        ul li {
            list-style: square;
        }

        mark {
            padding: 0.125rem 0.25rem;
            background-color: var(--mark-background-color);
            color: var(--mark-color);
            vertical-align: baseline;
        }

        blockquote {
            display: block;
            margin: var(--typography-spacing-vertical) 0;
            padding: var(--spacing);
            border-right: none;
            border-left: 0.25rem solid var(--blockquote-border-color);
            -webkit-border-start: 0.25rem solid var(--blockquote-border-color);
            border-inline-start: 0.25rem solid var(--blockquote-border-color);
            -webkit-border-end: none;
            border-inline-end: none;
        }

        blockquote footer {
            margin-top: calc(var(--typography-spacing-vertical) * 0.5);
            color: var(--blockquote-footer-color);
        }

        abbr[title] {
            border-bottom: 1px dotted;
            text-decoration: none;
            cursor: help;
        }

        ins {
            color: var(--ins-color);
            text-decoration: none;
        }

        del {
            color: var(--del-color);
        }

        ::-moz-selection {
            background-color: var(--primary-focus);
        }

        ::selection {
            background-color: var(--primary-focus);
        }

        /**
 * Embedded content
 */
        :where(audio, canvas, iframe, img, svg, video) {
            vertical-align: middle;
        }

        audio,
        video {
            display: inline-block;
        }

        audio:not([controls]) {
            display: none;
            height: 0;
        }

        :where(iframe) {
            border-style: none;
        }

        img {
            max-width: 100%;
            height: auto;
            border-style: none;
        }

        :where(svg:not([fill])) {
            fill: currentColor;
        }

        svg:not(#mount) {
            overflow: hidden;
        }

        /**
 * Button
 */
        button {
            margin: 0;
            overflow: visible;
            font-family: inherit;
            text-transform: none;
        }

        button,
        [type="button"],
        [type="reset"],
        [type="submit"] {
            -webkit-appearance: button;
        }

        button {
            display: block;
            width: 100%;
            margin-bottom: var(--spacing);
        }

        [role="button"] {
            display: inline-block;
            text-decoration: none;
        }

        button,
        input[type="submit"],
        input[type="button"],
        input[type="reset"],
        [role="button"] {
            --background-color: var(--primary);
            --border-color: var(--primary);
            --color: var(--primary-inverse);
            --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
            padding: var(--form-element-spacing-vertical) var(--form-element-spacing-horizontal);
            border: var(--border-width) solid var(--border-color);
            border-radius: var(--border-radius);
            outline: none;
            background-color: var(--background-color);
            box-shadow: var(--box-shadow);
            color: var(--color);
            font-weight: var(--font-weight);
            font-size: 1rem;
            line-height: var(--line-height);
            text-align: center;
            cursor: pointer;
            transition: background-color var(--transition), border-color var(--transition),
            color var(--transition), box-shadow var(--transition);
        }

        button:is([aria-current], :hover, :active, :focus),
        input[type="submit"]:is([aria-current], :hover, :active, :focus),
        input[type="button"]:is([aria-current], :hover, :active, :focus),
        input[type="reset"]:is([aria-current], :hover, :active, :focus),
        [role="button"]:is([aria-current], :hover, :active, :focus) {
            --background-color: var(--primary-hover);
            --border-color: var(--primary-hover);
            --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
            --color: var(--primary-inverse);
        }

        button:focus,
        input[type="submit"]:focus,
        input[type="button"]:focus,
        input[type="reset"]:focus,
        [role="button"]:focus {
            --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
            0 0 0 var(--outline-width) var(--primary-focus);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary,
        input[type="reset"] {
            --background-color: var(--secondary);
            --border-color: var(--secondary);
            --color: var(--secondary-inverse);
            cursor: pointer;
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:is([aria-current], :hover, :active, :focus),
        input[type="reset"]:is([aria-current], :hover, :active, :focus) {
            --background-color: var(--secondary-hover);
            --border-color: var(--secondary-hover);
            --color: var(--secondary-inverse);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:focus,
        input[type="reset"]:focus {
            --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
            0 0 0 var(--outline-width) var(--secondary-focus);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast {
            --background-color: var(--contrast);
            --border-color: var(--contrast);
            --color: var(--contrast-inverse);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:is([aria-current], :hover, :active, :focus) {
            --background-color: var(--contrast-hover);
            --border-color: var(--contrast-hover);
            --color: var(--contrast-inverse);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:focus {
            --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
            0 0 0 var(--outline-width) var(--contrast-focus);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline,
        input[type="reset"].outline {
            --background-color: transparent;
            --color: var(--primary);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline:is([aria-current], :hover, :active, :focus),
        input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
            --background-color: transparent;
            --color: var(--primary-hover);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary,
        input[type="reset"].outline {
            --color: var(--secondary);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary:is([aria-current], :hover, :active, :focus),
        input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
            --color: var(--secondary-hover);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast {
            --color: var(--contrast);
        }

        :is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast:is([aria-current], :hover, :active, :focus) {
            --color: var(--contrast-hover);
        }

        :where(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  )[disabled],
        :where(fieldset[disabled])
        :is(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  ),
        a[role="button"]:not([href]) {
            opacity: 0.5;
            pointer-events: none;
        }

        /**
 * Form elements
 */
        input,
        optgroup,
        select,
        textarea {
            margin: 0;
            font-size: 1rem;
            line-height: var(--line-height);
            font-family: inherit;
            letter-spacing: inherit;
        }

        input {
            overflow: visible;
        }

        select {
            text-transform: none;
        }

        legend {
            max-width: 100%;
            padding: 0;
            color: inherit;
            white-space: normal;
        }

        textarea {
            overflow: auto;
        }

        [type="checkbox"],
        [type="radio"] {
            padding: 0;
        }

        ::-webkit-inner-spin-button,
        ::-webkit-outer-spin-button {
            height: auto;
        }

        [type="search"] {
            -webkit-appearance: textfield;
            outline-offset: -2px;
        }

        [type="search"]::-webkit-search-decoration {
            -webkit-appearance: none;
        }

        ::-webkit-file-upload-button {
            -webkit-appearance: button;
            font: inherit;
        }

        ::-moz-focus-inner {
            padding: 0;
            border-style: none;
        }

        :-moz-focusring {
            outline: none;
        }

        :-moz-ui-invalid {
            box-shadow: none;
        }

        ::-ms-expand {
            display: none;
        }

        [type="file"],
        [type="range"] {
            padding: 0;
            border-width: 0;
        }

        input:not([type="checkbox"], [type="radio"], [type="range"]) {
            height: calc(
                    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
                    var(--border-width) * 2
            );
        }

        fieldset {
            margin: 0;
            margin-bottom: var(--spacing);
            padding: 0;
            border: 0;
        }

        label,
        fieldset legend {
            display: block;
            margin-bottom: calc(var(--spacing) * 0.25);
            font-weight: var(--form-label-font-weight, var(--font-weight));
        }

        input:not([type="checkbox"], [type="radio"]),
        select,
        textarea {
            width: 100%;
        }

        input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]),
        select,
        textarea {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            padding: var(--form-element-spacing-vertical) var(--form-element-spacing-horizontal);
        }

        input,
        select,
        textarea {
            --background-color: var(--form-element-background-color);
            --border-color: var(--form-element-border-color);
            --color: var(--form-element-color);
            --box-shadow: none;
            border: var(--border-width) solid var(--border-color);
            border-radius: var(--border-radius);
            outline: none;
            background-color: var(--background-color);
            box-shadow: var(--box-shadow);
            color: var(--color);
            font-weight: var(--font-weight);
            transition: background-color var(--transition), border-color var(--transition),
            color var(--transition), box-shadow var(--transition);
        }

        input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="checkbox"],
    [type="radio"],
    [readonly]
  ):is(:active, :focus),
        :where(select, textarea):is(:active, :focus) {
            --background-color: var(--form-element-active-background-color);
        }

        input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="switch"],
    [readonly]
  ):is(:active, :focus),
        :where(select, textarea):is(:active, :focus) {
            --border-color: var(--form-element-active-border-color);
        }

        input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="range"],
    [type="file"],
    [readonly]
  ):focus,
        select:focus,
        textarea:focus {
            --box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
        }

        input:not([type="submit"], [type="button"], [type="reset"])[disabled],
        select[disabled],
        textarea[disabled],
        :where(fieldset[disabled])
        :is(
    input:not([type="submit"], [type="button"], [type="reset"]),
    select,
    textarea
  ) {
            --background-color: var(--form-element-disabled-background-color);
            --border-color: var(--form-element-disabled-border-color);
            opacity: var(--form-element-disabled-opacity);
            pointer-events: none;
        }

        :where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid] {
            padding-right: calc(
                    var(--form-element-spacing-horizontal) + 1.5rem
            ) !important;
            padding-left: var(--form-element-spacing-horizontal);
            -webkit-padding-start: var(--form-element-spacing-horizontal) !important;
            padding-inline-start: var(--form-element-spacing-horizontal) !important;
            -webkit-padding-end: calc(
                    var(--form-element-spacing-horizontal) + 1.5rem
            ) !important;
            padding-inline-end: calc(
                    var(--form-element-spacing-horizontal) + 1.5rem
            ) !important;
            background-position: center right 0.75rem;
            background-size: 1rem auto;
            background-repeat: no-repeat;
        }

        :where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="false"] {
            background-image: var(--icon-valid);
        }

        :where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="true"] {
            background-image: var(--icon-invalid);
        }

        :where(input, select, textarea)[aria-invalid="false"] {
            --border-color: var(--form-element-valid-border-color);
        }

        :where(input, select, textarea)[aria-invalid="false"]:is(:active, :focus) {
            --border-color: var(--form-element-valid-active-border-color) !important;
            --box-shadow: 0 0 0 var(--outline-width) var(--form-element-valid-focus-color) !important;
        }

        :where(input, select, textarea)[aria-invalid="true"] {
            --border-color: var(--form-element-invalid-border-color);
        }

        :where(input, select, textarea)[aria-invalid="true"]:is(:active, :focus) {
            --border-color: var(--form-element-invalid-active-border-color) !important;
            --box-shadow: 0 0 0 var(--outline-width) var(--form-element-invalid-focus-color) !important;
        }

        [dir="rtl"]
        :where(input, select, textarea):not([type="checkbox"], [type="radio"]):is(
    [aria-invalid],
    [aria-invalid="true"],
    [aria-invalid="false"]
  ) {
            background-position: center left 0.75rem;
        }

        input::placeholder,
        input::-webkit-input-placeholder,
        textarea::placeholder,
        textarea::-webkit-input-placeholder,
        select:invalid {
            color: var(--form-element-placeholder-color);
            opacity: 1;
        }

        input:not([type="checkbox"], [type="radio"]),
        select,
        textarea {
            margin-bottom: var(--spacing);
        }

        select::-ms-expand {
            border: 0;
            background-color: transparent;
        }

        select:not([multiple], [size]) {
            padding-right: calc(var(--form-element-spacing-horizontal) + 1.5rem);
            padding-left: var(--form-element-spacing-horizontal);
            -webkit-padding-start: var(--form-element-spacing-horizontal);
            padding-inline-start: var(--form-element-spacing-horizontal);
            -webkit-padding-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
            padding-inline-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
            background-image: var(--icon-chevron);
            background-position: center right 0.75rem;
            background-size: 1rem auto;
            background-repeat: no-repeat;
        }

        [dir="rtl"] select:not([multiple], [size]) {
            background-position: center left 0.75rem;
        }

        :where(input, select, textarea) + small {
            display: block;
            width: 100%;
            margin-top: calc(var(--spacing) * -0.75);
            margin-bottom: var(--spacing);
            color: var(--muted-color);
        }

        label > :where(input, select, textarea) {
            margin-top: calc(var(--spacing) * 0.25);
        }

        /**
 * Form elements
 * Checkboxes & Radios
 */
        [type="checkbox"],
        [type="radio"] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 1.25em;
            height: 1.25em;
            margin-top: -0.125em;
            margin-right: 0.375em;
            margin-left: 0;
            -webkit-margin-start: 0;
            margin-inline-start: 0;
            -webkit-margin-end: 0.375em;
            margin-inline-end: 0.375em;
            border-width: var(--border-width);
            font-size: inherit;
            vertical-align: middle;
            cursor: pointer;
        }

        [type="checkbox"]::-ms-check,
        [type="radio"]::-ms-check {
            display: none;
        }

        [type="checkbox"]:checked,
        [type="checkbox"]:checked:active,
        [type="checkbox"]:checked:focus,
        [type="radio"]:checked,
        [type="radio"]:checked:active,
        [type="radio"]:checked:focus {
            --background-color: var(--primary);
            --border-color: var(--primary);
            background-image: var(--icon-checkbox);
            background-position: center;
            background-size: 0.75em auto;
            background-repeat: no-repeat;
        }

        [type="checkbox"] ~ label,
        [type="radio"] ~ label {
            display: inline-block;
            margin-right: 0.375em;
            margin-bottom: 0;
            cursor: pointer;
        }

        [type="checkbox"]:indeterminate {
            --background-color: var(--primary);
            --border-color: var(--primary);
            background-image: var(--icon-minus);
            background-position: center;
            background-size: 0.75em auto;
            background-repeat: no-repeat;
        }

        [type="radio"] {
            border-radius: 50%;
        }

        [type="radio"]:checked,
        [type="radio"]:checked:active,
        [type="radio"]:checked:focus {
            --background-color: var(--primary-inverse);
            border-width: 0.35em;
            background-image: none;
        }

        [type="checkbox"][role="switch"] {
            --background-color: var(--switch-background-color);
            --border-color: var(--switch-background-color);
            --color: var(--switch-color);
            width: 2.25em;
            height: 1.25em;
            border: var(--border-width) solid var(--border-color);
            border-radius: 1.25em;
            background-color: var(--background-color);
            line-height: 1.25em;
        }

        [type="checkbox"][role="switch"]:focus {
            --background-color: var(--switch-background-color);
            --border-color: var(--switch-background-color);
        }

        [type="checkbox"][role="switch"]:checked {
            --background-color: var(--switch-checked-background-color);
            --border-color: var(--switch-checked-background-color);
        }

        [type="checkbox"][role="switch"]:before {
            display: block;
            width: calc(1.25em - (var(--border-width) * 2));
            height: 100%;
            border-radius: 50%;
            background-color: var(--color);
            content: "";
            transition: margin 0.1s ease-in-out;
        }

        [type="checkbox"][role="switch"]:checked {
            background-image: none;
        }

        [type="checkbox"][role="switch"]:checked::before {
            margin-left: calc(1.125em - var(--border-width));
            -webkit-margin-start: calc(1.125em - var(--border-width));
            margin-inline-start: calc(1.125em - var(--border-width));
        }

        [type="checkbox"][aria-invalid="false"],
        [type="checkbox"]:checked[aria-invalid="false"],
        [type="radio"][aria-invalid="false"],
        [type="radio"]:checked[aria-invalid="false"],
        [type="checkbox"][role="switch"][aria-invalid="false"],
        [type="checkbox"][role="switch"]:checked[aria-invalid="false"] {
            --border-color: var(--form-element-valid-border-color);
        }

        [type="checkbox"][aria-invalid="true"],
        [type="checkbox"]:checked[aria-invalid="true"],
        [type="radio"][aria-invalid="true"],
        [type="radio"]:checked[aria-invalid="true"],
        [type="checkbox"][role="switch"][aria-invalid="true"],
        [type="checkbox"][role="switch"]:checked[aria-invalid="true"] {
            --border-color: var(--form-element-invalid-border-color);
        }

        /**
 * Form elements
 * Alternatives input types (Not Checkboxes & Radios)
 */
        [type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
        }

        [type="color"]::-moz-focus-inner {
            padding: 0;
        }

        [type="color"]::-webkit-color-swatch {
            border: 0;
            border-radius: calc(var(--border-radius) * 0.5);
        }

        [type="color"]::-moz-color-swatch {
            border: 0;
            border-radius: calc(var(--border-radius) * 0.5);
        }

        input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]):is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
            --icon-position: 0.75rem;
            --icon-width: 1rem;
            padding-right: calc(var(--icon-width) + var(--icon-position));
            background-image: var(--icon-date);
            background-position: center right var(--icon-position);
            background-size: var(--icon-width) auto;
            background-repeat: no-repeat;
        }

        input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="time"] {
            background-image: var(--icon-time);
        }

        [type="date"]::-webkit-calendar-picker-indicator,
        [type="datetime-local"]::-webkit-calendar-picker-indicator,
        [type="month"]::-webkit-calendar-picker-indicator,
        [type="time"]::-webkit-calendar-picker-indicator,
        [type="week"]::-webkit-calendar-picker-indicator {
            width: var(--icon-width);
            margin-right: calc(var(--icon-width) * -1);
            margin-left: var(--icon-position);
            opacity: 0;
        }

        [dir="rtl"]
        :is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
            text-align: right;
        }

        [type="file"] {
            --color: var(--muted-color);
            padding: calc(var(--form-element-spacing-vertical) * 0.5) 0;
            border: 0;
            border-radius: 0;
            background: none;
        }

        [type="file"]::file-selector-button {
            --background-color: var(--secondary);
            --border-color: var(--secondary);
            --color: var(--secondary-inverse);
            margin-right: calc(var(--spacing) / 2);
            margin-left: 0;
            -webkit-margin-start: 0;
            margin-inline-start: 0;
            -webkit-margin-end: calc(var(--spacing) / 2);
            margin-inline-end: calc(var(--spacing) / 2);
            padding: calc(var(--form-element-spacing-vertical) * 0.5) calc(var(--form-element-spacing-horizontal) * 0.5);
            border: var(--border-width) solid var(--border-color);
            border-radius: var(--border-radius);
            outline: none;
            background-color: var(--background-color);
            box-shadow: var(--box-shadow);
            color: var(--color);
            font-weight: var(--font-weight);
            font-size: 1rem;
            line-height: var(--line-height);
            text-align: center;
            cursor: pointer;
            transition: background-color var(--transition), border-color var(--transition),
            color var(--transition), box-shadow var(--transition);
        }

        [type="file"]::file-selector-button:is(:hover, :active, :focus) {
            --background-color: var(--secondary-hover);
            --border-color: var(--secondary-hover);
        }

        [type="file"]::-webkit-file-upload-button {
            --background-color: var(--secondary);
            --border-color: var(--secondary);
            --color: var(--secondary-inverse);
            margin-right: calc(var(--spacing) / 2);
            margin-left: 0;
            -webkit-margin-start: 0;
            margin-inline-start: 0;
            -webkit-margin-end: calc(var(--spacing) / 2);
            margin-inline-end: calc(var(--spacing) / 2);
            padding: calc(var(--form-element-spacing-vertical) * 0.5) calc(var(--form-element-spacing-horizontal) * 0.5);
            border: var(--border-width) solid var(--border-color);
            border-radius: var(--border-radius);
            outline: none;
            background-color: var(--background-color);
            box-shadow: var(--box-shadow);
            color: var(--color);
            font-weight: var(--font-weight);
            font-size: 1rem;
            line-height: var(--line-height);
            text-align: center;
            cursor: pointer;
            -webkit-transition: background-color var(--transition),
            border-color var(--transition), color var(--transition),
            box-shadow var(--transition);
            transition: background-color var(--transition), border-color var(--transition),
            color var(--transition), box-shadow var(--transition);
        }

        [type="file"]::-webkit-file-upload-button:is(:hover, :active, :focus) {
            --background-color: var(--secondary-hover);
            --border-color: var(--secondary-hover);
        }

        [type="file"]::-ms-browse {
            --background-color: var(--secondary);
            --border-color: var(--secondary);
            --color: var(--secondary-inverse);
            margin-right: calc(var(--spacing) / 2);
            margin-left: 0;
            margin-inline-start: 0;
            margin-inline-end: calc(var(--spacing) / 2);
            padding: calc(var(--form-element-spacing-vertical) * 0.5) calc(var(--form-element-spacing-horizontal) * 0.5);
            border: var(--border-width) solid var(--border-color);
            border-radius: var(--border-radius);
            outline: none;
            background-color: var(--background-color);
            box-shadow: var(--box-shadow);
            color: var(--color);
            font-weight: var(--font-weight);
            font-size: 1rem;
            line-height: var(--line-height);
            text-align: center;
            cursor: pointer;
            -ms-transition: background-color var(--transition),
            border-color var(--transition), color var(--transition),
            box-shadow var(--transition);
            transition: background-color var(--transition), border-color var(--transition),
            color var(--transition), box-shadow var(--transition);
        }

        [type="file"]::-ms-browse:is(:hover, :active, :focus) {
            --background-color: var(--secondary-hover);
            --border-color: var(--secondary-hover);
        }

        [type="range"] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 100%;
            height: 1.25rem;
            background: none;
        }

        [type="range"]::-webkit-slider-runnable-track {
            width: 100%;
            height: 0.25rem;
            border-radius: var(--border-radius);
            background-color: var(--range-border-color);
            -webkit-transition: background-color var(--transition),
            box-shadow var(--transition);
            transition: background-color var(--transition), box-shadow var(--transition);
        }

        [type="range"]::-moz-range-track {
            width: 100%;
            height: 0.25rem;
            border-radius: var(--border-radius);
            background-color: var(--range-border-color);
            -moz-transition: background-color var(--transition),
            box-shadow var(--transition);
            transition: background-color var(--transition), box-shadow var(--transition);
        }

        [type="range"]::-ms-track {
            width: 100%;
            height: 0.25rem;
            border-radius: var(--border-radius);
            background-color: var(--range-border-color);
            -ms-transition: background-color var(--transition),
            box-shadow var(--transition);
            transition: background-color var(--transition), box-shadow var(--transition);
        }

        [type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 1.25rem;
            height: 1.25rem;
            margin-top: -0.5rem;
            border: 2px solid var(--range-thumb-border-color);
            border-radius: 50%;
            background-color: var(--range-thumb-color);
            cursor: pointer;
            -webkit-transition: background-color var(--transition),
            transform var(--transition);
            transition: background-color var(--transition), transform var(--transition);
        }

        [type="range"]::-moz-range-thumb {
            -webkit-appearance: none;
            width: 1.25rem;
            height: 1.25rem;
            margin-top: -0.5rem;
            border: 2px solid var(--range-thumb-border-color);
            border-radius: 50%;
            background-color: var(--range-thumb-color);
            cursor: pointer;
            -moz-transition: background-color var(--transition),
            transform var(--transition);
            transition: background-color var(--transition), transform var(--transition);
        }

        [type="range"]::-ms-thumb {
            -webkit-appearance: none;
            width: 1.25rem;
            height: 1.25rem;
            margin-top: -0.5rem;
            border: 2px solid var(--range-thumb-border-color);
            border-radius: 50%;
            background-color: var(--range-thumb-color);
            cursor: pointer;
            -ms-transition: background-color var(--transition),
            transform var(--transition);
            transition: background-color var(--transition), transform var(--transition);
        }

        [type="range"]:hover,
        [type="range"]:focus {
            --range-border-color: var(--range-active-border-color);
            --range-thumb-color: var(--range-thumb-hover-color);
        }

        [type="range"]:active {
            --range-thumb-color: var(--range-thumb-active-color);
        }

        [type="range"]:active::-webkit-slider-thumb {
            transform: scale(1.25);
        }

        [type="range"]:active::-moz-range-thumb {
            transform: scale(1.25);
        }

        [type="range"]:active::-ms-thumb {
            transform: scale(1.25);
        }

        input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
            -webkit-padding-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
            padding-inline-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
            border-radius: 5rem;
            background-image: var(--icon-search);
            background-position: center left 1.125rem;
            background-size: 1rem auto;
            background-repeat: no-repeat;
        }

        input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
            -webkit-padding-start: calc(
                    var(--form-element-spacing-horizontal) + 1.75rem
            ) !important;
            padding-inline-start: calc(
                    var(--form-element-spacing-horizontal) + 1.75rem
            ) !important;
            background-position: center left 1.125rem, center right 0.75rem;
        }

        input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="false"] {
            background-image: var(--icon-search), var(--icon-valid);
        }

        input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="true"] {
            background-image: var(--icon-search), var(--icon-invalid);
        }

        [type="search"]::-webkit-search-cancel-button {
            -webkit-appearance: none;
            display: none;
        }

        [dir="rtl"]
        :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
            background-position: center right 1.125rem;
        }

        [dir="rtl"]
        :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
            background-position: center right 1.125rem, center left 0.75rem;
        }

        /**
 * Table
 */
        :where(table) {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            text-indent: 0;
        }

        th,
        td {
            padding: calc(var(--spacing) / 2) var(--spacing);
            border-bottom: var(--border-width) solid var(--table-border-color);
            color: var(--color);
            font-weight: var(--font-weight);
            font-size: var(--font-size);
            text-align: left;
            text-align: start;
        }

        tfoot th,
        tfoot td {
            border-top: var(--border-width) solid var(--table-border-color);
            border-bottom: 0;
        }

        table[role="grid"] tbody tr:nth-child(odd) {
            background-color: var(--table-row-stripped-background-color);
        }

        /**
 * Code
 */
        pre,
        code,
        kbd,
        samp {
            font-size: 0.875em;
            font-family: var(--font-family);
        }

        pre {
            -ms-overflow-style: scrollbar;
            overflow: auto;
        }

        pre,
        code,
        kbd {
            border-radius: var(--border-radius);
            background: var(--code-background-color);
            color: var(--code-color);
            font-weight: var(--font-weight);
            line-height: initial;
        }

        code,
        kbd {
            display: inline-block;
            padding: 0.375rem 0.5rem;
        }

        pre {
            display: block;
            margin-bottom: var(--spacing);
            overflow-x: auto;
        }

        pre > code {
            display: block;
            padding: var(--spacing);
            background: none;
            font-size: 14px;
            line-height: var(--line-height);
        }

        code b {
            color: var(--code-tag-color);
            font-weight: var(--font-weight);
        }

        code i {
            color: var(--code-property-color);
            font-style: normal;
        }

        code u {
            color: var(--code-value-color);
            text-decoration: none;
        }

        code em {
            color: var(--code-comment-color);
            font-style: normal;
        }

        kbd {
            background-color: var(--code-kbd-background-color);
            color: var(--code-kbd-color);
            vertical-align: baseline;
        }

        /**
 * Miscs
 */
        hr {
            height: 0;
            border: 0;
            border-top: 1px solid var(--muted-border-color);
            color: inherit;
        }

        [hidden],
        template {
            display: none !important;
        }

        canvas {
            display: inline-block;
        }

        /**
 * Accordion (<details>)
 */
        details {
            display: block;
            margin-bottom: var(--spacing);
            padding-bottom: var(--spacing);
            border-bottom: var(--border-width) solid var(--accordion-border-color);
        }

        details summary {
            line-height: 1rem;
            list-style-type: none;
            cursor: pointer;
            transition: color var(--transition);
        }

        details summary:not([role]) {
            color: var(--accordion-close-summary-color);
        }

        details summary::-webkit-details-marker {
            display: none;
        }

        details summary::marker {
            display: none;
        }

        details summary::-moz-list-bullet {
            list-style-type: none;
        }

        details summary::after {
            display: block;
            width: 1rem;
            height: 1rem;
            -webkit-margin-start: calc(var(--spacing, 1rem) * 0.5);
            margin-inline-start: calc(var(--spacing, 1rem) * 0.5);
            float: right;
            transform: rotate(-90deg);
            background-image: var(--icon-chevron);
            background-position: right center;
            background-size: 1rem auto;
            background-repeat: no-repeat;
            content: "";
            transition: transform var(--transition);
        }

        details summary:focus {
            outline: none;
        }

        details summary:focus:not([role="button"]) {
            color: var(--accordion-active-summary-color);
        }

        details summary[role="button"] {
            width: 100%;
            text-align: left;
        }

        details summary[role="button"]::after {
            height: calc(1rem * var(--line-height, 1.5));
            background-image: var(--icon-chevron-button);
        }

        details summary[role="button"]:not(.outline).contrast::after {
            background-image: var(--icon-chevron-button-inverse);
        }

        details[open] > summary {
            margin-bottom: calc(var(--spacing));
        }

        details[open] > summary:not([role]):not(:focus) {
            color: var(--accordion-open-summary-color);
        }

        details[open] > summary::after {
            transform: rotate(0);
        }

        [dir="rtl"] details summary {
            text-align: right;
        }

        [dir="rtl"] details summary::after {
            float: left;
            background-position: left center;
        }

        /**
 * Card (<article>)
 */
        article {
            margin: var(--block-spacing-vertical) 0;
            padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
            border-radius: var(--border-radius);
            background: var(--card-background-color);
            box-shadow: var(--card-box-shadow);
        }

        article > header,
        article > footer {
            margin-right: calc(var(--block-spacing-horizontal) * -1);
            margin-left: calc(var(--block-spacing-horizontal) * -1);
            padding: calc(var(--block-spacing-vertical) * 0.66) var(--block-spacing-horizontal);
            background-color: var(--card-sectionning-background-color);
        }

        article > header {
            margin-top: calc(var(--block-spacing-vertical) * -1);
            margin-bottom: var(--block-spacing-vertical);
            border-bottom: var(--border-width) solid var(--card-border-color);
            border-top-right-radius: var(--border-radius);
            border-top-left-radius: var(--border-radius);
        }

        article > footer {
            margin-top: var(--block-spacing-vertical);
            margin-bottom: calc(var(--block-spacing-vertical) * -1);
            border-top: var(--border-width) solid var(--card-border-color);
            border-bottom-right-radius: var(--border-radius);
            border-bottom-left-radius: var(--border-radius);
        }

        /**
 * Modal (<dialog>)
 */
        #mount {
            --scrollbar-width: 0px;
        }

        dialog {
            display: flex;
            z-index: 999;
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            align-items: center;
            justify-content: center;
            width: inherit;
            min-width: 100%;
            height: inherit;
            min-height: 100%;
            padding: var(--spacing);
            border: 0;
            -webkit-backdrop-filter: var(--modal-overlay-backdrop-filter);
            backdrop-filter: var(--modal-overlay-backdrop-filter);
            background-color: var(--modal-overlay-background-color);
            color: var(--color);
        }

        dialog article {
            max-height: calc(100vh - var(--spacing) * 2);
            overflow: auto;
        }

        @media (min-width: 576px) {
            dialog article {
                max-width: 510px;
            }
        }

        @media (min-width: 768px) {
            dialog article {
                max-width: 700px;
            }
        }

        dialog article > header,
        dialog article > footer {
            padding: calc(var(--block-spacing-vertical) * 0.5) var(--block-spacing-horizontal);
        }

        dialog article > header .close {
            margin: 0;
            margin-left: var(--spacing);
            float: right;
        }

        dialog article > footer {
            text-align: right;
        }

        dialog article > footer [role="button"] {
            margin-bottom: 0;
        }

        dialog article > footer [role="button"]:not(:first-of-type) {
            margin-left: calc(var(--spacing) * 0.5);
        }

        dialog article p:last-of-type {
            margin: 0;
        }

        dialog article .close {
            display: block;
            width: 1rem;
            height: 1rem;
            margin-top: calc(var(--block-spacing-vertical) * -0.5);
            margin-bottom: var(--typography-spacing-vertical);
            margin-left: auto;
            background-image: var(--icon-close);
            background-position: center;
            background-size: auto 1rem;
            background-repeat: no-repeat;
            opacity: 0.5;
            transition: opacity var(--transition);
        }

        dialog article .close:is([aria-current], :hover, :active, :focus) {
            opacity: 1;
        }

        dialog:not([open]),
        dialog[open="false"] {
            display: none;
        }

        .modal-is-open {
            padding-right: var(--scrollbar-width, 0px);
            overflow: hidden;
            pointer-events: none;
        }

        .modal-is-open dialog {
            pointer-events: auto;
        }

        :where(.modal-is-opening, .modal-is-closing) dialog,
        :where(.modal-is-opening, .modal-is-closing) dialog > article {
            animation-duration: 0.2s;
            animation-timing-function: ease-in-out;
            animation-fill-mode: both;
        }

        :where(.modal-is-opening, .modal-is-closing) dialog {
            animation-duration: 0.8s;
            animation-name: modal-overlay;
        }

        :where(.modal-is-opening, .modal-is-closing) dialog > article {
            animation-delay: 0.2s;
            animation-name: modal;
        }

        .modal-is-closing dialog,
        .modal-is-closing dialog > article {
            animation-delay: 0s;
            animation-direction: reverse;
        }

        @keyframes modal-overlay {
            from {
                -webkit-backdrop-filter: none;
                backdrop-filter: none;
                background-color: transparent;
            }
        }

        @keyframes modal {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        /**
 * Nav
 */
        :where(nav li)::before {
            float: left;
            content: "​";
        }

        nav,
        nav ul {
            display: flex;
        }

        nav {
            justify-content: space-between;
        }

        nav ol,
        nav ul {
            align-items: center;
            margin-bottom: 0;
            padding: 0;
            list-style: none;
        }

        nav ol:first-of-type,
        nav ul:first-of-type {
            margin-left: calc(var(--nav-element-spacing-horizontal) * -1);
        }

        nav ol:last-of-type,
        nav ul:last-of-type {
            margin-right: calc(var(--nav-element-spacing-horizontal) * -1);
        }

        nav li {
            display: inline-block;
            margin: 0;
            padding: var(--nav-element-spacing-vertical) var(--nav-element-spacing-horizontal);
        }

        nav li > * {
            --spacing: 0;
        }

        nav :where(a, [role="link"]) {
            display: inline-block;
            margin: calc(var(--nav-link-spacing-vertical) * -1) calc(var(--nav-link-spacing-horizontal) * -1);
            padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
            border-radius: var(--border-radius);
            text-decoration: none;
        }

        nav :where(a, [role="link"]):is([aria-current], :hover, :active, :focus) {
            text-decoration: none;
        }

        nav[aria-label="breadcrumb"] {
            align-items: center;
            justify-content: start;
        }

        nav[aria-label="breadcrumb"] ul li:not(:first-child) {
            -webkit-margin-start: var(--nav-link-spacing-horizontal);
            margin-inline-start: var(--nav-link-spacing-horizontal);
        }

        nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
            position: absolute;
            width: calc(var(--nav-link-spacing-horizontal) * 2);
            -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) / 2);
            margin-inline-start: calc(var(--nav-link-spacing-horizontal) / 2);
            content: "/";
            color: var(--muted-color);
            text-align: center;
        }

        nav[aria-label="breadcrumb"] a[aria-current] {
            background-color: transparent;
            color: inherit;
            text-decoration: none;
            pointer-events: none;
        }

        nav [role="button"] {
            margin-right: inherit;
            margin-left: inherit;
            padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
        }

        aside nav,
        aside ol,
        aside ul,
        aside li {
            display: block;
        }

        aside li {
            padding: calc(var(--nav-element-spacing-vertical) * 0.5) var(--nav-element-spacing-horizontal);
        }

        aside li a {
            display: block;
        }

        aside li [role="button"] {
            margin: inherit;
        }

        [dir="rtl"] nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
            content: "\\";
        }

        /**
 * Progress
 */
        progress {
            display: inline-block;
            vertical-align: baseline;
        }

        progress {
            -webkit-appearance: none;
            -moz-appearance: none;
            display: inline-block;
            appearance: none;
            width: 100%;
            height: 0.5rem;
            margin-bottom: calc(var(--spacing) * 0.5);
            overflow: hidden;
            border: 0;
            border-radius: var(--border-radius);
            background-color: var(--progress-background-color);
            color: var(--progress-color);
        }

        progress::-webkit-progress-bar {
            border-radius: var(--border-radius);
            background: none;
        }

        progress[value]::-webkit-progress-value {
            background-color: var(--progress-color);
        }

        progress::-moz-progress-bar {
            background-color: var(--progress-color);
        }

        @media (prefers-reduced-motion: no-preference) {
            progress:indeterminate {
                background: var(--progress-background-color) linear-gradient(
                        to right,
                        var(--progress-color) 30%,
                        var(--progress-background-color) 30%
                ) top left/150% 150% no-repeat;
                animation: progress-indeterminate 1s linear infinite;
            }

            progress:indeterminate[value]::-webkit-progress-value {
                background-color: transparent;
            }

            progress:indeterminate::-moz-progress-bar {
                background-color: transparent;
            }
        }

        @media (prefers-reduced-motion: no-preference) {
            [dir="rtl"] progress:indeterminate {
                animation-direction: reverse;
            }
        }

        @keyframes progress-indeterminate {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        /**
 * Dropdown ([role="list"])
 */
        details[role="list"],
        li[role="list"] {
            position: relative;
        }

        details[role="list"] summary + ul,
        li[role="list"] > ul {
            display: flex;
            z-index: 99;
            position: absolute;
            top: auto;
            right: 0;
            left: 0;
            flex-direction: column;
            margin: 0;
            padding: 0;
            border: var(--border-width) solid var(--dropdown-border-color);
            border-radius: var(--border-radius);
            border-top-right-radius: 0;
            border-top-left-radius: 0;
            background-color: var(--dropdown-background-color);
            box-shadow: var(--card-box-shadow);
            color: var(--dropdown-color);
            white-space: nowrap;
        }

        details[role="list"] summary + ul li,
        li[role="list"] > ul li {
            width: 100%;
            margin-bottom: 0;
            padding: calc(var(--form-element-spacing-vertical) * 0.5) var(--form-element-spacing-horizontal);
            list-style: none;
        }

        details[role="list"] summary + ul li:first-of-type,
        li[role="list"] > ul li:first-of-type {
            margin-top: calc(var(--form-element-spacing-vertical) * 0.5);
        }

        details[role="list"] summary + ul li:last-of-type,
        li[role="list"] > ul li:last-of-type {
            margin-bottom: calc(var(--form-element-spacing-vertical) * 0.5);
        }

        details[role="list"] summary + ul li a,
        li[role="list"] > ul li a {
            display: block;
            margin: calc(var(--form-element-spacing-vertical) * -0.5) calc(var(--form-element-spacing-horizontal) * -1);
            padding: calc(var(--form-element-spacing-vertical) * 0.5) var(--form-element-spacing-horizontal);
            overflow: hidden;
            color: var(--dropdown-color);
            text-decoration: none;
            text-overflow: ellipsis;
        }

        details[role="list"] summary + ul li a:hover,
        li[role="list"] > ul li a:hover {
            background-color: var(--dropdown-hover-background-color);
        }

        details[role="list"] summary::after,
        li[role="list"] > a::after {
            display: block;
            width: 1rem;
            height: calc(1rem * var(--line-height, 1.5));
            -webkit-margin-start: 0.5rem;
            margin-inline-start: 0.5rem;
            float: right;
            transform: rotate(0deg);
            background-position: right center;
            background-size: 1rem auto;
            background-repeat: no-repeat;
            content: "";
        }

        details[role="list"] {
            padding: 0;
            border-bottom: none;
        }

        details[role="list"] summary {
            margin-bottom: 0;
        }

        details[role="list"] summary:not([role]) {
            height: calc(
                    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
                    var(--border-width) * 2
            );
            padding: var(--form-element-spacing-vertical) var(--form-element-spacing-horizontal);
            border: var(--border-width) solid var(--form-element-border-color);
            border-radius: var(--border-radius);
            background-color: var(--form-element-background-color);
            color: var(--form-element-placeholder-color);
            line-height: inherit;
            cursor: pointer;
            transition: background-color var(--transition), border-color var(--transition),
            color var(--transition), box-shadow var(--transition);
        }

        details[role="list"] summary:not([role]):active,
        details[role="list"] summary:not([role]):focus {
            border-color: var(--form-element-active-border-color);
            background-color: var(--form-element-active-background-color);
        }

        details[role="list"] summary:not([role]):focus {
            box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
        }

        details[role="list"][open] summary {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }

        details[role="list"][open] summary::before {
            display: block;
            z-index: 1;
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: none;
            content: "";
            cursor: default;
        }

        nav details[role="list"] summary,
        nav li[role="list"] a {
            display: flex;
            direction: ltr;
        }

        nav details[role="list"] summary + ul,
        nav li[role="list"] > ul {
            min-width: -moz-fit-content;
            min-width: fit-content;
            border-radius: var(--border-radius);
        }

        nav details[role="list"] summary + ul li a,
        nav li[role="list"] > ul li a {
            border-radius: 0;
        }

        nav details[role="list"] summary,
        nav details[role="list"] summary:not([role]) {
            height: auto;
            padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
        }

        nav details[role="list"][open] summary {
            border-radius: var(--border-radius);
        }

        nav details[role="list"] summary + ul {
            margin-top: var(--outline-width);
            -webkit-margin-start: 0;
            margin-inline-start: 0;
        }

        nav details[role="list"] summary[role="link"] {
            margin-bottom: calc(var(--nav-link-spacing-vertical) * -1);
            line-height: var(--line-height);
        }

        nav details[role="list"] summary[role="link"] + ul {
            margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
            -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) * -1);
            margin-inline-start: calc(var(--nav-link-spacing-horizontal) * -1);
        }

        li[role="list"]:hover > ul,
        li[role="list"] a:active ~ ul,
        li[role="list"] a:focus ~ ul {
            display: flex;
        }

        li[role="list"] > ul {
            display: none;
            margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
            -webkit-margin-start: calc(
                    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
            );
            margin-inline-start: calc(
                    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
            );
        }

        li[role="list"] > a::after {
            background-image: var(--icon-chevron);
        }

        /**
 * Loading ([aria-busy=true])
 */
        [aria-busy="true"] {
            cursor: progress;
        }

        [aria-busy="true"]:not(input, select, textarea)::before {
            display: inline-block;
            width: 1em;
            height: 1em;
            border: 0.1875em solid currentColor;
            border-radius: 1em;
            border-right-color: transparent;
            content: "";
            vertical-align: text-bottom;
            vertical-align: -0.125em;
            animation: spinner 0.75s linear infinite;
            opacity: var(--loading-spinner-opacity);
        }

        [aria-busy="true"]:not(input, select, textarea):not(:empty)::before {
            margin-right: calc(var(--spacing) * 0.5);
            margin-left: 0;
            -webkit-margin-start: 0;
            margin-inline-start: 0;
            -webkit-margin-end: calc(var(--spacing) * 0.5);
            margin-inline-end: calc(var(--spacing) * 0.5);
        }

        [aria-busy="true"]:not(input, select, textarea):empty {
            text-align: center;
        }

        button[aria-busy="true"],
        input[type="submit"][aria-busy="true"],
        input[type="button"][aria-busy="true"],
        input[type="reset"][aria-busy="true"],
        a[aria-busy="true"] {
            pointer-events: none;
        }

        @keyframes spinner {
            to {
                transform: rotate(360deg);
            }
        }

        /**
 * Tooltip ([data-tooltip])
 */
        [data-tooltip] {
            position: relative;
        }

        [data-tooltip]:not(a, button, input) {
            border-bottom: 1px dotted;
            text-decoration: none;
            cursor: help;
        }

        [data-tooltip][data-placement="top"]::before,
        [data-tooltip][data-placement="top"]::after,
        [data-tooltip]::before,
        [data-tooltip]::after {
            display: block;
            z-index: 99;
            position: absolute;
            bottom: 100%;
            left: 50%;
            padding: 0.25rem 0.5rem;
            overflow: hidden;
            transform: translate(-50%, -0.25rem);
            border-radius: var(--border-radius);
            background: var(--tooltip-background-color);
            content: attr(data-tooltip);
            color: var(--tooltip-color);
            font-style: normal;
            font-weight: var(--font-weight);
            font-size: 0.875rem;
            text-decoration: none;
            text-overflow: ellipsis;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
        }

        [data-tooltip][data-placement="top"]::after,
        [data-tooltip]::after {
            padding: 0;
            transform: translate(-50%, 0rem);
            border-top: 0.3rem solid;
            border-right: 0.3rem solid transparent;
            border-left: 0.3rem solid transparent;
            border-radius: 0;
            background-color: transparent;
            content: "";
            color: var(--tooltip-background-color);
        }

        [data-tooltip][data-placement="bottom"]::before,
        [data-tooltip][data-placement="bottom"]::after {
            top: 100%;
            bottom: auto;
            transform: translate(-50%, 0.25rem);
        }

        [data-tooltip][data-placement="bottom"]:after {
            transform: translate(-50%, -0.3rem);
            border: 0.3rem solid transparent;
            border-bottom: 0.3rem solid;
        }

        [data-tooltip][data-placement="left"]::before,
        [data-tooltip][data-placement="left"]::after {
            top: 50%;
            right: 100%;
            bottom: auto;
            left: auto;
            transform: translate(-0.25rem, -50%);
        }

        [data-tooltip][data-placement="left"]:after {
            transform: translate(0.3rem, -50%);
            border: 0.3rem solid transparent;
            border-left: 0.3rem solid;
        }

        [data-tooltip][data-placement="right"]::before,
        [data-tooltip][data-placement="right"]::after {
            top: 50%;
            right: auto;
            bottom: auto;
            left: 100%;
            transform: translate(0.25rem, -50%);
        }

        [data-tooltip][data-placement="right"]:after {
            transform: translate(-0.3rem, -50%);
            border: 0.3rem solid transparent;
            border-right: 0.3rem solid;
        }

        [data-tooltip]:focus::before,
        [data-tooltip]:focus::after,
        [data-tooltip]:hover::before,
        [data-tooltip]:hover::after {
            opacity: 1;
        }

        @media (hover: hover) and (pointer: fine) {
            [data-tooltip][data-placement="bottom"]:focus::before,
            [data-tooltip][data-placement="bottom"]:focus::after,
            [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::before,
            [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
            [data-tooltip]:hover::before,
            [data-tooltip]:hover::after {
                animation-duration: 0.2s;
                animation-name: tooltip-slide-top;
            }

            [data-tooltip][data-placement="bottom"]:focus::after,
            [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
            [data-tooltip]:hover::after {
                animation-name: tooltip-caret-slide-top;
            }

            [data-tooltip][data-placement="bottom"]:focus::before,
            [data-tooltip][data-placement="bottom"]:focus::after,
            [data-tooltip][data-placement="bottom"]:hover::before,
            [data-tooltip][data-placement="bottom"]:hover::after {
                animation-duration: 0.2s;
                animation-name: tooltip-slide-bottom;
            }

            [data-tooltip][data-placement="bottom"]:focus::after,
            [data-tooltip][data-placement="bottom"]:hover::after {
                animation-name: tooltip-caret-slide-bottom;
            }

            [data-tooltip][data-placement="left"]:focus::before,
            [data-tooltip][data-placement="left"]:focus::after,
            [data-tooltip][data-placement="left"]:hover::before,
            [data-tooltip][data-placement="left"]:hover::after {
                animation-duration: 0.2s;
                animation-name: tooltip-slide-left;
            }

            [data-tooltip][data-placement="left"]:focus::after,
            [data-tooltip][data-placement="left"]:hover::after {
                animation-name: tooltip-caret-slide-left;
            }

            [data-tooltip][data-placement="right"]:focus::before,
            [data-tooltip][data-placement="right"]:focus::after,
            [data-tooltip][data-placement="right"]:hover::before,
            [data-tooltip][data-placement="right"]:hover::after {
                animation-duration: 0.2s;
                animation-name: tooltip-slide-right;
            }

            [data-tooltip][data-placement="right"]:focus::after,
            [data-tooltip][data-placement="right"]:hover::after {
                animation-name: tooltip-caret-slide-right;
            }
        }

        @keyframes tooltip-slide-top {
            from {
                transform: translate(-50%, 0.75rem);
                opacity: 0;
            }
            to {
                transform: translate(-50%, -0.25rem);
                opacity: 1;
            }
        }

        @keyframes tooltip-caret-slide-top {
            from {
                opacity: 0;
            }
            50% {
                transform: translate(-50%, -0.25rem);
                opacity: 0;
            }
            to {
                transform: translate(-50%, 0rem);
                opacity: 1;
            }
        }

        @keyframes tooltip-slide-bottom {
            from {
                transform: translate(-50%, -0.75rem);
                opacity: 0;
            }
            to {
                transform: translate(-50%, 0.25rem);
                opacity: 1;
            }
        }

        @keyframes tooltip-caret-slide-bottom {
            from {
                opacity: 0;
            }
            50% {
                transform: translate(-50%, -0.5rem);
                opacity: 0;
            }
            to {
                transform: translate(-50%, -0.3rem);
                opacity: 1;
            }
        }

        @keyframes tooltip-slide-left {
            from {
                transform: translate(0.75rem, -50%);
                opacity: 0;
            }
            to {
                transform: translate(-0.25rem, -50%);
                opacity: 1;
            }
        }

        @keyframes tooltip-caret-slide-left {
            from {
                opacity: 0;
            }
            50% {
                transform: translate(0.05rem, -50%);
                opacity: 0;
            }
            to {
                transform: translate(0.3rem, -50%);
                opacity: 1;
            }
        }

        @keyframes tooltip-slide-right {
            from {
                transform: translate(-0.75rem, -50%);
                opacity: 0;
            }
            to {
                transform: translate(0.25rem, -50%);
                opacity: 1;
            }
        }

        @keyframes tooltip-caret-slide-right {
            from {
                opacity: 0;
            }
            50% {
                transform: translate(-0.05rem, -50%);
                opacity: 0;
            }
            to {
                transform: translate(-0.3rem, -50%);
                opacity: 1;
            }
        }

        /**
 * Accessibility & User interaction
 */
        [aria-controls] {
            cursor: pointer;
        }

        [aria-disabled="true"],
        [disabled] {
            cursor: not-allowed;
        }

        [aria-hidden="false"][hidden] {
            display: initial;
        }

        [aria-hidden="false"][hidden]:not(:focus) {
            clip: rect(0, 0, 0, 0);
            position: absolute;
        }

        a,
        area,
        button,
        input,
        label,
        select,
        summary,
        textarea,
        [tabindex] {
            -ms-touch-action: manipulation;
        }

        [dir="rtl"] {
            direction: rtl;
        }

        /**
* Reduce Motion Features
*/
        @media (prefers-reduced-motion: reduce) {
            *:not([aria-busy="true"]),
            :not([aria-busy="true"])::before,
            :not([aria-busy="true"])::after {
                background-attachment: initial !important;
                animation-duration: 1ms !important;
                animation-delay: -1ms !important;
                animation-iteration-count: 1 !important;
                scroll-behavior: auto !important;
                transition-delay: 0s !important;
                transition-duration: 0s !important;
            }
        }

        #mount#mount {
            /* --primary: rgb(227, 59, 126); */
            --primary: #ea4c89;
            --primary-hover: #f082ac;
            --icon-xia: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9IkZyYW1lIj4KPHBhdGggaWQ9IlZlY3RvciIgZD0iTTguMDAyOTEgOS42Nzk4M0wzLjgzMzM5IDUuNTEyMjFMMy4wMjUzOSA2LjMxOTgzTDguMDAzMjkgMTEuMjk1MUwxMi45NzYyIDYuMzE5ODNMMTIuMTY3OSA1LjUxMjIxTDguMDAyOTEgOS42Nzk4M1oiIGZpbGw9IiM4MzgzODMiLz4KPC9nPgo8L3N2Zz4K");
            --switch-checked-background-color: var(--primary);
        }

        li.select-link.select-link:hover > ul {
            display: none;
        }

        li.select-link.select-link > ul {
            display: none;
        }

        li.select-link.select-link a:focus ~ ul {
            display: none;
        }

        li.select-link.select-link a:active ~ ul {
            display: none;
        }

        li.select-link-active.select-link-active > ul {
            display: flex;
        }

        li.select-link-active.select-link-active:hover > ul {
            display: flex;
        }

        li.select-link-active.select-link-active a:focus ~ ul {
            display: flex;
        }

        li.select-link-active.select-link-active a:active ~ ul {
            display: flex;
        }

        ul.select-link-ul.select-link-ul {
            right: 0px;
            left: auto;
        }

        a.select-link-selected {
            background-color: var(--primary-focus);
        }

        .immersive-translate-no-select {
            -webkit-touch-callout: none; /* iOS Safari */
            -webkit-user-select: none; /* Safari */
            -khtml-user-select: none; /* Konqueror HTML */
            -moz-user-select: none; /* Old versions of Firefox */
            -ms-user-select: none; /* Internet Explorer/Edge */
            user-select: none;
        }

        /* li[role="list"].no-arrow > a::after { */
        /*   background-image: none; */
        /*   width: 0; */
        /*   color: var(--color); */
        /* } */
        li[role="list"].no-arrow {
            margin-left: 8px;
            padding-right: 0;
        }

        li[role="list"] > a::after {
            -webkit-margin-start: 0.2rem;
            margin-inline-start: 0.2rem;
        }

        li[role="list"].no-arrow > a,
        li[role="list"].no-arrow > a:link,
        li[role="list"].no-arrow > a:visited {
            color: var(--secondary);
        }

        select.min-select {
            --form-element-spacing-horizontal: 0;
            margin-bottom: 4px;
            max-width: 128px;
            overflow: hidden;
            color: var(--primary);
            font-size: 13px;
            border: none;
            padding: 0;
            padding-right: 20px;
            padding-left: 8px;
            text-overflow: ellipsis;
            color: var(--color);

        }

        select.min-select-secondary {
            color: var(--color);
        }

        select.min-select:focus {
            outline: none;
            border: none;
            --box-shadow: none;
        }

        select.min-select-no-arrow {
            background-image: none;
            padding-right: 0;
        }

        select.min-select-left {
            padding-right: 0px;
            /* padding-left: 24px; */
            /* background-position: center left 0; */
            text-overflow: ellipsis;
            text-align: left;
        }

        .muted {
            color: var(--muted-color);
        }

        .select.button-select {
            --background-color: var(--secondary-hover);
            --border-color: var(--secondary-hover);
            --color: var(--secondary-inverse);
            cursor: pointer;
            --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
            padding: var(--form-element-spacing-vertical) var(--form-element-spacing-horizontal);
            border: var(--border-width) solid var(--border-color);
            border-radius: var(--border-radius);
            outline: none;
            background-color: var(--background-color);
            box-shadow: var(--box-shadow);
            color: var(--color);
            font-weight: var(--font-weight);
            font-size: 16px;
            line-height: var(--line-height);
            text-align: center;
            cursor: pointer;
            transition: background-color var(--transition), border-color var(--transition),
            color var(--transition), box-shadow var(--transition);
            -webkit-appearance: button;
            margin: 0;
            margin-bottom: 0px;
            overflow: visible;
            font-family: inherit;
            text-transform: none;
        }

        html {
            font-size: 16px;
            --font-size: 16px;
        }

        body {
            padding: 0;
            margin: 0 auto;
            min-width: 268px;
            border-radius: 10px;
        }

        .popup-container {
            color: #666;
            background-color: var(--popup-footer-background-color);
            width: 316px;
            min-width: 316px;
        }

        .popup-content {
            background-color: var(--popup-content-background-color);
            border-radius: 0px 0px 12px 12px;
            padding: 16px 20px;
        }

        .immersive-translate-popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            touch-action: none;
        }

        .immersive-translate-popup-wrapper {
            background: var(--background-color);
            border-radius: 10px;
            border: 1px solid var(--muted-border-color);
        }

        #mount#mount {
            --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
            "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
            "Segoe UI Symbol", "Noto Color Emoji";
            --line-height: 1.5;
            --font-weight: 400;
            --font-size: 16px;
            --border-radius: 4px;
            --border-width: 1px;
            --outline-width: 3px;
            --spacing: 16px;
            --typography-spacing-vertical: 24px;
            --block-spacing-vertical: calc(var(--spacing) * 2);
            --block-spacing-horizontal: var(--spacing);
            --grid-spacing-vertical: 0;
            --grid-spacing-horizontal: var(--spacing);
            --form-element-spacing-vertical: 12px;
            --form-element-spacing-horizontal: 16px;
            --nav-element-spacing-vertical: 16px;
            --nav-element-spacing-horizontal: 8px;
            --nav-link-spacing-vertical: 8px;
            --nav-link-spacing-horizontal: 8px;
            --form-label-font-weight: var(--font-weight);
            --transition: 0.2s ease-in-out;
            --modal-overlay-backdrop-filter: blur(4px);
        }

        [data-theme="light"],
        #mount:not([data-theme="dark"]) {
            --popup-footer-background-color: #e8eaeb;
            --popup-content-background-color: #ffffff;
            --popup-item-background-color: #f3f5f6;
            --popup-item-hover-background-color: #eaeced;
            --popup-trial-pro-background-color: #F9FBFC;
            --text-black-2: #222222;
            --text-gray-2: #222222;
            --text-gray-6: #666666;
            --text-gray-9: #999999;
            --text-gray-c2: #c2c2c2;
        }

        @media only screen and (prefers-color-scheme: dark) {
            #mount:not([data-theme="light"]) {
                --popup-footer-background-color: #0d0d0d;
                --popup-content-background-color: #191919;
                --popup-item-background-color: #272727;
                --popup-item-hover-background-color: #333333;
                --popup-trial-pro-background-color: #222222;
                --text-black-2: #ffffff;
                --text-gray-2: #dbdbdb;
                --text-gray-6: #b3b3b3;
                --text-gray-9: #777777;
                --text-gray-c2: #5b5b5b;
            }
        }

        [data-theme="dark"] {
            --popup-footer-background-color: #0d0d0d;
            --popup-content-background-color: #191919;
            --popup-item-background-color: #272727;
            --popup-item-hover-background-color: #333333;
            --popup-trial-pro-background-color: #222222;
            --text-black-2: #ffffff;
            --text-gray-2: #dbdbdb;
            --text-gray-6: #b3b3b3;
            --text-gray-9: #777777;
            --text-gray-c2: #5b5b5b;
        }

        .text-balck {
            color: var(--text-black-2);
        }

        .text-gray-2 {
            color: var(--text-gray-2);
        }

        .text-gray-6 {
            color: var(--text-gray-6);
        }

        .text-gray-9 {
            color: var(--text-gray-9);
        }

        .text-gray-c2 {
            color: var(--text-gray-c2);
        }

        #mount {
            min-width: 268px;
        }

        .main-button {
            font-size: 15px;
            vertical-align: middle;
            border-radius: 12px;
            padding: unset;
            height: 44px;
            line-height: 44px;
        }

        .pt-4 {
            padding-top: 16px;
        }

        .p-2 {
            padding: 8px;
        }

        .pl-5 {
            padding-left: 48px;
        }

        .p-0 {
            padding: 0;
        }

        .pl-2 {
            padding-left: 8px;
        }

        .pl-4 {
            padding-left: 24px;
        }

        .pt-2 {
            padding-top: 8px;
        }

        .pb-2 {
            padding-bottom: 8px;
        }

        .pb-4 {
            padding-bottom: 16px;
        }

        .pb-5 {
            padding-bottom: 20px;
        }

        .pr-5 {
            padding-right: 48px;
        }

        .text-sm {
            font-size: 13px;
        }

        .text-base {
            font-size: 16px;
        }

        .w-full {
            width: 100%;
        }

        .flex {
            display: flex;
        }

        .flex-row {
            flex-direction: row;
        }

        .flex-wrap {
            flex-wrap: wrap;
        }

        .flex-end {
            justify-content: flex-end;
        }

        .flex-grow {
            flex-grow: 1;
        }

        .justify-between {
            justify-content: space-between;
        }

        .mb-0 {
            margin-bottom: 0px;
        }

        .mb-2 {
            margin-bottom: 8px;
        }

        .mb-4 {
            margin-bottom: 16px;
        }

        .mb-3 {
            margin-bottom: 12px;
        }

        .inline-block {
            display: inline-block;
        }

        .py-2 {
            padding-top: 8px;
            padding-bottom: 8px;
        }

        .py-2-5 {
            padding-top: 6px;
            padding-bottom: 6px;
        }

        .mt-0 {
            margin-top: 0;
        }

        .mt-2 {
            margin-top: 8px;
        }

        .mt-3 {
            margin-top: 12px;
        }

        .mt-4 {
            margin-top: 16px;
        }

        .mt-5 {
            margin-top: 20px;
        }

        .mt-6 {
            margin-top: 24px;
        }

        .mb-1 {
            margin-bottom: 4px;
        }

        .ml-4 {
            margin-left: 24px;
        }

        .ml-3 {
            margin-left: 16px;
        }

        .ml-2 {
            margin-left: 8px;
        }

        .ml-1 {
            margin-left: 4px;
        }

        .mr-1 {
            margin-right: 4px;
        }

        .mr-2 {
            margin-right: 8px;
        }

        .mr-3 {
            margin-right: 16px;
        }

        .mx-2 {
            margin-left: 8px;
            margin-right: 8px;
        }

        .pl-3 {
            padding-left: 12px;
        }

        .pr-3 {
            padding-right: 12px;
        }

        .p-3 {
            padding: 12px;
        }

        .px-1 {
            padding-left: 4px;
            padding-right: 4px;
        }

        .px-3 {
            padding-left: 12px;
            padding-right: 12px;
        }

        .pt-3 {
            padding-top: 12px;
        }

        .px-6 {
            padding-left: 18px;
            padding-right: 18px;
        }

        .px-4 {
            padding-left: 16px;
            padding-right: 16px;
        }

        .pt-6 {
            padding-top: 20px;
        }

        .py-3 {
            padding-top: 12px;
            padding-bottom: 12px;
        }

        .py-0 {
            padding-top: 0;
            padding-bottom: 0;
        }

        .left-auto {
            left: auto !important;
        }

        .max-h-28 {
            max-height: 112px;
        }

        .max-h-30 {
            max-height: 120px;
        }

        .overflow-y-scroll {
            overflow-y: scroll;
        }

        .text-xs {
            font-size: 12px;
        }

        .flex-1 {
            flex: 1;
        }

        .flex-3 {
            flex: 3;
        }

        .flex-4 {
            flex: 4;
        }

        .flex-2 {
            flex: 2;
        }

        .items-center {
            align-items: center;
        }

        .max-content {
            width: max-content;
        }

        .justify-center {
            justify-content: center;
        }

        .items-end {
            align-items: flex-end;
        }

        .items-baseline {
            align-items: baseline;
        }

        .my-5 {
            margin-top: 48px;
            margin-bottom: 48px;
        }

        .my-4 {
            margin-top: 24px;
            margin-bottom: 24px;
        }

        .my-3 {
            margin-top: 16px;
            margin-bottom: 16px;
        }

        .pt-3 {
            padding-top: 12px;
        }

        .px-3 {
            padding-left: 12px;
            padding-right: 12px;
        }

        .pt-2 {
            padding-top: 8px;
        }

        .px-2 {
            padding-left: 8px;
            padding-right: 8px;
        }

        .pt-1 {
            padding-top: 4px;
        }

        .px-1 {
            padding-left: 4px;
            padding-right: 4px;
        }

        .pb-2 {
            padding-bottom: 8px;
        }

        .justify-end {
            justify-content: flex-end;
        }

        .w-auto {
            width: auto;
        }

        .shrink-0 {
            flex-shrink: 0;
        }

        select.language-select,
        select.translate-service,
        select.min-select {
            --form-element-spacing-horizontal: 0;
            margin-bottom: 0px;
            max-width: unset;
            flex: 1;
            overflow: hidden;
            font-size: 13px;
            border: none;
            border-radius: 8px;
            padding-right: 30px;
            padding-left: 0px;
            background-position: center right 12px;
            background-size: 16px auto;
            background-image: var(--icon-xia);
            text-overflow: ellipsis;
            color: var(--text-gray-2);
            background-color: transparent;
            box-shadow: unset !important;
            cursor: pointer;
        }

        select.more {
            background-position: center right;
            padding-right: 20px;
        }

        select.transform-padding-left {
            padding-left: 12px;
            transform: translateX(-12px);
        }

        select.translate-service {
            color: var(--text-black-2);
        }

        /* dark use black, for windows */
        @media (prefers-color-scheme: dark) {

            select.language-select option,
            select.translate-service option,
            select.min-select option {
                background-color: #666666;
            }
        }

        .text-overflow-ellipsis {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }

        .max-w-20 {
            max-width: 180px;
            white-space: nowrap;
        }

        select.min-select-secondary {
            color: var(--color);
        }

        select.min-select:focus {
            outline: none;
            border: none;
            --box-shadow: none;
        }

        select.min-select-no-arrow {
            background-image: none;
            padding-right: 0;
        }

        select.min-select-left {
            padding-right: 0px;
            /* padding-left: 24px; */
            /* background-position: center left 0; */
            text-overflow: ellipsis;
            text-align: left;
        }

        .popup-footer {
            background-color: var(--popup-footer-background-color);
            height: 40px;
        }

        .text-right {
            text-align: right;
        }

        .clickable {
            cursor: pointer;
        }

        .close {
            cursor: pointer;
            width: 16px;
            height: 16px;
            background-image: var(--icon-close);
            background-position: center;
            background-size: auto 1rem;
            background-repeat: no-repeat;
            opacity: 0.5;
            transition: opacity var(--transition);
        }

        .padding-two-column {
            padding-left: 40px;
            padding-right: 40px;
        }

        .muted {
            color: #999;
        }

        .text-label {
            color: #666;
        }

        .display-none {
            display: none;
        }

        /* dark use #18232c */
        @media (prefers-color-scheme: dark) {
            .text-label {
                color: #9ca3af;
            }
        }

        .text-decoration-none {
            text-decoration: none;
        }

        .text-decoration-none:is([aria-current], :hover, :active, :focus),
        [role="link"]:is([aria-current], :hover, :active, :focus) {
            --text-decoration: none !important;
            background-color: transparent !important;
        }

        .language-select-container {
            position: relative;
            width: 100%;
            background-color: var(--popup-item-background-color);
            height: 55px;
            border-radius: 12px;
        }

        select.language-select {
            color: var(--text-black-2);
            font-size: 14px;
            padding: 8px 24px 24px 16px;
            position: absolute;
            border-radius: 12px;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
        }

        select.text-gray-6 {
            color: var(--text-gray-6);
        }

        .language-select-container label {
            position: absolute;
            bottom: 10px;
            left: 16px;
            font-size: 12px;
            color: var(--text-gray-9);
            line-height: 12px;
            margin: 0;
        }

        .translation-service-container {
            background-color: var(--popup-item-background-color);
            border-radius: 12px;
        }

        .min-select-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 44px;
            background-color: var(--popup-item-background-color);
            padding-left: 16px;
        }

        .min-select-container:first-child {
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .min-select-container:last-child {
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        .min-select-container:only-child {
            border-radius: 10px;
        }

        .translate-mode {
            width: 44px;
            height: 44px;
            border-radius: 22px;
            background-color: var(--popup-item-background-color);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            cursor: pointer;
        }

        .translate-mode svg {
            fill: var(--text-gray-2);
        }

        .widgets-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .widgets-container > :not(:last-child) {
            margin-right: 8px;
        }

        .widget-item {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--popup-item-background-color);
            font-size: 12px;
            height: 44px;
            border-radius: 8px;
            cursor: pointer;
            flex: 1;
        }

        .widget-item svg {
            fill: var(--text-gray-2);
        }

        .setting svg {
            fill: var(--text-gray-6);
        }

        .share-button-container {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 2px 3px 0 8px;
        }

        .share-button-container svg {
            fill: var(--text-gray-9);
        }

        .min-select-container:hover,
        .language-select-container:hover,
        .widget-item:hover,
        .translate-mode:hover {
            background-color: var(--popup-item-hover-background-color);
        }

        .main-button:hover {
            background-color: #f5508f;
        }

        .share-button-container:hover {
            background-color: var(--popup-item-background-color);
            border-radius: 6px;
        }

        .error-boundary {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            display: flex;
            padding: 12px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.88);
            word-break: break-all;
            margin: 12px;
            border-radius: 12px;
            flex-direction: column;
        }


        .upgrade-pro {
            border-radius: 11px;
            background: linear-gradient(57deg, #272727 19.8%, #696969 82.2%);
            padding: 2px 8px;
            transform: scale(0.85);
        }

        .upgrade-pro span {
            background: linear-gradient(180deg, #FFEAB4 17.65%, #F8C235 85.29%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 12px;
            margin-left: 4px;
        }


        .upgrade-pro svg {
            margin-top: -2px;
        }

        .upgrade-pro:hover {
            background: linear-gradient(57deg, #3D3D3D 19.8%, #949494 82.2%);
        }

        .border-bottom-radius-0 {
            border-bottom-left-radius: 0 !important;
            border-bottom-right-radius: 0 !important;
        }

        .trial-pro-container {
            border-radius: 0px 0px 12px 12px;
            background: var(--popup-trial-pro-background-color);
            display: flex;
            align-items: center;
            height: 44px;
            padding-left: 16px;
            padding-right: 12px;
            font-size: 12px;
        }

        .trial-pro-container label {
            line-height: 13px;
            color: var(--text-black-2);
        }

        .trial-pro-container img {
            margin-left: 5px;
        }

        .cursor-pointer {
            cursor: pointer;
        }

        .upgrade-pro-discount-act {
            height: 25px;
            display: flex;
            padding: 0 4px;
            align-items: center;
            border-radius: 15px;
            background: linear-gradient(90deg, #CEFBFA 11.33%, #D7F56F 63.75%, #FCCD5E 100%);
            transform: scale(0.9);
            box-shadow: 0px 1.8px 3.6px 0px rgba(0, 0, 0, 0.10);
            cursor: pointer;
        }

        .upgrade-pro-discount-act span {
            font-size: 12px;
            font-weight: 700;
            margin-left: 4px;
            color: #222222;
        }

        .upgrade-pro-discount-act:hover {
            text-decoration: unset;
            background: linear-gradient(90deg, #E2FFFE 11.33%, #E6FF91 63.75%, #FFDF93 100%);
        }

        html {
            font-size: 17px;
        }

        @media print {
            .imt-fb-container {
                display: none !important;
            }
        }

        #mount#mount {
            position: absolute;
            display: none;
            min-width: 250px;
            height: auto;
            --font-size: 17px;
            font-size: 17px;
        }

        /* float-ball */
        .imt-fb-container {
            position: fixed;
            padding: 0;
            z-index: 2147483647;
            top: 335px;
            width: 56px;
            display: flex;
            flex-direction: column;
            display: none;
        }

        .imt-fb-container.left {
            align-items: flex-start;
            left: 0;
        }

        .imt-fb-container.right {
            align-items: flex-end;
            right: 0;
        }

        .imt-fb-btn {
            cursor: pointer;
            background: linear-gradient(320.9deg, #db3b7b 26.47%, #ffcee2 88.86%);
            height: 36px;
            width: 56px;
            box-shadow: 2px 6px 10px 0px #0e121629;
        }

        .imt-fb-btn.left {
            border-top-right-radius: 36px;
            border-bottom-right-radius: 36px;
        }

        .imt-fb-btn.right {
            border-top-left-radius: 36px;
            border-bottom-left-radius: 36px;
        }

        .imt-fb-btn div {
            background: linear-gradient(140.91deg, #ff87b7 12.61%, #ec4c8c 76.89%);
            height: 34px;
            width: 54px;
            margin: 1px;
            display: flex;
            align-items: center;
        }

        .imt-fb-btn.left div {
            border-top-right-radius: 34px;
            border-bottom-right-radius: 34px;
            justify-content: flex-end;
        }

        .imt-fb-btn.right div {
            border-top-left-radius: 34px;
            border-bottom-left-radius: 34px;
        }

        .imt-fb-logo-img {
            width: 20px;
            height: 20px;
            margin: 0 10px;
        }

        .imt-float-ball-translated {
            position: absolute;
            width: 11px;
            height: 11px;
            bottom: 4px;
            right: 20px;
        }

        .btn-animate {
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
            -webkit-transition: -webkit-transform ease-out 250ms;
            transition: -webkit-transform ease-out 250ms;
            transition: transform ease-out 250ms;
            transition: transform ease-out 250ms, -webkit-transform ease-out 250ms;
        }

        .imt-fb-setting-btn {
            margin-right: 18px;
            width: 28px;
            height: 28px;
        }

        .immersive-translate-popup-wrapper {
            background: var(--background-color);
            border-radius: 20px;
            box-shadow: 2px 10px 24px 0px #0e121614;
            border: none;
            overflow: hidden;
        }

        .imt-fb-close-content {
            padding: 22px;
            width: 320px;
        }

        .imt-fb-close-title {
            font-weight: 500;
            color: var(--h2-color);
        }

        .imt-fb-close-radio-content {
            background-color: var(--background-light-green);
            padding: 8px 20px;
        }

        .imt-fb-radio-sel,
        .imt-fb-radio-nor {
            width: 16px;
            height: 16px;
            border-radius: 8px;
            flex-shrink: 0;
        }

        .imt-fb-radio-sel {
            border: 2px solid var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .imt-fb-radio-sel div {
            width: 8px;
            height: 8px;
            border-radius: 4px;
            background-color: var(--primary);
        }

        .imt-fb-radio-nor {
            border: 2px solid #d3d4d6;
        }

        .imt-fb-primary-btn {
            background-color: var(--primary);
            width: 72px;
            height: 32px;
            color: white;
            border-radius: 8px;
            text-align: center;
            line-height: 32px;
            font-size: 16px;
            cursor: pointer;
        }

        .imt-fb-default-btn {
            border: 1px solid var(--primary);
            width: 72px;
            height: 32px;
            border-radius: 8px;
            color: var(--primary);
            line-height: 32px;
            text-align: center;
            font-size: 16px;
        }

        .imt-fb-guide-container {
            width: 312px;
            transform: translateY(-50%);
        }

        .imt-fb-guide-bg {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: -1;
            height: 100%;
            width: 100%;
        }

        .imt-fb-guide-bg.left {
            transform: scaleX(-1);
        }

        .imt-fb-guide-content {
            margin: 16px 32px 60px 21px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .imt-fb-guide-content.left {
            margin: 16px 21px 60px 32px;
        }

        .imt-fb-guide-img {
            width: 235px;
            height: 171px;
            margin-top: 16px;
        }

        .imt-fb-guide-message {
            font-size: 16px;
            line-height: 28px;
            color: #333333;
            white-space: pre-wrap;
            text-align: center;
            font-weight: 700;
            margin-top: 10px;
        }

        .imt-fb-guide-button {
            margin-top: 16px;
            line-height: 40px;
            height: 40px;
            padding: 0 20px;
            width: unset;
        }

        .imt-fb-more-buttons {
            box-shadow: 0px 2px 10px 0px #00000014;
            border: 1px solid var(--float-ball-more-button-border-color);
            background: var(--float-ball-more-button-background-color);
            width: 36px;
            display: flex;
            flex-direction: column;
            border-radius: 18px;
            margin-right: 8px;
        }

        .imt-fb-more-button {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }


        /* Sheet.css */
        .immersive-translate-sheet {
            position: fixed;
            transform: translateY(100%);
            /* Start off screen */
            left: 0;
            right: 0;
            background-color: white;
            transition: transform 0.3s ease-out;
            /* Smooth slide transition */
            box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.1);
            /* Ensure it's above other content */
            bottom: 0;
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            overflow: hidden;
        }

        .immersive-translate-sheet.visible {
            transform: translateY(0);
        }

        .immersive-translate-sheet-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            opacity: 0;
            transition: opacity 0.3s ease-out;
        }

        .immersive-translate-sheet-backdrop.visible {
            opacity: 1;
        }

        .popup-container-sheet {
            max-width: 100vw;
            width: 100vw;
        }

        .imt-no-events svg * {
            pointer-events: none !important;
        }

        .imt-manga-button {
            width: 36px;
            display: flex;
            flex-direction: column;
            position: relative;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            pointer-events: all;
            margin: 12px 0 0 0;
            background-color: white;
            border-radius: 18px;
            filter: drop-shadow(0px 2px 10px rgba(0, 0, 0, 0.08));
            opacity: 0.5;
            right: 8px;
        }

        .imt-manga-feedback {
            cursor: pointer;
            margin: 10px 9px 12px 9px;
        }

        .imt-manga-button:hover {
            opacity: 1;
        }

        .imt-manga-translated {
            position: absolute;
            left: 24px;
            top: 20px;
        }

        .imt-float-ball-loading {
            animation: imt-loading-animation 0.6s infinite linear !important;
        }

        .imt-manga-guide-bg {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: -1;
            width: 372px;
            transform: translateY(-50%);
        }

        .imt-manga-guide-content {
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            margin: 0 40px 0;
        }

        .img-manga-guide-button {
            width: fit-content;
            margin: 16px auto;
        }

        .img-manga-close {
            position: absolute;
            bottom: -200px;
            width: 32px;
            height: 32px;
            left: 0;
            right: 0;
            margin: auto;
            cursor: pointer;
        }

        @-webkit-keyframes imt-loading-animation {
            from {
                -webkit-transform: rotate(0deg);
            }

            to {
                -webkit-transform: rotate(359deg);
            }
        }

        @keyframes imt-loading-animation {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(359deg);
            }
        }
        </style>
        <div id="mount" style="display: block;"></div>
    </template>
</div>
</html>