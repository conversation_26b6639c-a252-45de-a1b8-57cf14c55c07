* {
    margin: 0;
    padding: 0;
}

.wapper {
    position: relative;
    padding: 163px 0 202px;
    width: 100%;
}

.wapper .left-bottom {
    position: absolute;
    width: 200px;
    height: 86px;
    bottom: 0;
    left: 44px;
    background: url('../images/left-bottom.png') no-repeat center / cover;
}

.wapper .right-top {
    position: absolute;
    width: 63px;
    height: 27px;
    top: 180px;
    right: 286px;
    background: url('../images/right-top.png') no-repeat center / cover;
}

.wapper .main {
    position: relative;
    margin: 0 auto;
    width: 801px;
    height: 582px;
    background: url('../images/404.png') no-repeat center / cover;
}

.wapper .main .animation {
    position: absolute;
    width: 178px;
    height: 217px;
    top: 78px;
    left: 53px;
    background: url('../images/animation.png') no-repeat center / cover;
    animation-duration: 15s;
}
.wapper .button {
    margin: 33px auto 0;
    width: 140px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    background-color: #b61f22;
    border-radius: 10px;

}
.wapper .button a {
    display: block;
    text-decoration: none;
    font-size: 18px;
    color: #fff;
}