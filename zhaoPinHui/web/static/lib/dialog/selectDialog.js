var selectPanelTemplate = `
    <div class="select-dialog">
        <el-input
        ref="targetInput"
        v-model="valueText"
        readonly
        @click="handleOpen"
        :clearable="clearable"
        :placeholder="placeholder"
        @clear="handleClear"
        ></el-input>

        <el-dialog
        v-model="visible"
        :close-on-click-modal="closeOnClickModal"
        @close="handleClose">
            <template #title>
                <div class="select-header">
                    <div class="title">{{ title }}</div>
                    <div class="tips">
                        (最多选择<span>{{ multipleLimit }}</span
                        >项)
                    </div>
                    <div v-if="remote" class="search">
                        <el-select
                        :remote-method="handleSearch"
                        filterable
                        remote
                        reserve-keyword
                        :size="size"
                        :placeholder="searchPlaceholder"
                        v-model="searValue"
                        @change="handleSearchChange"
                        >
                        <el-option
                            v-for="item in searchOptions"
                            :key="item.k"
                            :label="item.v"
                            :value="item.k"
                        />
                        </el-select>
                    </div>
                </div>
            </template>

            <div class="select-content">
                <div v-show="!!valueItems.length" class="select">
                    <div class="select-label">已选：</div>
                    <div class="select-value">
                        <el-tag
                        v-for="item in valueItems"
                        :key="item.k"
                        @close="handleRemove(item)"
                        class="tag"
                        disable-transitions
                        closable
                        >
                        <span class="el-tag__content">{{ item.v }}</span>
                        </el-tag>
                    </div>
                </div>

                <div class="data-content">
                    <div class="left-content">
                        <a
                        @click="handleClickFirst(index)"
                        v-for="(item, index) in list"
                        :key="item.k"
                        href="JavaScript:;"
                        class="list"
                        :class="{
                            active: selectFirstIndex === index,
                            'has-select': handleFirstClass(item.k)
                        }">{{ item.v }}</a>
                    </div>

                    <div class="right-content">
                        <div v-for="(first, first_index) in list"
                        :key="first_index"
                        class="second-content"
                        :class="{ show: selectFirstIndex == first_index }">
                        <!-- 带标题-只含二级或者只含三级 目前只有户籍国籍-海外-->
                        <template v-if="first.k == '3870' && first.v == '海外' && name === 'allNative'">
                            <div
                            v-for="(second, second_index) in first.children"
                            :key="second_index"
                            class="second-list"
                            >
                            <div class="second-list-title">{{ second.v }}</div>
                            <!-- 含三级 -->
                            <template v-if="/-1/.test(second.k)">
                                <div class="second-list-content">
                                <div
                                    v-for="(second_1, second_index_1) in second.children"
                                    :key="second_index_1"
                                >
                                    <div class="column-4">
                                    <div
                                        v-for="(second_2, second_index_2) in second_1.children"
                                        :key="second_index_2"
                                        class="second-item"
                                    >
                                        <label>
                                        <input
                                            class="btn-active"
                                            :class="{
                                            'has-select': handleSecondClass(second_2.k)
                                            }"
                                            name="openThirdKey"
                                            :value="second_2.k"
                                            v-model="secondOpenkey"
                                            type="radio"
                                        />
                                        <span>{{ second_2.v }}</span>
                                        </label>
                                    </div>
                                    </div>

                                    <div class="third-content">
                                    <div
                                        v-for="(second_2, second_index_2) in second_1.children"
                                        :key="second_index_2"
                                    >
                                        <div
                                        class="third-list"
                                        :class="[
                                            second_2.k === secondOpenkey ? 'column-4' : 'hidden'
                                        ]"
                                        >
                                        <div
                                            v-for="(third, third_index) in second_2.children"
                                            :key="third_index"
                                            class="third-item"
                                        >
                                            <label>
                                            <input
                                                @input="
                                                (e) => {
                                                    handleSelect(e)
                                                }
                                                "
                                                class="btn-select"
                                                name="value"
                                                :value="third.k"
                                                v-model="value"
                                                type="checkbox"
                                            />
                                            <span>{{ third.v }}</span>
                                            </label>
                                        </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                                </div>
                            </template>

                            <!-- 只有二级 -->
                            <template v-else>
                                <div class="second-list-content">
                                <div class="column-4">
                                    <div
                                    v-for="(second_1, second_index_1) in second.children"
                                    :key="second_index_1"
                                    class="second-item"
                                    >
                                    <label>
                                        <input
                                        @input="
                                            (e) => {
                                            handleSelect(e)
                                            }
                                        "
                                        class="btn-select"
                                        name="value"
                                        :value="second_1.k"
                                        v-model="value"
                                        type="checkbox"
                                        />
                                        <span>{{ second_1.v }}</span>
                                    </label>
                                    </div>
                                </div>
                                </div>
                            </template>
                            </div>
                        </template>

                        <!-- 不带标题-只含二级或只含三级 -->
                        <template v-else>
                            <!-- 只有二级 -->
                            <template v-if="secondMap.includes(name)">
                                <div class="second-list-content" class="column-4">
                                    <div v-for="second in first.children" :key="second.k" class="second-item only">
                                    <label>
                                        <input
                                        @input="
                                            (e) => {
                                            handleSelect(e)
                                            }
                                        "
                                        class="btn-select"
                                        name="value"
                                        :value="second.k"
                                        v-model="value"
                                        type="checkbox"
                                        />
                                        <span>{{ second.v }}</span>
                                    </label>
                                    </div>
                                </div>
                            </template>

                            <!-- 含三级 -->
                            <template v-if="thirdMap.includes(name)">
                                <div class="second-list-content">
                                    <div v-for="(second_1, second_index_1) in first.children" :key="second_index_1">
                                    <div class="column-4">
                                        <div
                                        v-for="(second_2, second_index_2) in second_1.children"
                                        :key="second_index_2"
                                        class="second-item"
                                        >
                                        <label>
                                            <!-- 特殊情况，有些只到二级，例如专业-其他 -->
                                            <input
                                            v-if="second_2.children"
                                            class="btn-active"
                                            :class="{
                                                'has-select': handleSecondClass(second_2.k)
                                            }"
                                            name="openThirdKey"
                                            :value="second_2.k"
                                            v-model="secondOpenkey"
                                            type="radio"
                                            />
                                            <input
                                            v-else
                                            @focus="secondOpenkey = ''"
                                            @input="
                                                (e) => {
                                                handleSelect(e)
                                                }
                                            "
                                            class="btn-select"
                                            name="value"
                                            :value="second_2.k"
                                            v-model="value"
                                            type="checkbox"
                                            />
                                            <span>{{ second_2.v }}</span>
                                        </label>
                                        </div>
                                    </div>

                                    <div class="third-content">
                                        <div
                                        v-for="(second_2, second_index_2) in second_1.children"
                                        :key="second_index_2"
                                        >
                                        <div
                                            class="third-list"
                                            :class="[second_2.k === secondOpenkey ? 'column-4' : 'hidden']"
                                        >
                                            <div
                                            v-for="(third, third_index) in second_2.children"
                                            :key="third_index"
                                            class="third-item"
                                            >
                                            <label>
                                                <input
                                                @input="
                                                    (e) => {
                                                    handleSelect(e)
                                                    }
                                                "
                                                class="btn-select"
                                                name="value"
                                                :value="third.k"
                                                v-model="value"
                                                type="checkbox"
                                                />
                                                <span>{{ third.v }}</span>
                                            </label>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
                <div v-if="multiple" class="select-footer">
                    <el-button @click="submit" class="submit" type="primary" :size="size">确定</el-button>
                </div>
        </div>
        </el-dialog>
    </div>
`

const selectDialogComponent = {
    name: 'selectDialog',
    template: selectPanelTemplate,
    props: {
        /**
         * @ name
         * major 需求专业
         * area 地区
         * allNative 户籍国籍
         */
        name: {
            type: String,
            default: 'allNative'
        },
        /* 列表数据 */
        list: {
            type: Array,
            default: []
        },
        size: {
            type: String,
            default: 'default'
        },
        clearable: {
            type: Boolean,
            default: false
        },
        // 是否支持搜索
        remote: {
            type: Boolean,
            default: true
        },
        // 列数，三级联动需要后端配合
        column: {
            type: Number,
            default: 4
        },
        closeOnClickModal: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Function,
            default: () => false
        },
        title: {
            type: String,
            default: '请选择'
        },
        placeholder: {
            type: String,
            default: '请输入关键词'
        },
        searchPlaceholder: {
            type: String,
            default: '请输入关键词'
        },
        modelValue: [String, Number, Array],
        multiple: {
            type: Boolean,
            default: false
        },
        // 最多可以选择的个数
        multipleLimit: {
            type: Number,
            default: 1
        },
        // 返回结果是否需要','拼接，用于多选
        isJoin: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        realValue() {
            const { modelValue } = this
            let newValue
            if (Array.isArray(modelValue)) {
                newValue = modelValue
            } else {
                newValue = modelValue ? String(modelValue).split(',') : []
            }
            return newValue
        }
    },
    watch: {
        realValue: {
            handler(value) {
                this.value = value
                this.recursive('init')
            },
            immediate: true
        },
        value: {
            handler(value) {
                this.recursive()
            },
            deep: true
        },
        list: {
            handler(val) {
                this.getLastChildren(val)
            },
            deep: true
        }
    },
    data() {
        return {
            visible: false,
            searValue: '',
            // 不含标题二级k映射表
            secondMap: ['allNative', 'function', 'area', 'certificate', 'skillId', 'title', 'categoryJob'],

            // 不含标题三级k映射表
            thirdMap: ['major'],

            // 选中的值
            value: [],
            valueItems: [],
            valueText: '',

            // 一级索引
            selectFirstIndex: 0,

            // 展开三级
            secondOpenkey: '',

            // 真实一级标识
            firstMarkKey: [],

            // 真实二级标识
            secondMarkKey: [],

            // 最后子级，用于搜索
            lastChildrenOptions: [],

            // 搜索数据
            searchLoading: false,
            searchOptions: []
        }
    },
    mounted() {
        const { list } = this
        this.recursive('init')
    },
    methods: {
        getValueText() {
            const { valueItems } = this
            const labels = []

            valueItems.forEach((item) => labels.push(item.v))
            this.valueText = labels.join()
            return labels
        },

        initOpen(firstActiveIndex, secondOpenkey) {
            this.selectFirstIndex = firstActiveIndex
            this.secondOpenkey = secondOpenkey
        },

        // 获取父级标识
        getParentMark(data) {
            const { parentId, topParentId } = data
            this.secondMarkKey.push(parentId)
            this.firstMarkKey.push(topParentId)
        },

        // 清除父级标识
        clearParentMark() {
            this.firstMarkKey = []
            this.secondMarkKey = []
        },

        handleSecondClass(k) {
            const has = this.secondMarkKey.includes(k)
            return has
        },

        handleFirstClass(k) {
            const has = this.firstMarkKey.includes(k)
            return has
        },

        // 已选去重, 例如城市-热门城市包括城市北京，北京-北京
        getValueItems(arr) {
            const newArr = []
            const obj = {}
            const { length } = arr
            for (let i = 0; i < length; i += 1) {
                const item = arr[i]
                const { k } = item
                if (!obj[k]) {
                    newArr.push(item)
                    obj[k] = true
                }
            }
            this.valueItems = newArr
        },

        // 递归
        recursive(type = '') {
            const { length: optionsLength } = this.list
            if (!optionsLength) return

            const valueItems = []
            const { value, list } = this

            this.clearParentMark()

            let isFirst = false
            list.forEach((data, index) => {
                const arrayMap = (array, parent) => {
                    const { length } = array
                    for (let i = 0; i < length; i += 1) {
                        const item = array[i]
                        const { k, children } = item
                        if (value.includes(k)) {
                            valueItems.push(item)
                            if (!isFirst && type === 'init') {
                                isFirst = true
                                this.initOpen(index, parent.k)
                            }
                            this.getParentMark(item)
                        }
                        if (Array.isArray(children) && children.length) {
                            arrayMap(children, item)
                        }
                    }
                }
                const { length: childrenLength = 0 } = data.children
                if (childrenLength) {
                    arrayMap(data.children, data)
                }
            })
            this.getValueItems(valueItems)
            this.getValueText()
        },

        getLastChildren(arr) {
            const { length: optionsLength } = arr
            if (!optionsLength) return

            const lastChildren = []
            const arrayMap = (array) => {
                const { length } = array
                for (let i = 0; i < length; i += 1) {
                    const item = array[i]
                    const { children } = item

                    if (Array.isArray(children) && children.length) {
                        arrayMap(children)
                    } else {
                        lastChildren.push(item)
                    }
                }
            }
            arrayMap(arr)
            this.lastChildrenOptions = lastChildren
        },

        // 点击一级
        handleClickFirst(index) {
            this.selectFirstIndex = index
        },

        handleOpen() {
            this.visible = true
        },

        handleRemove(data) {
            const { valueItems, value } = this
            const { k } = data
            this.valueItems = valueItems.filter((item) => item.k !== k)
            this.value = value.filter((item) => item !== k)
        },

        handleChangeModelValue(val) {
            this.$nextTick(() => {
                this.$emit('update:modelValue', val)
                this.$emit('update', { label: this.getValueText(), value: val })
                this.visible = false
            })
        },

        // 处理筛选数据
        handleSearch(query) {
            if (query) {
                this.searchLoading = true
                const { lastChildrenOptions } = this
                setTimeout(() => {
                    const options = lastChildrenOptions.filter((item) => {
                        return item.v.toLowerCase().includes(query.toLowerCase())
                    })

                    const newArr = []
                    const searchOptions = []
                    options.forEach((item) => {
                        const { k } = item
                        if (!newArr.includes(k)) {
                            newArr.push(k)
                            searchOptions.push(item)
                        }
                    })
                    this.searchOptions = searchOptions
                    this.searchLoading = false
                }, 200)
            } else {
                this.searchOptions = []
            }
        },

        handleSearchChange(k) {
            const { multiple } = this

            this.searchOptions = []

            if (!multiple) {
                this.handleChangeModelValue(k)
            } else {
                const {
                    value,
                    value: { length }
                } = this
                const { multipleLimit } = this
                if (length >= multipleLimit) {
                    this.searValue = ''
                    ElementPlus.ElMessage.warning(`您最多选择${multipleLimit}项`)
                    return
                }
                const contain = value.includes(k)
                if (!contain) {
                    this.value.push(k)
                }
            }
            this.searValue = ''
        },

        // 选中最后一级
        handleSelect(e) {
            const {
                target: { value, checked }
            } = e
            const { multiple, multipleLimit } = this

            // 单选
            if (!multiple) {
                this.handleChangeModelValue(value)
            } else {
                const { length } = this.value
                if (checked && multipleLimit <= length) {
                    ElementPlus.ElMessage.warning(`您最多选择${multipleLimit}项`)
                    this.value = this.value.filter((k) => k !== value)
                }
            }
        },

        submit() {
            const { value } = this
            const { isJoin } = this
            this.handleChangeModelValue(isJoin ? value.join() : value)
        },

        // 关闭弹窗，还原真实值
        handleClose() {
            this.$refs.targetInput.blur()
            this.value = this.realValue
        },

        // 清空
        handleClear() {
            const { multiple } = this
            const val = multiple ? [] : ''
            this.handleChangeModelValue(val)
        }
    }
}
