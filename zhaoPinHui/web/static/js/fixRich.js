// 把width的宽度写到style里面去，并且去掉width
function fixRichImages() {
    const richImages = document.querySelectorAll('.rich-img')
    richImages.forEach((img) => {
        if (img.hasAttribute('width')) {
            const width = img.getAttribute('width')
            img.style.width = width
            img.removeAttribute('width')
        }
    })
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', fixRichImages)
