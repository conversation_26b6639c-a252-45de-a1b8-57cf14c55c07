var service = axios.create({
    withCredentials: true, // 跨域支持发送cookie
    timeout: 20000 // 请求超时时间
})

function httpGet(url, params = {}) {
    var apiUrl = config.baseUrl + url
    return new Promise(function (resolve, reject) {
        service
            .get(apiUrl, {
                params: params
            })
            .then(function (response) {
                const res = response.data

                if (res.result == 1) {
                    // 正常
                    if (res.msg != '') {
                        ElementPlus.ElMessage.success(res.msg)
                    }
                    resolve(res.data)
                } else {
                    if (res.code == 403) {
                        window.localStorage.clear()
                        window.sessionStorage.clear()
                        removeToken()
                        window.globalComponents.loginDialogComponent.showLoginDialog()
                        reject(res.data)
                        return
                    }
                    // 异常
                    if (res.msg != '') {
                        ElementPlus.ElMessage.error(res.msg)
                    }

                    if (res.data) {
                        reject(res.data)
                    } else {
                        reject(res.msg)
                    }
                }
            })
            .catch(function (error) {
                if (error.message.indexOf('timeout') != -1) {
                    ElementPlus.ElMessage.error('网络超时')
                } else if (error.message == 'Network Error') {
                    ElementPlus.ElMessage.error('网络连接错误')
                } else {
                    if (error.response.data) ElementPlus.ElMessage.error(error.response.statusText)
                    else ElementPlus.ElMessage.error('接口路径找不到')
                }
                reject(error)
            })
    })
}

function httpPost(url, params = {}) {
    var apiUrl = config.baseUrl + url

    return new Promise(function (resolve, reject) {
        service({
            headers: {},
            transformRequest: [
                function (data) {
                    // 在请求之前对data传参进行格式转换
                    var formData = new FormData()
                    if (data) {
                        formData = Qs.stringify(data)
                    }
                    return formData
                }
            ],
            url: apiUrl,
            method: 'post',
            data: params
        })
            .then(function (response) {
                const res = response.data

                if (res.result == 1) {
                    // 正常
                    if (res.msg != '') {
                        ElementPlus.ElMessage.success(res.msg)
                    }
                    resolve(res.data)
                } else {
                    if (res.code == 403) {
                        window.localStorage.clear()
                        window.sessionStorage.clear()
                        removeToken()
                        window.globalComponents.loginDialogComponent.showLoginDialog()
                        return
                    }
                    // 简历未完善
                    if (res.code == 301) {
                        ElementPlus.ElMessageBox.confirm('当前简历信息不完善，请先完善简历', '温馨提示', {
                            cancelButtonText: '稍后操作',
                            confirmButtonText: '去完善',
                            buttonSize: 'large'
                        })
                            .then(() => {
                                const base = '/member/person'
                                const stepRouter = {
                                    1: '/required/basic',
                                    2: '/required/education',
                                    3: '/required/intention'
                                }
                                window.location.href = base + stepRouter[res.data] + '?redirect=' + encodeURIComponent(window.location.href)
                            })
                            .catch(() => { })
                        return
                    }
                    // 异常
                    if (res.msg != '') {
                        ElementPlus.ElMessage.error(res.msg)
                    }

                    if (res.data) {
                        reject(res.data)
                    } else {
                        reject(res.msg)
                    }
                }
            })
            .catch(function (error) {
                if (error.message.indexOf('timeout') != -1) {
                    ElementPlus.ElMessage.error('网络超时')
                } else if (error.message == 'Network Error') {
                    ElementPlus.ElMessage.error('网络连接错误')
                } else {
                    if (error.response && error.response.data) ElementPlus.ElMessage.error(error.response.statusText)
                    else ElementPlus.ElMessage.error('系统出错了,请联系管理员')
                }
                reject(error)
            })
    })
}
