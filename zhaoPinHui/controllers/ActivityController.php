<?php
namespace zhaoPinHui\controllers;

use common\base\models\BaseHwActivity;
use common\components\MessageException;
use common\helpers\FormatConverter;
use common\service\CommonService;
use Yii;
use common\service\zhaoPinHuiColumn\ActivityService;
use yii\base\Exception;

/**
 * create user：shannon
 * create time：2025/2/11 下午7:18
 */
class ActivityController extends BaseZhaoPinHuiController
{
    /**
     * 活动详情页
     */
    public function actionDetail()
    {
        $activityLink = Yii::$app->request->get('code');
        if (!$activityLink) {
            //去404
            $this->notFound();
        }
        $activityId = BaseHwActivity::getActivityIdByActivityLinkOne($activityLink);
        if (!$activityId) {
            //去404
            $this->notFound();
        }
        $data                                                 = ActivityService::getInstance()
            ->setPlatform(CommonService::PLATFORM_ZHAOPINHUI)
            ->runDetail([
                'activityId' => $activityId,
                'pageSize'   => 18,
            ]);
        $data['companyParticipatingList']['dataList']         = FormatConverter::convertLine($data['companyParticipatingList']['dataList']);
        $data['companyParticipatingList']['dataList']['list'] = $this->renderPartial('/special-activity/company_index_item.html',
            [
                'list'       => $data['companyParticipatingList']['dataList']['list'],
                'activityId' => $data['activityInfo']['id'],
            ]);

        // 去掉html 标签并且截取100个字符
        $descriptionContent = strip_tags($data['activityInfo']['activityDetail']);
        // 去掉换行
        $descriptionContent = str_replace(PHP_EOL, '', $descriptionContent);
        $descriptionContent = mb_substr($descriptionContent, 0, 120, 'utf-8');
        $this->setSeo([
            'title'       => $data['activityInfo']['name'] . '-高校人才网|高才网',
            'keywords'    => $data['activityInfo']['name'] . ',高校人才网',
            'description' => $descriptionContent,
        ]);
        $data['miniShareLinkUrl'] = BaseHwActivity::getMiniShareLinkUrl($data['activityInfo']['id']);

        return $this->render('detail.html', $data);
    }

    /**
     * 参会单位列表
     */
    public function actionGetCompanyList()
    {
        try {
            $params       = Yii::$app->request->get();
            $list         = ActivityService::getInstance()
                ->setPlatform(CommonService::PLATFORM_ZHAOPINHUI)
                ->runCompanyList($params);
            $list         = FormatConverter::convertLine($list);
            $list['list'] = $this->renderPartial('/special-activity/company_index_item.html', [
                'list'       => $list['list'],
                'activityId' => $params['activityId'],
            ]);

            return $this->success($list);
        } catch (MessageException $e) {
            return $this->fail($e->getMessage());
        }
    }
}