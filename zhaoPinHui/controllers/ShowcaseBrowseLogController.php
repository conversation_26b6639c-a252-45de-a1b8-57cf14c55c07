<?php
namespace zhao<PERSON>inHui\controllers;

use common\base\models\BaseBuriedPointLog;
use common\base\models\BaseShowcaseBrowseLog;
use common\helpers\FormatConverter;
use Yii;
use yii\db\Exception;

class ShowcaseBrowseLogController extends BaseZhaoPinHuiController
{
    /**
     * 新增广告统计数据
     */
    public function actionAddShowcaseBrowseLog()
    {
        $request = Yii::$app->request->get();
        unset($request['/1.gif']);

        $token = $request['token'];

        if (!$token) {
            $this->response();
        }

        if (Yii::$app->params['showcaseBrowse']['token'] != $token) {
            $this->response();
        }

        $useragent = Yii::$app->request->headers['user-agent'] ?: '';
        // 这里过滤一下,价格token
        try {
            BaseShowcaseBrowseLog::addShowcaseBrowseLog(FormatConverter::convertHump($request), $useragent);

            $this->response();
        } catch (\Exception $e) {
            $this->response();
        }
    }

    /**
     * 新增用户埋点数据日志
     * @return void
     */
    public function actionAddBuriedPointLog()
    {
        $request = Yii::$app->request->get();
        unset($request['/2.gif']);

        try {
            BaseBuriedPointLog::create($request);

            $this->response();
        } catch (Exception $e) {
            $this->response();
        }
    }
}