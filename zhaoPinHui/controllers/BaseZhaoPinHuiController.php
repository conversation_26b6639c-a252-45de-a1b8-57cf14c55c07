<?php
namespace zhao<PERSON>inHui\controllers;

use common\base\BaseController;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\libs\Cache;
use Yii;
use yii\web\Response;

class BaseZhaoPinHuiController extends BaseController
{

    public $memberId;
    public $token;

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }
        $url = urldecode(Yii::$app->request->url);

        if ($url != '/' && $url != rtrim($url, '/')) {
            //去掉？后面的参数
            $url = rtrim($url, '/');
            $this->redirect($url, 301);
        }

        // 登录
        if (!Yii::$app->user->isGuest) {
            $model = new BaseMemberLoginForm();
            $model->setMemberInfo(true);
            $user                     = BaseMember::getLoginInfo();
            Yii::$app->params['user'] = $user;
            $this->setActive();
        }

        if (in_array($action->uniqueID, $this->ignoreLogin())) {
            return parent::beforeAction($action);
        }

        // 非登录
        if (Yii::$app->user->isGuest) {
            $token = \Yii::$app->request->getHeaders()
                ->get('token');
            if (!$token) {
                header('Content-Type: application/json');
                $ret = [
                    'code'   => 403,
                    'result' => 0,
                    'msg'    => '请您先进行登录后再操作',
                ];
                echo json_encode($ret);
                exit;
            } else {
                $jwtAuth  = new \common\libs\JwtAuth();
                $memberId = $jwtAuth->checkToken($token);

                if ($memberId) {
                    $this->token    = $token;
                    $this->memberId = $memberId;

                    (new BaseMemberLoginForm())->loginById($memberId);

                    $this->setActive();
                }
            }
        }

        return parent::beforeAction($action);
    }

    /**
     * 无需登录就可以操作的控制器
     * @return string[]
     */
    public function ignoreLogin()
    {
        return [
            'home/index',
            'home/get-activity-list',
            'home/load-guess-like',
            'login/get-captcha-config',
            'login/account-login',
            'login/validate-mobile-login-code',
            'login/send-mobile-login-code',
            'login/check-mini-login-qrcode',
            'login/get-mini-login-qrcode',
            'login/add-login-pop-tips-amount',
            'config/load-country-mobile-code',
            'activity/detail',
            'activity/get-list',
            'showcase-browse-log/add-buried-point-log',
            'showcase-browse-log/add-showcase-browse-log',
            'activity/special-activity-index',
            'activity/detail',
            'activity/detail',
            'activity/get-company-list',
            'special-activity/index',
            'special-activity/get-company-list',
            'special-activity/get-search-params',
        ];
    }

    public function notFound()
    {
        Yii::$app->response->setStatusCode(404)
            ->send();
        echo $this->renderPartial('/home/<USER>');
        exit();
    }

    public function setActive()
    {
        // // 做一个简单的测试,把用户的id和现在的时间保存到缓存里面去,一段时间后再取出来,用于更新用户的活跃时间
        $userId   = Yii::$app->user->id;
        $actionId = Yii::$app->controller->action->uniqueId;

        if ($userId && $actionId) {
            $key  = Cache::ALL_RESUME_ACTION_CONTROLLER_KEY;
            $time = CUR_TIMESTAMP;
            // 写集合
            Cache::zadd($key, $time, $userId);
        }
    }

    public function response()
    {
        if (Yii::$app->has('response')) {
            $response          = Yii::$app->getResponse();
            $response->isSent  = false;
            $response->stream  = null;
            $response->data    = null;
            $response->content = null;
        } else {
            $response = new Response();
        }
        $response->statusCode = 200;
        $response->format     = Response::FORMAT_JSON;

        $response->send();
        exit;
    }

    /**
     * title
     * keywords
     * description
     * Copyright
     *
     * @param $data
     */
    public function setSeo($data = [])
    {
        // 避免有些页面没有
        $config = Yii::$app->params['seo']['default'];

        // 特殊设置,找到当前的控制器
        $actionId = Yii::$app->controller->action->uniqueId;

        $pcConfig = Yii::$app->params['seo']['pc']['specialRoute'];

        if ($pcConfig && isset($pcConfig[$actionId])) {
            $config = array_merge($config, $pcConfig[$actionId]);
        }

        $this->getView()->title = $data['title'] ?: $config["title"];

        $keywordsFlag = 'keywords'; //关键点是这个标记！！！
        $this->view->registerMetaTag([
            'name'    => 'keywords',
            'content' => $data['keywords'] ?: $config["keywords"],
        ], $keywordsFlag);

        $descFlag = 'desc'; //关键点也是这个标记！！！
        $this->view->registerMetaTag([
            'name'    => 'description',
            'content' => $data['description'] ?: $config["description"],
        ], $descFlag);

        $descFlag = 'Copyright'; //关键点也是这个标记！！！
        $this->view->registerMetaTag([
            'name'    => 'Copyright',
            'content' => $config["Copyright"],
        ], $descFlag);
    }

    /**
     * 获取简历ID
     * @return mixed
     */
    public function getResumeId()
    {
        $memberId = Yii::$app->user->id;

        if ($memberId) {
            return BaseResume::findOneVal(['member_id' => $memberId], 'id');
        }

        return 0;
    }
}