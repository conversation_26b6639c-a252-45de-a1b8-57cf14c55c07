<?php
/**
 * create user：shannon
 * create time：2025/1/20 上午11:00
 */
namespace zhaoPinHui\controllers;

use common\components\MessageException;
use Yii;
use common\service\CommonService;
use common\service\zhaoPinHuiColumn\HomeService;

class HomeController extends BaseZhaoPinHuiController
{
    /**
     * 活动汇总页面
     * @return string
     * @throws \common\components\MessageException
     */
    public function actionIndex()
    {
        $data                         = (new HomeService())->setPlatform(CommonService::PLATFORM_ZHAOPINHUI)
            ->run(Yii::$app->request->get());
        $activityListHtml             = $this->renderPartial('activity_item.html', $data);
        $data['activityList']['html'] = $activityListHtml;
        $this->setSeo($data['seo']);

        return $this->render('index.html', $data);
    }

    /**
     * 活动分页列表
     */
    public function actionGetActivityList()
    {
        try {
            $params                       = Yii::$app->request->get();
            $data                         = (new HomeService())->setPlatform(CommonService::PLATFORM_ZHAOPINHUI)
                ->runPcList($params);
            $activityListHtml             = $this->renderPartial('activity_item.html', $data);
            $data['activityList']['html'] = $activityListHtml;

            return $this->success($data);
        } catch (MessageException $e) {
            return $this->fail($e->getMessage());
        }
    }
}