<?php

namespace zhaoPinHui\controllers;

use common\helpers\ArrayHelper;
use common\helpers\FormatConverter;
use common\service\zhaoPinHuiColumn\BaseService;
use queue\Producer;
use Yii;
use zhaoPinHui\models\SpecialActivity;

/**
 * 专场类
 * Class ActivityController
 */
class SpecialActivityController extends BaseZhaoPinHuiController
{
    /**
     * 活动专场页面
     */
    public function actionIndex()
    {
        $code = \Yii::$app->request->get('code');
        $data = SpecialActivity::indexPage($code, $this->getResumeId());
        if (empty($data)) {
            $this->notFound();
        }

        $data['companyList']['list'] = $this->renderPartial('company_index_item.html', [
            'list'       => $data['companyList']['list'],
            'activityId' => 0,
        ]);
        // 去掉html 标签并且截取100个字符
        $descriptionContent = strip_tags($data['detail']['activityDetail']);
        // 去掉换行
        $descriptionContent = str_replace(PHP_EOL, '', $descriptionContent);
        $descriptionContent = mb_substr($descriptionContent, 0, 120, 'utf-8');
        $this->setSeo([
            'title'       => $data['detail']['name'] . '-高校人才网|高才网',
            'keywords'    => $data['detail']['name'] . ',高校人才网',
            'description' => $descriptionContent,
        ]);

        return $this->render('index.html', $data);
    }

    /**
     * 活动专场单位详情页面
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCompanyList()
    {
        $params       = \Yii::$app->request->get();
        $list         = SpecialActivity::getActivityCompanyList($params, $this->getResumeId());
        $list         = FormatConverter::convertLine($list);
        $list['list'] = $this->renderPartial('company_index_item.html', [
            'list'       => $list['list'],
            'activityId' => $params['activityId'] ?? 0,
        ]);

        return $this->success(ArrayHelper::intToString($list));
    }

    /**
     * 活动专场参会单位筛选列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSearchParams()
    {
        $params                         = \Yii::$app->request->get();
        $searchParams                   = SpecialActivity::getSearchParams($params, $this->getResumeId());
        $searchParams['companyTabList'] = $this->renderPartial('company_activity_tab_item.html', [
            'companyTabList' => $searchParams['companyTabList'],
            'activityId'     => $params['activityId'] ?? 0,
        ]);

        return $this->success(ArrayHelper::intToString($searchParams));
    }
}