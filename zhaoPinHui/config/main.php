<?php
$params = array_merge(require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php', require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php');

return [
    'id'                  => 'app-zhaoPinHui',
    'basePath'            => dirname(__DIR__),
    'controllerNamespace' => 'zhaoPinHui\controllers',
    'bootstrap'           => ['log'],
    'defaultRoute'        => 'home',
    'components'          => [
        'request' => [
            'csrfParam' => '_csrf-zhaoPinHui',
        ],
        'session' => [
            'name'    => '_identity',
            'timeout' => 3600,
        ],

        'user'         => [
            'class'           => 'yii\web\User',
            'identityClass'   => 'common\base\models\BaseMember',
            'enableAutoLogin' => true,
            'identityCookie'  => [
                'name'     => '_identity',
                'httpOnly' => true,
            ],
            'idParam'         => '__member',
        ],
        'errorHandler' => [
            'errorAction' => 'home\error',
        ],
        'urlManager'   => [
            'enablePrettyUrl' => true,
            'showScriptName'  => false,
            'rules'           => [
                'xianshang/<code:[a-z0-9]+>.html'     => 'activity/detail',
                'xianxia/<code:[a-z0-9]+>.html'       => 'activity/detail',
                'zhuanchang/<code:[a-zA-Z0-9]+>.html' => 'special-activity/index',
                //汇总页面
                '/'                                   => 'home/index',
                'yincai/?'                            => 'home/index',
                'yincai/<areaKey:[a-z]*>/?'           => 'home/index',
                // 汇总页面--局部加载
                'yincaipage/?'                        => 'home/get-activity-list',
                'yincaipage/<areaKey:[a-z]*>/?'       => 'home/get-activity-list',

            ],
        ],
    ],
    'params'              => $params,
];
