<?php
/**
 * create user：伍彦川
 * create time：2025/2/12 14:23
 */
namespace zhaoPinHui\models;

use common\base\BaseActiveRecord;
use common\base\models\BaseHwSpecialActivity;
use common\helpers\FormatConverter;
use common\service\zhaoPinHuiColumn\SpecialActivityService;

class SpecialActivity extends BaseActiveRecord
{
    /**
     * 获取参会单位列表
     * @param $params
     * @return array
     * @throws \common\components\MessageException
     */
    public static function getActivityCompanyList($params, $resumeId): array
    {
        return SpecialActivityService::getActivityCompany($params, $resumeId);
    }

    /**
     * 获取参会单位列表查询列表
     * @param $params
     * @param $resumeId
     * @return array
     * @throws \common\components\MessageException
     */
    public static function getSearchParams($params, $resumeId): array
    {
        return SpecialActivityService::getActivityCompanySearchParams($params, $resumeId);
    }

    public static function indexPage($code, $resumeId): array
    {
        $specialActivity = BaseHwSpecialActivity::find()
            ->where(['special_link' => $code])
            ->asArray()
            ->one();
        if (empty($specialActivity)) {
            return [];
        }

        // 详情
        $detail = SpecialActivityService::getSpecialActivityDetail($specialActivity['id'], $resumeId, $specialActivity);

        // 活动场次
        $relationActivity = SpecialActivityService::getSpecialActivityRelationActivity($specialActivity['id'],
            $resumeId);

        // 单位tab
        $companyTabList = SpecialActivityService::getActivityCompanySearchActivityTab($specialActivity['id']);

        // 单位列表
        $companyList = SpecialActivityService::getActivityCompany([
            'specialActivityId' => $specialActivity['id'],
        ]);

        // 单位筛选项目和当前所选
        $companySearchParams = SpecialActivityService::getActivityCompanySearchParams([
            'specialActivityId' => $specialActivity['id'],
        ], $resumeId);

        return FormatConverter::convertLine([
            'detail'              => $detail,
            'relationActivity'    => $relationActivity,
            'companyTabList'      => $companyTabList,
            'companyList'         => $companyList,
            'companySearchParams' => $companySearchParams,
            'shareLink'           => SpecialActivityService::getMiniShareLinkUrl($detail['id']),
        ]);
    }
}