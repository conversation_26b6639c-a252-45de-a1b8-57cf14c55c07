<?php
/**
 * create user：伍彦川
 * create time：2025/2/6 11:46
 */
namespace zhaoPinHui\models;

use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseDictionary;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseHwActivityCompany;
use common\base\models\BaseHwSpecialActivity;
use common\base\models\BaseHwSpecialActivityRelation;
use common\base\models\BaseJob;
use common\base\models\BaseJobCategoryRelation;
use common\base\models\BaseJobMajorRelation;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\StringHelper;
use common\libs\Cache;
use common\service\zhaoPinHuiColumn\ActivityService;
use common\service\zhaoPinHuiColumn\SpecialActivityService;

class Activity extends BaseActiveRecord
{

}