<?php

namespace zhaoPinHui\components;

use Yii;
use yii\web\ErrorHandler;
use yii\web\Response;

class CommonErrorHandler extends <PERSON>rrorHandler
{
    protected function renderException($exception)
    {
        $statusCode = $exception->statusCode;

        if ($statusCode == '404') {
            // 这里无奈做一些特殊处理

            if (!Yii::$app->request->isAjax) {
                //返回的状态码

                Yii::$app->response->setStatusCode(404)
                    ->send();
                echo Yii::$app->view->renderFile('@app/views/home/<USER>', ['message' => '这里是404页面']);
                exit;
            }
        }

        $code          = $exception->getCode();
        $messages      = $exception->getMessage();
        $file          = $exception->getFile();
        $line          = $exception->getLine();
        $previous      = $exception->getPrevious();
        $traceAsString = $exception->getTraceAsString();
        if (Yii::$app->has('response')) {
            $response          = Yii::$app->getResponse();
            $response->isSent  = false;
            $response->stream  = null;
            $response->data    = null;
            $response->content = null;
        } else {
            $response = new Response();
        }

        if (Yii::$app->params['environment'] == 'prod') {
            $ret = [
                'msg'    => $messages,
                'result' => 0,
                'data'   => $code,
                'code'   => $statusCode,
            ];
        } else {
            $ret = [
                'msg'           => $messages,
                'result'        => 0,
                'data'          => $code,
                'file'          => $file,
                'line'          => $line,
                'traceAsString' => $traceAsString,
                'previous'      => $previous,
                'code'          => $statusCode,
            ];
        }

        $response->data   = $ret;
        $response->format = \yii\web\Response::FORMAT_JSON;

        $response->send();
    }
}