<?php
/**
 * create user：shannon
 * create time：2024/9/29 下午4:08
 */
namespace zhaoPinHui\components;

use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\helpers\UrlHelper;
use Yii;

class HeadWidget extends BaseWidget
{
    const ROUTE_URl = [
        'left'  => [
            [
                'url'    => '/',
                'name'   => '招聘会首页',
                'active' => '',
                'target' => '_blank',
            ],
            [
                // 'url'    => 'https://www.gaoxiaojob.com/zhaopin/zt/yca_quanguoxunhui2025/index.html',
                // https://zentao.jugaocai.com/index.php?m=story&f=view&id=1162
                'url'    => 'https://zhaopinhui.gaoxiaojob.com/xianxia/gxrcw2025szxgbsbshkjrcfh.html',
                'name'   => '全国巡回现场招聘会',
                'active' => '',
                'target' => '_blank',
            ],
            [
                // https://zentao.jugaocai.com/index.php?m=story&f=view&id=1172
                // 'url'    => 'https://zhaopinhui.gaoxiaojob.com/xianshang/rpoxsmshdsc.html',
                // https://zentao.jugaocai.com/index.php?m=story&f=storyView&storyID=1197
                'url'    => 'https://zhaopinhui.gaoxiaojob.com/xianshang/2025nbsjzgjrcrpoxsmshdsc.html',
                'name'   => '2025RPO线上面试会',
                'active' => '',
                'target' => '_blank',
            ],
            [
                'url'    => 'https://haiwai.gaoxiaojob.com/chuhai',
                'name'   => '出海引才',
                'active' => '',
                'target' => '_blank',
            ],
            [
                'url'    => 'https://haiwai.gaoxiaojob.com/guiguo',
                'name'   => '归国活动',
                'active' => '',
                'target' => '_blank',
            ],
        ],
        'right' => [
            [
                'url'    => 'https://www.gaoxiaojob.com/member/company/applyCooperation',
                'active' => '',
                'name'   => '合作申请',
                'rel'    => 'nofollow',
                'target' => '_blank',
            ],
        ],
    ];

    public function run()
    {
        //是否登录
        $isLogin   = !Yii::$app->user->isGuest && Yii::$app->user->identity->type == BaseMember::TYPE_PERSON;
        $avatar    = '';
        $avatarUrl = '';
        if ($isLogin) {
            $userInfo         = BaseMember::findOne(Yii::$app->user->id);
            $resumeInfo       = BaseResume::findOne(['member_id' => $userInfo->id]);
            $resumeStepNumber = BaseResumeComplete::getResumeStep($resumeInfo->id);
            $avatar           = BaseMemberLoginForm::getAvatar($userInfo->avatar, $resumeInfo->gender);
            $avatarUrl        = UrlHelper::createPcResumeStepUrl($resumeStepNumber);
        }
        $leftRoute = self::ROUTE_URl['left'];
        if (Yii::$app->controller->action->uniqueId == 'home/index') {
            $leftRoute[0]['active'] = 'active';
        }

        return $this->render('head_widget.html', [
            'leftRoute'  => $leftRoute,
            'rightRoute' => self::ROUTE_URl['right'],
            'isLogin'    => $isLogin,
            'avatar'     => $avatar,
            'avatarUrl'  => $avatarUrl,

        ]);
    }
}