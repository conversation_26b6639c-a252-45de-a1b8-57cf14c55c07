<?php
namespace zhao<PERSON>inHui\components;

use yii\base\Widget;

class BaseWidget extends Widget
{

    public function beforeRun()
    {
        // 判断html的head是否已经加载了
        // if (!HEAD_COMPONENT_LOADED) {
        //     sleep(1);
        //     // Yii::$app->view->beginPage();
        // }
        //
        // Yii::$app->getView()
        //     ->registerJsFile('//img.gaoxiaojob.com/uploads/static/lib/jquery/jquery.min.js',
        //         ['position' => \yii\web\View::POS_HEAD]);

        // Yii::$app->getView()
        //     ->registerJsFile('//img.gaoxiaojob.com/uploads/static/lib/vue/vue.min.js',
        //         ['position' => \yii\web\View::POS_HEAD]);

        return parent::beforeRun();
    }
}