# CLAUDE.md

本文件为Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

这是一个基于Yii2高级模板构建的多端后端系统，为高校人才网平台提供服务。系统包含多个应用程序，服务于不同类型的用户，包括PC前端、移动H5、后台管理、小程序和API接口。

## 架构说明

### 多应用结构
项目遵循Yii2高级模板架构，包含以下主要应用：

- **admin/**: 后台管理面板，用于系统管理
- **api/**: RESTful API接口
- **frontendPc/**: PC端网页前端
- **h5/**: 移动端H5前端
- **miniApp/**: 微信小程序后端
- **companyH5/**: 企业专用H5界面
- **boShiHou/**: 博士后专用界面
- **zhaoPinHui/**: 招聘会界面
- **haiWai/**: 海外人才界面
- **common/**: 共享代码（模型、组件、库、服务、辅助函数）
- **console/**: 控制台命令和数据迁移
- **queue/**: 异步处理队列任务类
- **timer/**: 定时任务脚本

### 核心组件

- **模型层**: 共享实体位于`common/models/`，各应用特定模型在各自的`models/`文件夹中
- **队列系统**: 大量使用基于Redis的队列进行异步处理（短信、邮件、统计等）
- **服务层**: 业务逻辑位于`common/service/`
- **辅助函数**: 工具函数位于`common/helpers/`
- **第三方库**: 外部服务集成位于`common/libs/`

## 开发命令

### 控制台命令
```bash
# 主控制台入口
./yii <命令>

# 测试环境控制台
./yii_test <命令>

# 检查系统需求
php requirements.php
```

### Composer命令
```bash
# 安装依赖
composer install

# 更新依赖
composer update
```

### 环境设置
```bash
# 初始化环境（创建配置文件）
./init
```

## 关键配置文件

- **composer.json**: PHP依赖和项目元数据
- **common/config/main.php**: 核心应用配置，包含队列引导
- **{app}/config/main.php**: 应用特定配置
- **{app}/config/main-local.php**: 本地环境覆盖配置

## 数据库和队列系统

系统使用MySQL作为主数据存储，Redis用于队列管理。为不同类型的后台任务配置了多个队列通道：

- smsQueue: 短信通知
- emailQueue (1-5): 多通道邮件处理
- meilisearchQueue: 搜索索引更新
- companyJobMatchPersonQueue: 职位匹配算法
- afterAnnouncementUpdateJobQueue: 公告更新后处理

## 常见开发模式

### 模型结构
- 基础模型在`common/models/`
- ActiveRecord扩展在`common/base/BaseActiveRecord.php`
- Elasticsearch模型在`common/es/`

### 控制器架构
- 各应用目录下的基础控制器
- 通用功能在`common/base/BaseController.php`
- 错误处理通过`CommonErrorHandler`

### 队列处理
- 任务类在`queue/`目录
- 队列配置在`common/config/main.php`
- 基于Redis的多通道队列系统

## 文件结构说明

- **uploads/**: 文件上传存储
- **resume/**: 简历相关文件
- **document/**: 项目文档
- **vendor/**: Composer依赖
- **runtime/**: 应用运行时文件（每个应用独立）
- **web/**: 网页可访问文件（每个应用独立）

## 重要集成库

项目集成了众多第三方服务：
- 微信生态系统（公众号、小程序、企业微信）
- 支付系统
- 短信服务（阿里云、腾讯云）
- 搜索服务（Meilisearch）
- 文件存储（七牛、阿里云OSS）
- PDF生成（mPDF、TCPDF）
- AI服务（OpenAI、阿里云）

## 开发注意事项

### 代码规范
- 遵循PSR-4自动加载标准
- 使用Yii2框架约定
- 数据库操作优先使用ActiveRecord
- 异步任务使用队列系统

### 测试和部署
- 使用`requirements.php`检查环境要求
- 通过`init`脚本初始化不同环境
- 队列进程需要独立启动和监控

### 安全考虑
- 敏感配置使用`*-local.php`文件
- 文件上传有安全验证
- 用户输入进行过滤和验证