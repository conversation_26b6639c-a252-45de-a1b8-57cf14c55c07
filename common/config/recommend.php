<?php

/**
 * 推荐服务配置文件
 * 用于控制推荐服务的版本切换和相关参数
 */

return [
    // 推荐服务版本控制
    'version' => [
        // 职位推荐服务版本 (v1|v2)
        'job' => 'v2',
        
        // 公告推荐服务版本 (v1|v2)
        'announcement' => 'v2',
        
        // 单位推荐服务版本 (v1|v2)
        'company' => 'v2',
    ],
    
    // 推荐数量配置
    'limits' => [
        // 职位推荐默认数量
        'job' => 8,
        
        // 公告推荐默认数量
        'announcement' => 8,
        
        // 单位推荐默认数量
        'company' => 10,
    ],
    
    // 性能监控配置
    'monitoring' => [
        // 是否启用性能监控
        'enabled' => true,
        
        // 慢查询阈值（秒）
        'slow_query_threshold' => 1.0,
        
        // 是否记录查询日志
        'log_queries' => true,
    ],
    
    // 缓存配置
    'cache' => [
        // 单位推荐缓存时间（秒）
        'company_recommend_ttl' => 48 * 3600, // 48小时
        
        // 是否启用缓存
        'enabled' => true,
    ],
    
    // 降级策略配置
    'fallback' => [
        // 是否启用自动降级
        'auto_fallback' => true,
        
        // 错误率阈值，超过此值自动降级到v1
        'error_rate_threshold' => 0.1, // 10%
        
        // 响应时间阈值，超过此值自动降级到v1（秒）
        'response_time_threshold' => 2.0,
    ],
];
