<?php

namespace common\service\recommend;

use common\service\job\RecommendService as JobRecommendServiceV1;
use common\service\job\RecommendServiceV2 as JobRecommendServiceV2;
use common\service\announcement\RecommendService as AnnouncementRecommendServiceV1;
use common\service\announcement\RecommendServiceV2 as AnnouncementRecommendServiceV2;
use common\service\company\RecommendService as CompanyRecommendServiceV1;
use common\service\company\RecommendServiceV2 as CompanyRecommendServiceV2;
use Yii;

/**
 * 推荐服务工厂类
 * 统一管理推荐服务的版本切换和降级策略
 */
class RecommendServiceFactory
{
    // 服务类型常量
    const TYPE_JOB = 'job';
    const TYPE_ANNOUNCEMENT = 'announcement';
    const TYPE_COMPANY = 'company';
    
    // 版本常量
    const VERSION_V1 = 'v1';
    const VERSION_V2 = 'v2';
    
    /**
     * 获取职位推荐服务实例
     * @return JobRecommendServiceV1|JobRecommendServiceV2
     */
    public static function createJobRecommendService()
    {
        $version = self::getServiceVersion(self::TYPE_JOB);
        
        if ($version === self::VERSION_V2) {
            return new JobRecommendServiceV2();
        } else {
            return new JobRecommendServiceV1();
        }
    }
    
    /**
     * 获取公告推荐服务实例
     * @return AnnouncementRecommendServiceV1|AnnouncementRecommendServiceV2
     */
    public static function createAnnouncementRecommendService()
    {
        $version = self::getServiceVersion(self::TYPE_ANNOUNCEMENT);
        
        if ($version === self::VERSION_V2) {
            return new AnnouncementRecommendServiceV2();
        } else {
            return new AnnouncementRecommendServiceV1();
        }
    }
    
    /**
     * 获取单位推荐服务实例
     * @return CompanyRecommendServiceV1|CompanyRecommendServiceV2
     */
    public static function createCompanyRecommendService()
    {
        $version = self::getServiceVersion(self::TYPE_COMPANY);
        
        if ($version === self::VERSION_V2) {
            return new CompanyRecommendServiceV2();
        } else {
            return new CompanyRecommendServiceV1();
        }
    }
    
    /**
     * 获取服务版本
     * @param string $type 服务类型
     * @return string 版本号
     */
    private static function getServiceVersion($type)
    {
        // 从配置文件获取版本设置
        $config = self::getRecommendConfig();
        $configVersion = $config['version'][$type] ?? self::VERSION_V1;
        
        // 检查是否需要降级
        if (self::shouldFallback($type)) {
            Yii::warning("推荐服务{$type}因性能问题降级到V1", 'recommend');
            return self::VERSION_V1;
        }
        
        return $configVersion;
    }
    
    /**
     * 检查是否需要降级到V1
     * @param string $type 服务类型
     * @return bool
     */
    private static function shouldFallback($type)
    {
        $config = self::getRecommendConfig();
        
        // 如果未启用自动降级，直接返回false
        if (!($config['fallback']['auto_fallback'] ?? false)) {
            return false;
        }
        
        // 检查错误率
        $errorRate = self::getErrorRate($type);
        $errorThreshold = $config['fallback']['error_rate_threshold'] ?? 0.1;
        
        if ($errorRate > $errorThreshold) {
            return true;
        }
        
        // 检查响应时间
        $avgResponseTime = self::getAverageResponseTime($type);
        $timeThreshold = $config['fallback']['response_time_threshold'] ?? 2.0;
        
        if ($avgResponseTime > $timeThreshold) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取错误率
     * @param string $type 服务类型
     * @return float
     */
    private static function getErrorRate($type)
    {
        // 这里可以从Redis或其他监控系统获取错误率
        // 暂时返回0，实际项目中需要实现具体的监控逻辑
        return 0.0;
    }
    
    /**
     * 获取平均响应时间
     * @param string $type 服务类型
     * @return float
     */
    private static function getAverageResponseTime($type)
    {
        // 这里可以从Redis或其他监控系统获取平均响应时间
        // 暂时返回0，实际项目中需要实现具体的监控逻辑
        return 0.0;
    }
    
    /**
     * 获取推荐配置
     * @return array
     */
    private static function getRecommendConfig()
    {
        static $config = null;
        
        if ($config === null) {
            $configFile = Yii::getAlias('@common/config/recommend.php');
            if (file_exists($configFile)) {
                $config = require $configFile;
            } else {
                // 默认配置
                $config = [
                    'version' => [
                        'job' => self::VERSION_V2,
                        'announcement' => self::VERSION_V2,
                        'company' => self::VERSION_V2,
                    ],
                    'fallback' => [
                        'auto_fallback' => false,
                        'error_rate_threshold' => 0.1,
                        'response_time_threshold' => 2.0,
                    ],
                ];
            }
        }
        
        return $config;
    }
    
    /**
     * 记录性能指标
     * @param string $type 服务类型
     * @param string $version 版本
     * @param float $responseTime 响应时间
     * @param bool $success 是否成功
     */
    public static function recordMetrics($type, $version, $responseTime, $success)
    {
        $config = self::getRecommendConfig();
        
        if (!($config['monitoring']['enabled'] ?? false)) {
            return;
        }
        
        // 记录慢查询
        $slowThreshold = $config['monitoring']['slow_query_threshold'] ?? 1.0;
        if ($responseTime > $slowThreshold) {
            Yii::warning("推荐服务{$type}({$version})慢查询: {$responseTime}s", 'recommend');
        }
        
        // 记录查询日志
        if ($config['monitoring']['log_queries'] ?? false) {
            $status = $success ? 'SUCCESS' : 'FAILED';
            Yii::info("推荐服务{$type}({$version}) {$status}: {$responseTime}s", 'recommend');
        }
        
        // 这里可以将指标发送到监控系统（如Prometheus、InfluxDB等）
        // 实际项目中需要实现具体的指标收集逻辑
    }
}
