<?php

namespace frontendPc\controllers;

use common\base\models\BaseActivity;
use common\base\models\BaseActivityForm;
use common\base\models\BaseActivityFormRegistrationForm;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseUploadForm;
use frontendPc\models\ArticleFormRegistrationForm;
use Yii;
use yii\base\Exception;

class ActivityFormRegistrationFormController extends BaseFrontendPcController
{
    /**
     * 活动表单报名
     */
    public function actionRegistrationActivityForm()
    {
        $request     = Yii::$app->request->post();
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ArticleFormRegistrationForm::registrationResumeActivityForm($request);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * @return \yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionIndex()
    {
        try {
            $request = Yii::$app->request->get();
            $name    = BaseActivityForm::findOneVal(['id' => $request['activityFormId']], 'name');
            // 报名成功页TDK
            $seoConfig   = Yii::$app->params['seo']['activityRegistrationFormSuccess'];
            $description = str_replace('【表单名称】', $name, $seoConfig['description']);
            $this->setSeo([
                'title'       => $seoConfig['title'],
                'keywords'    => $seoConfig['keywords'],
                'description' => $description,
            ]);

            return $this->render('/activity/dbMeetingApplySucceed.html');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 活动表单报名成功
     */
    public function actionRegistrationFormSuccess()
    {
        $memberId = Yii::$app->user->id;
        if (!$memberId) {
            return $this->success([
                'type' => 1,
                'tips' => '用户未登录',
            ]);
        }
        $request  = Yii::$app->request->get();
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        if (!BaseActivityFormRegistrationForm::checkRegistrationForm($request['activityFormId'], $resumeId)) {
            $activity = BaseActivity::find()
                ->alias('f')
                ->leftJoin(['a' => BaseActivityForm::tableName()], 'a.activity_id = f.id')
                ->select(['f.token'])
                ->where([
                    'a.id' => $request['activityFormId'],
                ])
                ->asArray()
                ->one();

            $token = $activity['token'];

            return $this->success([
                'type' => 2,
                'tips' => '用户未报名',
                'link' => '/a/' . $token,
            ]);
        }

        $data = ArticleFormRegistrationForm::getResumeActivityFormSuccess($request);

        return $this->success($data);
    }

    public function actionUpload()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $model = new BaseUploadForm();

            $data = $model->resume(Yii::$app->user->id);

            //保存附件简历记录
            $data  = array_merge($data, [
                'note' => 'activity_form',
            ]);
            $token = BaseResumeAttachment::saveAttachment($data)['token'];

            $transaction->commit();

            return $this->success([
                'name'  => $data['name'],
                'token' => $token,
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

}
