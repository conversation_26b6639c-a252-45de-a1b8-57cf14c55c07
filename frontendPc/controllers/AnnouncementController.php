<?php

namespace frontendPc\controllers;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementCollect;
use common\base\models\BaseAnnouncementReport;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleClickLog;
use common\base\models\BaseFile;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\helpers\FileHelper;
use common\libs\BaiduTimeFactor;
use common\libs\Qiniu;
use common\libs\ToutiaoTimeFactor;
use common\service\announcement\RecommendService;
use common\service\specialNeedService\AnnouncementInformationService;
use frontendPc\models\Announcement;
use frontendPc\models\AnnouncementReport;
use frontendPc\models\Article;
use frontendPc\models\Job;
use frontendPc\models\Member;
use frontendPc\models\Resume;
use frontendPc\models\ResumeAnnouncementReportRecord;
use queue\Producer;
use Yii;
use yii\helpers\Url;

class AnnouncementController extends BaseFrontendPcController
{

    /**
     * 获取详情页面信息
     */
    public function actionDetail()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id ?: 0;

        if (Yii::$app->request->get('update') == 1) {
            // $info = Announcement::getDetailInfo($id, true);
            Producer::afterAnnouncementUpdateJob($id);
        } else {
            $info = Announcement::getDetailInfo($id);
        }
        //是否已经收藏
        $info['isCollect'] = BaseAnnouncementCollect::getCollectStatus($id, $memberId);

        if (!$info['id']) {
            $this->notFound();
        }
        if ($info['is_delete'] == BaseArticle::IS_DELETE_YES) {
            $this->notFound();
        }

        if ($info['is_show'] == BaseArticle::IS_SHOW_NO) {
            $this->notFound();
        }

        if ($info['status'] == BaseArticle::STATUS_STAGING) {
            $this->notFound();
        }

        BaseArticleClickLog::create($info['article_id'], $memberId);
        //查看公告是否有附件需要显示
        $info['fileList'] = [];
        if (!empty($info['file_ids'])) {
            $fileIdsArr = explode(',', $info['file_ids']);
            $file_data  = BaseFile::getIdsList($fileIdsArr);
            $fileArr    = [];
            foreach ($file_data as &$value) {
                if (!empty($value['path'])) {
                    $item         = [];
                    $item['path'] = FileHelper::getFullUrl($value['path']);
                    // 添加下载参数
                    $item['path']   = Qiniu::supplementDownloadParameter($item['path'], $value['name']);
                    $item['name']   = $value['name'];
                    $item['suffix'] = FileHelper::getFileSuffixClassName($value['suffix']);
                    $item['id']     = $value['id'];

                    $fileArr[] = $item;
                }
            }
            $info['fileList'] = $fileArr;
        }
        //判断使用的页面模板是哪个
        // $info['status']                = 2;
        // $info['establishmentTypeText'] = '部分有编';
        switch ($info['template_id']) {
            case Announcement::TEMPLATE_SENIOR_ONE:
                //高级模板
                $template        = 'cooperationDetail.html';
                $info['content'] = Article::replaceIllegalStyle($info['content']);
                $info['content'] = Announcement::formatContent($info['content']);
                if (!$memberId) {
                    //未登录，判断是否需要邮箱脱敏
                    $info['content'] = BaseAnnouncement::setEmailMask($id, $info['content']);
                }
                // 2.0添加逻辑 背景图 若未上传，则按“上传的背景图 > 单位主页背景图 > 高级模版2默认背景图”的优先级展示在公告详情页中。
                $headerImage = BaseAnnouncement::getRealHeadImage($id);
                if ($headerImage) {
                    $info['headerImage'] = $headerImage;
                }
                break;
            case Announcement::TEMPLATE_ORDINARY:
                //普通模版
                $template        = 'unCooperationDetail.html';
                $info['content'] = Article::replaceIllegalStyle($info['content']);
                if (!$memberId) {
                    //未登录，判断是否需要邮箱脱敏
                    $info['content'] = BaseAnnouncement::setEmailMask($id, $info['content']);
                }
                //  所属省份、工作地点、报名方式
                //                //推荐职位
                //                $recommendJob    = BaseJob::getRecommendList(['pageSize' => Yii::$app->params['recommendJobCount']]);
                //                $recommendJobUrl = BaseJob::getRecommendJobUrl();

                //推荐公告
                $recommendService    = new RecommendService();
                $recommendSearchData = [
                    'announcementId' => $id,
                    'limit'          => 8,
                ];
                $recommendList       = $recommendService->setData($recommendSearchData)
                    ->getRecommendList();
                $recommendList0      = array_slice($recommendList, 0, 4);
                $recommendList1      = array_slice($recommendList, 4, 4);
                $recommendHtml       = $this->renderPartial('/announcement/recommend.html', [
                    'recommendList0' => $recommendList0,
                    'recommendList1' => $recommendList1,
                ]);

                break;
            case Announcement::TEMPLATE_DOUBLE_MEETING_ACTIVITY:
                // 双会模板
                $template        = 'doubleMeetingActivityDetail.html';
                $info['content'] = Article::replaceIllegalStyle($info['content']);
                $info['content'] = Announcement::formatContent($info['content']);
                break;
            case Announcement::TEMPLATE_SENIOR_TWO:
                //高级模板2
                $template        = 'cooperationDetailTwo.html';
                $info['content'] = Article::replaceIllegalStyle($info['content']);
                $info['content'] = Announcement::formatContentV2($info['content']);
                // 这里有个恶心的点，就不放到前端判断了，在这里判断
                // if ($info['establishmentTypeText'] && $info['allWelfareLabel'][0]) {
                //     $info['establishmentTypeText'] = $info['establishmentTypeText'] . '、';
                // }
                $info['allWelfareLabelText'] = implode('、', $info['allWelfareLabel']);
                if (!$memberId) {
                    //未登录，判断是否需要邮箱脱敏
                    $info['content'] = BaseAnnouncement::setEmailMask($id, $info['content']);
                }

                // 2.0添加逻辑 背景图 若未上传，则按“上传的背景图 > 单位主页背景图 > 高级模版2默认背景图”的优先级展示在公告详情页中。
                $headerImage = BaseAnnouncement::getRealHeadImage($id);
                if ($headerImage) {
                    $info['headerImage'] = $headerImage;
                }

                break;
            case Announcement::TEMPLATE_SENIOR_THREE:
                //高级模板3
                $template        = 'cooperationDetailThree.html';
                $info['content'] = Article::replaceIllegalStyle($info['content']);
                $info['content'] = Announcement::formatContentV2($info['content']);
                // 这里有个恶心的点，就不放到前端判断了，在这里判断
                // if ($info['establishmentTypeText'] && $info['allWelfareLabel'][0]) {
                //     $info['establishmentTypeText'] = $info['establishmentTypeText'] . '、';
                // }
                $info['allWelfareLabelText'] = implode('、', $info['allWelfareLabel']);
                if (!$memberId) {
                    //未登录，判断是否需要邮箱脱敏
                    $info['content'] = BaseAnnouncement::setEmailMask($id, $info['content']);
                }

                // 2.0添加逻辑 背景图 若未上传，则按“上传的背景图 > 单位主页背景图 > 高级模版2默认背景图”的优先级展示在公告详情页中。
                $headerImage = BaseAnnouncement::getRealHeadImage($id);
                if ($headerImage) {
                    $info['headerImage'] = $headerImage;
                }

                break;
        }

        //判断用户当前的状态（是否完成简历前三步）
        $userStatus = Member::USER_STATUS_UN_LOGIN;
        if (!empty($memberId)) {
            $userStatus = Member::getUserResumeStatus($memberId);
        }
        $columnInfo = Article::getHomeColumnShow($info['article_id']);

        $jobListUrl = Url::toRoute([
            '/announcement/job-list',
            'id' => $id,
        ]);

        $seoConfig = Yii::$app->params['seo']['announcementDetail'];
        // 先把公告的原本的找出来
        $keywords    = $info['seo_keywords'];
        $description = $info['seo_description'];
        if (!$description) {
            // 去掉html 标签并且截取100个字符
            $descriptionContent = strip_tags($info['content']);
            // 去掉换行
            $descriptionContent = str_replace(PHP_EOL, '', $descriptionContent);
            $descriptionContent = mb_substr($descriptionContent, 0, 100, 'utf-8');
            $description        = str_replace([
                '【公告标题】',
                '【公告内容】',
            ], [
                $info['title'],
                $descriptionContent,
            ], $seoConfig['description']);
        }
        if (!$keywords) {
            $keywords = $info['title'];
        }

        $this->setSeo([
            'title'       => str_replace('【公告标题】', $info['title'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);

        $welfareLabelViewAmount = Yii::$app->params['welfareLabelViewAmount'];

        BaiduTimeFactor::create($info['fullRefreshTime']);
        ToutiaoTimeFactor::create($info['fullRefreshTime']);

        // 当前公告是否已经查看过
        $resumeId  = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $hasRecord = ResumeAnnouncementReportRecord::checkReportRecord($resumeId, $id);
        // 获取公告热度
        $heatInfo['hasRecord'] = $hasRecord;
        $heatInfo['reportUrl'] = Announcement::getReportUrl($resumeId, $id);
        if ($hasRecord) {
            $heatInfo['data'] = AnnouncementReport::getHeat($id);
        } else {
            $heatInfo['data'] = [];
        }

        $info = (new AnnouncementInformationService())->handelMajorRanking($info);

        $info = (new AnnouncementInformationService())->handelDetailInfo($info, $id);

        return $this->render($template, [
            'info'                   => $info,
            'userStatus'             => $userStatus,
            'columnInfo'             => $columnInfo,
            'isShowTips'             => mb_strlen($info['majorNameText'], 'utf-8') >= 260,
            //            'recommendJob'           => $recommendJob,
            //            'recommendJobUrl'        => $recommendJobUrl,
            'recommendHtml'          => $recommendHtml,
            'jobListUrl'             => $jobListUrl,
            'welfareLabelViewAmount' => $welfareLabelViewAmount,
            'heatInfo'               => $heatInfo,
            'shareUrlCode'           => BaseAnnouncement::getDetailMiniCode($id),
        ]);
    }

    public function actionPreview()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;

        $info = Announcement::getDetailInfo($id, true);
        //是否已经收藏
        $info['isCollect'] = BaseAnnouncementCollect::getCollectStatus($id, $memberId);

        if (!$info['id']) {
            $this->notFound();
        }
        if ($info['status'] != BaseArticle::STATUS_STAGING) {
            $this->notFound();
        }

        //查看公告是否有附件需要显示
        $info['fileList'] = [];
        if (!empty($info['file_ids'])) {
            $fileIdsArr = explode(',', $info['file_ids']);
            $file_data  = BaseFile::getIdsList($fileIdsArr);
            $fileArr    = [];
            foreach ($file_data as &$value) {
                if (!empty($value['path'])) {
                    $item         = [];
                    $item['path'] = FileHelper::getFullUrl($value['path']);
                    $item['name'] = $value['name'];
                    $item['id']   = $value['id'];
                    $fileArr[]    = $item;
                }
            }
            $info['fileList'] = $fileArr;
        }
        //判断使用的页面模板是哪个

        switch ($info['template_id']) {
            case Announcement::TEMPLATE_SENIOR_ONE:
                //高级模板
                $template        = 'cooperationDetail.html';
                $info['content'] = Article::replaceIllegalStyle($info['content']);
                $info['content'] = Announcement::formatContent($info['content']);
                break;
            case Announcement::TEMPLATE_ORDINARY:
                //普通模版
                $template        = 'unCooperationDetail.html';
                $info['content'] = Article::replaceIllegalStyle($info['content']);
                break;
            case Announcement::TEMPLATE_DOUBLE_MEETING_ACTIVITY:
                // 双会模板
                $template        = 'doubleMeetingActivityDetail.html';
                $info['content'] = Article::replaceIllegalStyle($info['content']);
                $info['content'] = Announcement::formatContent($info['content']);
                break;
            case Announcement::TEMPLATE_SENIOR_TWO:
                //高级模板2
                $template        = 'cooperationDetailTwo.html';
                $info['content'] = Article::replaceIllegalStyle($info['content']);
                $info['content'] = Announcement::formatContentV2($info['content']);
                // 这里有个恶心的点，就不放到前端判断了，在这里判断
                // if ($info['establishmentTypeText'] && $info['allWelfareLabel'][0]) {
                //     $info['establishmentTypeText'] = $info['establishmentTypeText'] . '、';
                // }
                $info['allWelfareLabelText'] = implode('、', $info['allWelfareLabel']);
                break;

            case Announcement::TEMPLATE_SENIOR_THREE:
                //高级模板3
                $template        = 'cooperationDetailThree.html';
                $info['content'] = Article::replaceIllegalStyle($info['content']);
                $info['content'] = Announcement::formatContentV2($info['content']);
                // 这里有个恶心的点，就不放到前端判断了，在这里判断
                // if ($info['establishmentTypeText'] && $info['allWelfareLabel'][0]) {
                //     $info['establishmentTypeText'] = $info['establishmentTypeText'] . '、';
                // }
                $info['allWelfareLabelText'] = implode('、', $info['allWelfareLabel']);
                break;
            default:
                $this->notFound();
        }

        //判断用户当前的状态（是否完成简历前三步）
        $userStatus = Member::USER_STATUS_UN_LOGIN;
        if (!empty($memberId)) {
            $userStatus = Member::getUserResumeStatus($memberId);
        }
        $columnInfo = Article::getHomeColumnShow($info['article_id']);

        $jobListUrl = Url::toRoute([
            '/announcement/job-list',
            'id' => $id,
        ]);

        $seoConfig = Yii::$app->params['seo']['announcementDetail'];
        // 先把公告的原本的找出来
        $keywords    = $info['seo_keywords'];
        $description = $info['seo_description'];
        if (!$description) {
            // 去掉html 标签并且截取100个字符
            $descriptionContent = strip_tags($info['content']);
            // 去掉换行
            $descriptionContent = str_replace(PHP_EOL, '', $descriptionContent);
            $descriptionContent = mb_substr($descriptionContent, 0, 100, 'utf-8');
            $description        = str_replace('【公告标题】', $info['title'], $seoConfig['description']);
            $description        = str_replace('【公告内容】', $descriptionContent, $description);
        }
        if (!$keywords) {
            $keywords = $info['title'];
        }

        $this->setSeo([
            'title'       => str_replace('【公告标题】', $info['title'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);

        $welfareLabelViewAmount = Yii::$app->params['welfareLabelViewAmount'];

        BaiduTimeFactor::create($info['fullRefreshTime']);
        ToutiaoTimeFactor::create($info['fullRefreshTime']);

        return $this->render($template, [
            'info'                   => $info,
            'userStatus'             => $userStatus,
            'columnInfo'             => $columnInfo,
            'jobListUrl'             => $jobListUrl,
            'welfareLabelViewAmount' => $welfareLabelViewAmount,
            'isPreview'              => '1',
        ]);
    }

    /**
     * 公告下的职位列表
     * @return string
     * @throws \Exception
     */
    public function actionJobList()
    {
        $searchData = Yii::$app->request->get();
        $id         = $searchData['id'];
        if ($id <= 0) {
            $this->notFound();
        }
        $info = Announcement::getDetailInfo($id);
        if (!$info) {
            $this->notFound();
        }
        $memberId = Yii::$app->user->id;
        //是否已经收藏
        $info['isCollect'] = BaseAnnouncementCollect::getCollectStatus($id, $memberId);
        if ($info['is_show'] == BaseArticle::IS_SHOW_NO) {
            $this->notFound();
        }

        if ($info['status'] == BaseArticle::STATUS_STAGING) {
            $this->notFound();
        }

        //判断使用的页面模板是哪个
        $templateType = $info['template_id'];
        //        //推荐职位
        //        $recommendJob    = BaseJob::getRecommendList(['pageSize' => Yii::$app->params['recommendJobCount']]);
        //        $recommendJobUrl = BaseJob::getRecommendJobUrl();

        //查询公告下的职位列表
        $jobInfo = Job::getAnnouncementChildJobList($searchData);
        //设置默认值
        $resumeAttachmentList = [];
        $resumeStatus         = Resume::STATUS_UN_COMPLETE_BASE_INFO;
        $userStatus           = Member::USER_STATUS_UN_LOGIN;
        if (!empty($memberId)) {
            //获取用户邮箱
            $userEmail = Member::findOneVal(['id' => $memberId], 'email');

            //获取用户附件简历列表
            $resumeAttachmentList = BaseResumeAttachment::getList($memberId);

            //判断用户当前的状态（是否完成简历前三步）
            $userStatus = Member::getUserResumeStatus($memberId);
            if ($userStatus == Member::USER_STATUS_COMPLETE_RESUME) {
                //已经完成前三步，获取用户简历状态
                $resumeStatus = BaseResume::STATUS_COMPLETE_BASE_INFO;
            }
        }
        $announcementUrl = Url::toRoute([
            '/announcement/detail',
            'id' => $id,
        ]);

        $welfareLabelViewAmount = Yii::$app->params['welfareLabelViewAmount'];

        $seoConfig   = Yii::$app->params['seo']['announcementJobList'];
        $title       = str_replace('【公告标题】', $info['title'], $seoConfig['title']);
        $keywords    = str_replace('【公告标题】', $info['title'], $seoConfig['keywords']);
        $description = str_replace('【公告标题】', $info['title'], $seoConfig['description']);

        $this->setSeo([
            'title'       => $title,
            'keywords'    => $keywords,
            'description' => $description,
        ]);

        // 这里看模板id
        // $templateType   = 5;
        // $info['status'] = 2;
        // $info['status']                = 2;
        // $info['establishmentTypeText'] = '部分有编';
        switch ($templateType) {
            case Announcement::TEMPLATE_ORDINARY:
                $template = 'unCooperationJobList.html';
                break;
            case Announcement::TEMPLATE_SENIOR_ONE:
                // 2.0添加逻辑 背景图 若未上传，则按“上传的背景图 > 单位主页背景图 > 高级模版2默认背景图”的优先级展示在公告详情页中。
                $headerImage = BaseAnnouncement::getRealHeadImage($id);
                if ($headerImage) {
                    $info['headerImage'] = $headerImage;
                }
                $template = 'cooperationJobList.html';
                break;
            case Announcement::TEMPLATE_DOUBLE_MEETING_ACTIVITY:
                // 双会模板
                $template = 'doubleMeetingActivityJobList.html';
                break;
            case Announcement::TEMPLATE_SENIOR_TWO:

                $headerImage = BaseAnnouncement::getRealHeadImage($id);
                if ($headerImage) {
                    $info['headerImage'] = $headerImage;
                }
                $template = 'cooperationJobListTwo.html';
                break;
            case Announcement::TEMPLATE_SENIOR_THREE:

                $headerImage = BaseAnnouncement::getRealHeadImage($id);
                if ($headerImage) {
                    $info['headerImage'] = $headerImage;
                }
                $template = 'cooperationJobListThree.html';
                break;
        }

        $jobInfo = (new AnnouncementInformationService())->handelJobList($jobInfo, $id);
        $info    = (new AnnouncementInformationService())->handelDetailInfo($info, $id);

        if ($info['jobAmount'] > 5) {
            $jobFilter = BaseAnnouncement::getJobListFilter($id);;
        }

        return $this->render($template, [
            'info'                   => $info,
            'jobList'                => $jobInfo['list'],
            'jobFilter'              => $jobFilter ?? [],
            //            'recommendJob'           => $recommendJob,
            //            'recommendJobUrl'        => $recommendJobUrl,
            'templateType'           => $templateType,
            'userEmail'              => $userEmail,
            'userStatus'             => $userStatus,
            'announcementUrl'        => $announcementUrl,
            'resumeStatus'           => $resumeStatus,
            'welfareLabelViewAmount' => $welfareLabelViewAmount,
            'jobCount'               => $jobInfo['totalNum'],
            'shareUrlCode'           => BaseAnnouncement::getDetailMiniCode($id),
        ]);
    }

    /**
     * 公告报告详情页
     */
    public function actionReport()
    {
        $announcementId   = Yii::$app->request->get('id');
        $memberId         = Yii::$app->user->id;
        $resumeId         = Resume::findOneVal(['member_id' => $memberId], 'id');
        $token            = Yii::$app->request->get('token', 0);
        $url              = Announcement::getDetailUrl($announcementId);
        $permissionDenied = "<script>alert('您暂无权限访问该页面');window.location.href='$url';</script>";

        if (empty($resumeId) || empty($token)) {
            exit($permissionDenied);
        }

        $announcementRow = Announcement::find()
            ->alias('an')
            ->select('ar.status, ar.is_delete, ar.is_show')
            ->where([
                'an.id' => $announcementId,
            ])
            ->leftJoin(['ar' => BaseArticle::tableName()], 'ar.id = an.article_id')
            ->asArray()
            ->one();

        if (empty($announcementRow)) {
            exit($permissionDenied);
        }
        if ($announcementRow['is_delete'] == BaseArticle::IS_DELETE_YES) {
            exit($permissionDenied);
        }

        if ($announcementRow['status'] == BaseArticle::STATUS_STAGING) {
            exit($permissionDenied);
        }

        // 是否查看过
        $hasRecord = ResumeAnnouncementReportRecord::checkReportRecord($resumeId, $announcementId, $token);
        if (!$hasRecord) {
            exit($permissionDenied);
        }

        // 输出分析报告
        // exit('公告报告');
        // $data = BaseAnnouncementReport::getReport($announcementId);

        // return $this->render('report.html', ['data' => $data]);

        return $this->render('reportLoading.html', [
            'announcementId' => $announcementId,
            'token'          => $token,
        ]);
    }

    public function actionReportDetail()
    {
        $announcementId   = Yii::$app->request->get('announcementId');
        $memberId         = Yii::$app->user->id;
        $resumeId         = Resume::findOneVal(['member_id' => $memberId], 'id');
        $token            = Yii::$app->request->get('token', 0);
        $url              = Announcement::getDetailUrl($announcementId);
        $permissionDenied = "<script>alert('您暂无权限访问该页面');window.location.href='$url';</script>";

        if (empty($resumeId) || empty($token)) {
            exit($permissionDenied);
        }

        $announcementRow = Announcement::find()
            ->alias('an')
            ->select('ar.status, ar.is_delete, ar.is_show')
            ->where([
                'an.id' => $announcementId,
            ])
            ->leftJoin(['ar' => BaseArticle::tableName()], 'ar.id = an.article_id')
            ->asArray()
            ->one();

        if (empty($announcementRow)) {
            exit($permissionDenied);
        }
        if ($announcementRow['is_delete'] == BaseArticle::IS_DELETE_YES) {
            exit($permissionDenied);
        }

        if ($announcementRow['status'] == BaseArticle::STATUS_STAGING) {
            exit($permissionDenied);
        }

        // 是否查看过
        $hasRecord = ResumeAnnouncementReportRecord::checkReportRecord($resumeId, $announcementId, $token);
        if (!$hasRecord) {
            exit($permissionDenied);
        }

        // 输出分析报告
        // exit('公告报告');
        $data = BaseAnnouncementReport::getReport($announcementId);

        return $this->success(['html' => $this->renderPartial('report.html', ['data' => $data])]);
    }
}
