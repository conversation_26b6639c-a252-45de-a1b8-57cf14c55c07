<?php
/**
 * create user：shannon
 * create time：2024/3/1 10:42
 */
namespace frontendPc\controllers;

use common\base\models\BaseHomePosition;
use common\base\models\BaseResume;
use common\base\models\BaseSeoHotWordConfig;
use common\base\models\BaseShowcase;
use common\libs\BaiduTimeFactor;
use common\libs\ToutiaoTimeFactor;
use Yii;

class HotWordController extends BaseFrontendPcController
{
    /**
     * 热词搜索
     * @return string
     */
    public function actionIndex()
    {
        //获取热词code
        $code = Yii::$app->request->get('code');
        //不存在code直接去404
        if (empty($code)) {
            $this->notFound();
        }
        //获取一下关键词信息
        $hotInfo = BaseSeoHotWordConfig::findOne(['code' => $code]);
        //不存在code直接去404
        if (empty($hotInfo)) {
            $this->notFound();
        }
        $data = BaseSeoHotWordConfig::getHotWord($hotInfo->id);
        $this->setSeo($data['seo']);
        unset($data['seo']);
        $isLogin         = !Yii::$app->user->isGuest;
        $data['isLogin'] = $isLogin;
        $data['isVip']   = $isLogin && BaseResume::checkVip(Yii::$app->user->id);
        //获取广告位置
        $positionInfo = BaseHomePosition::findOne([
            'number' => 'sousuojieguoye_01_youce',
            'status' => BaseHomePosition::STATUS_SHOW_YES,
        ]);

        //获取广告位数据
        $positionData         = BaseShowcase::getByPositionConfig($positionInfo->id, $positionInfo->number);
        $data['positionData'] = $positionData;

        //时间因子
        BaiduTimeFactor::createHotword();
        ToutiaoTimeFactor::createHotword();

        return $this->render('index.html', $data);
    }
}