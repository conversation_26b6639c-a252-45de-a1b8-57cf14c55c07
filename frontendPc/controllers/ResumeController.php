<?php

namespace frontendPc\controllers;

use common\base\models\BaseResumeShare;
use common\libs\ShortLink;
use Yii;

class ResumeController extends BaseFrontendPcController
{
    public function actionShare()
    {
        $this->layout = false;
        $code         = Yii::$app->request->get('code');

        if (!$code) {
            $this->notFound();
        }

        // 找到对应的信息
        $model = BaseResumeShare::find()
            ->select([
                'id',
                'expire_time',
            ])
            ->where(['code' => $code])
            ->one();

        if (!$model) {
            $this->notFound();
        }

        $return = [
            'code'     => $code,
            'isExpire' => 0,
        ];

        if (time() > strtotime($model->expire_time)) {
            $return['isExpire'] = 1;
        }

        // 这里就渲染页面了
        return $this->render('/resume/share/index.html', [
            'data' => $return,
        ]);
    }

}
