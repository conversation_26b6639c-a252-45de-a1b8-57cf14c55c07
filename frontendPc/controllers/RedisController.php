<?php
/**
 * create user：shannon
 * create time：2024/12/30 上午10:01
 */
namespace frontendPc\controllers;

use common\base\BaseController;
use common\libs\Cache;
use Yii;
use Exception;

class RedisController extends BaseController
{
    /**
     * Redis管理页面
     */
    public function actionIndex()
    {
        $this->layout = false;
        if (Yii::$app->params['environment'] == 'prod') {
            exit('无权访问');
        }

        return $this->render('redis.html');
    }

    /**
     * 获取Redis值
     * @return \yii\web\Response
     */
    public function actionGetRedis()
    {
        if (Yii::$app->params['environment'] == 'prod') {
            exit('无权访问');
        }
        try {
            $key = Yii::$app->request->get('key');
            if (empty($key)) {
                return $this->asJson([
                    'code'    => 400,
                    'message' => 'Key不能为空',
                    'data'    => null,
                ]);
            }

            $redis = Yii::$app->redis;

            // 检查是否包含通配符
            if (strpos($key, '*') !== false) {
                $keys = $redis->keys($key);
                if (empty($keys)) {
                    return $this->asJson([
                        'code'    => 0,
                        'message' => 'success',
                        'data'    => [
                            'key'   => $key,
                            'type'  => 'pattern',
                            'value' => [],
                        ],
                    ]);
                }

                // 获取所有匹配的key的值
                $result = [];
                foreach ($keys as $matchedKey) {
                    $type     = $redis->type($matchedKey);
                    $value    = $this->getRedisValue($matchedKey, $type);
                    $result[] = [
                        'key'   => $matchedKey,
                        'type'  => $type,
                        'value' => $value,
                    ];
                }

                return $this->asJson([
                    'code'    => 0,
                    'message' => 'success',
                    'data'    => [
                        'key'   => $key,
                        'type'  => 'pattern',
                        'value' => $result,
                    ],
                ]);
            }

            // 单个key的处理逻辑
            $type  = $redis->type($key);
            $value = $this->getRedisValue($key, $type);

            return $this->asJson([
                'code'    => 0,
                'message' => 'success',
                'data'    => [
                    'key'   => $key,
                    'type'  => $this->getRedisTypeName($type),
                    'value' => $value,
                ],
            ]);
        } catch (Exception $e) {
            return $this->asJson([
                'code'    => 500,
                'message' => $e->getMessage(),
                'data'    => null,
            ]);
        }
    }

    /**
     * 获取Redis值
     * @param string $key
     * @param string $type
     * @return mixed
     * @throws Exception
     */
    private function getRedisValue($key, $type)
    {
        try {
            switch ($type) {
                case 'string':
                    $value = Cache::get($key);
                    // 尝试反序列化
                    if (is_string($value) && strpos($value, 'a:') === 0) {
                        $unserializedValue = @unserialize($value);
                        if ($unserializedValue !== false) {
                            if (is_array($unserializedValue) && isset($unserializedValue[0]) && is_array($unserializedValue[0])) {
                                $formattedRules = [];
                                foreach ($unserializedValue[0] as $rule) {
                                    if ($rule instanceof \yii\web\UrlRule) {
                                        $formattedRules[] = [
                                            'name'    => $rule->name,
                                            'pattern' => $rule->pattern,
                                            'route'   => $rule->route,
                                        ];
                                    }
                                }

                                return $formattedRules;
                            }

                            return $unserializedValue;
                        }
                    }

                    return $value;
                case 'set':
                    return Cache::sMembers($key);
                case 'list':
                    return Cache::lRange($key, 0, -1);
                case 'hash':
                    return Cache::hvals($key);
                case 'zset':
                    return Cache::zRange($key, 0, -1, true);
                case 'none':
                    throw new Exception('Key不存在');
                default:
                    // 尝试所有可能的类型
                    try {
                        // 尝试作为字符串读取
                        $value = Cache::get($key);
                        if ($value !== false) {
                            return $value;
                        }
                    } catch (\Exception $e) {
                    }

                    try {
                        // 尝试作为set读取
                        $value = Cache::sMembers($key);
                        if ($value !== false) {
                            return $value;
                        }
                    } catch (\Exception $e) {
                    }

                    try {
                        // 尝试作为hash读取
                        $value = Cache::hvals($key);
                        if (!empty($value)) {
                            return $value;
                        }
                    } catch (\Exception $e) {
                    }

                    try {
                        // 尝试作为list读取
                        $value = Cache::lRange($key, 0, -1);
                        if ($value !== false) {
                            return $value;
                        }
                    } catch (\Exception $e) {
                    }

                    try {
                        // 尝试作为zset读取
                        $value = Cache::zRange($key, 0, -1, true);
                        if ($value !== false) {
                            return $value;
                        }
                    } catch (\Exception $e) {
                    }

                    throw new Exception('无法确定键的类型或获取值失败');
            }
        } catch (Exception $e) {
            throw new Exception('获取Redis值失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取Redis数据类型的可读名称
     * @param string $type
     * @return string
     */
    private function getRedisTypeName($type)
    {
        // 直接返回类型字符串，因为已经是可读格式
        return $type ?: 'unknown';
    }

    /**
     * 设置Redis值
     * @return \yii\web\Response
     */
    public function actionSetRedis()
    {
        if (Yii::$app->params['environment'] == 'prod') {
            exit('无权访问');
        }
        try {
            $data  = json_decode(Yii::$app->request->getRawBody(), true);
            $key   = $data['key'] ?? '';
            $value = $data['value'] ?? '';

            if (empty($key) || empty($value)) {
                return $this->asJson([
                    'code'    => 400,
                    'message' => 'Key和Value不能为空',
                    'data'    => null,
                ]);
            }

            // 尝试解析JSON格式的value
            $parsedValue = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $value = $parsedValue;
            }

            $result = Cache::set($key, $value);

            return $this->asJson([
                'code'    => 0,
                'message' => 'success',
                'data'    => [
                    'success' => $result,
                    'message' => '数据保存成功',
                ],
            ]);
        } catch (Exception $e) {
            return $this->asJson([
                'code'    => 500,
                'message' => $e->getMessage(),
                'data'    => null,
            ]);
        }
    }

    /**
     * 删除Redis键
     * @return \yii\web\Response
     */
    public function actionDeleteRedis()
    {
        if (Yii::$app->params['environment'] == 'prod') {
            exit('无权访问');
        }
        try {
            $data = json_decode(Yii::$app->request->getRawBody(), true);
            $key  = $data['key'] ?? '';

            if (empty($key)) {
                return $this->asJson([
                    'code'    => 400,
                    'message' => 'Key不能为空',
                    'data'    => null,
                ]);
            }

            $result = Cache::delete($key);

            return $this->asJson([
                'code'    => 0,
                'message' => 'success',
                'data'    => [
                    'success' => $result,
                    'message' => $result ? '删除成功' : '键不存在',
                ],
            ]);
        } catch (Exception $e) {
            return $this->asJson([
                'code'    => 500,
                'message' => $e->getMessage(),
                'data'    => null,
            ]);
        }
    }

    /**
     * 获取所有Cache常量及其注释
     * @return \yii\web\Response
     */
    public function actionGetCacheConstants()
    {
        if (Yii::$app->params['environment'] == 'prod') {
            exit('无权访问');
        }

        try {
            $reflection = new \ReflectionClass('common\libs\Cache');
            $constants = $reflection->getConstants();
            
            // 获取文件内容
            $fileContent = file_get_contents($reflection->getFileName());
            
            $result = [];
            foreach ($constants as $name => $value) {
                // 查找常量的单行注释（//）
                $lines = explode("\n", $fileContent);
                $comment = '';
                foreach ($lines as $i => $line) {
                    if (strpos($line, "const $name") !== false) {
                        // 检查上一行是否包含注释
                        if ($i > 0 && strpos(trim($lines[$i - 1]), '//') === 0) {
                            $comment = trim(substr(trim($lines[$i - 1]), 2));
                            break;
                        }
                    }
                }
                
                $result[] = [
                    'name' => $name,
                    'value' => $value,
                    'comment' => $comment
                ];
            }

            return $this->asJson([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'constants' => $result
                ]
            ]);
        } catch (Exception $e) {
            return $this->asJson([
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null
            ]);
        }
    }
}