<?php

namespace frontendPc\controllers\api;

use common\base\models\BaseMemberSchedule;
use common\helpers\FormatConverter;
use frontendPc\models\MemberSchedule;
use frontendPc\models\UnitCenter;
use Yii;
use yii\base\Exception;
use yii\web\Response;

class MemberScheduleController extends BaseFrontPcApiController
{
    /**
     * 添加日程
     */
    public function actionAdd()
    {
        $post = Yii::$app->request->post();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $post['memberId'] = Yii::$app->user->id;

            $model = new MemberSchedule();
            if ($post['id']) {
                $model->editNormal(FormatConverter::convertHump($post));
            } else {
                $model->createNormal(FormatConverter::convertHump($post), BaseMemberSchedule::TYPE_NORMAL);
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 我的日程
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetMySchedule()
    {
        $request  = Yii::$app->request->get();
        $memberId = Yii::$app->user->id;

        try {
            return $this->success(MemberSchedule::getMySchedule(FormatConverter::convertHump($request), $memberId));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取日程信息
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetMemberSchedule()
    {
        $request = Yii::$app->request->get();
        if (strlen($request['id']) < 1) {
            throw new Exception('参数id不能为空');
        }

        try {
            return $this->success(UnitCenter::getMemberSchedule(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除日程
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionDeleteMemberSchedule()
    {
        $request = Yii::$app->request->post();
        if (strlen($request['id']) < 1) {
            throw new Exception('参数id不能为空');
        }

        try {
            UnitCenter::deleteMemberSchedule($request);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取某一个日程
     */
    public function actionGetDetail()
    {
    }

    /**
     * 获取月份日程
     */
    public function actionGetMonth()
    {
    }

    /**
     * 获取天日程
     */
    public function actionGetDate()
    {
    }
}