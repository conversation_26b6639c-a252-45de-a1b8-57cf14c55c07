<?php

namespace frontendPc\controllers\api;

use common\service\chat\ChatApplication;

class ChatCommonPhraseController extends BaseFrontPcApiController
{
    /**
     * 获取常用语列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        $memberId = \Yii::$app->user->id;

        try {
            $app  = ChatApplication::getInstance();
            $list = $app->getCommonPhrase($memberId);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionDelete()
    {
        $memberId = \Yii::$app->user->id;
        $id       = \Yii::$app->request->post('id');

        try {
            $app = ChatApplication::getInstance();
            $app->deleteCommonPhrase($memberId, $id);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionEdit()
    {
        $memberId = \Yii::$app->user->id;
        $id       = \Yii::$app->request->post('id');
        $content  = \Yii::$app->request->post('content');

        if (!$content) {
            return $this->fail('内容不能为空');
        }

        try {
            $app = ChatApplication::getInstance();
            $app->editCommonPhrase($memberId, $content, $id);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}