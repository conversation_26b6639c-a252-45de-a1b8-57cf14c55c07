<?php

namespace frontendPc\controllers\api\company;

use admin\models\CompanyInfoAuth;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobContact;
use common\base\models\BaseResume;
use common\base\models\BaseUploadForm;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\base\models\BaseJobClickLog;
use common\helpers\FormatConverter;
use common\helpers\StringHelper;
use common\libs\Cache;
use common\service\CommonService;
use common\service\company\PushEditMessageService;
use common\service\companyAuth\BaseButtonService;
use common\service\companyAuth\ButtonCallAuthService;
use common\service\job\AddService;
use common\service\job\BaseService;
use common\service\job\ReleaseAgainService;
use common\service\v2\job\AddBatchImportService;
use common\service\v2\job\DeleteService;
use common\service\v2\job\EditInitService;
use common\service\v2\job\EditService;
use common\service\v2\job\OfflineService;
use common\service\v2\job\RefreshService;
use common\service\v2\job\RepublishService;
use common\service\v2\job\TemplateInitAddService;
use common\service\v2\job\TemplateListService;
use frontendPc\models\Announcement;
use frontendPc\models\Job;
use frontendPc\models\Member;
use frontendPc\models\Resume;
use frontendPc\models\ResumeAttachment;
use frontendPc\models\ResumeComplete;
use Yii;
use yii\base\Exception;
use yii\console\Response;
use common\helpers\ArrayHelper;

class JobController extends BaseFrontPcApiCompanyController
{
    /**
     * 新增、添加纯职位
     * @return Response|\yii\web\Response
     */
    public function actionAdd()
    {
        //按钮权限
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
                ->run();

            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
        $tran = Yii::$app->db->beginTransaction();
        try {
            $data = (new \common\service\v2\job\AddService())->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                ->run();
            $tran->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $tran->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位模板列表
     * @return Response|\yii\web\Response
     */
    public function actionTemplateList()
    {
        try {
            return $this->success((new TemplateListService())->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                ->setParams(Yii::$app->request->get())
                ->setFormat(TemplateListService::FORMAT_TYPE_ID_NAME)
                ->run());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位模板初始化
     * @return Response|\yii\web\Response
     */
    public function actionTemplateInitAdd()
    {
        try {
            return $this->success((new TemplateInitAddService())->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                ->run());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量导入新增职位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddBatchImport()
    {
        //按钮权限
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $transaction->commit();
            (new AddBatchImportService())->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                ->run();

            return $this->success(['msgTxt' => '成功批量导入数据']);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑初始化职位
     * @return Response|\yii\web\Response
     */
    public function actionEditInit()
    {
        try {
            return $this->success((new EditInitService())->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                ->run());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑职位
     * 编辑纯职位、公告下职位
     * @return Response|\yii\web\Response
     */
    public function actionEdit()
    {
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_EDIT)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
        $tran = Yii::$app->db->beginTransaction();
        try {
            $data = (new EditService())->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                ->run();
            $tran->commit();

            return $this->success($data);
        } catch (Exception $e) {
            $tran->rollBack();

            return $this->fail($e->getMessage());
        }
    }


    //    =========================v2========v2=====v2==========================
    //    =========================v2========v2=====v2==========================
    //    =========================v2========v2=====v2==========================
    //    =========================v2========v2=====v2==========================
    //    =========================v2========v2=====v2==========================
    /**
     * 获取职位标题列表
     * @return Response|\yii\web\Response
     */
    //    public function actionGetNameList()
    //    {
    //        try {
    //            return $this->success((new TemplateInitAddService())->setPlatform(CommonService::PLATFORM_ADMIN)
    //                ->run());
    //        } catch (Exception $e) {
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    /**
     * 获取职位地址列表
     * @return Response|\yii\web\Response
     */
    public function actionGetAddressList()
    {
        try {
            return $this->success(Job::getCompanyAddressList());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取职位详情
     * @return Response|\yii\web\Response
     */
    public function actionGetDetails()
    {
        $request = Yii::$app->request->get();
        try {
            $data           = Job::getDetails(FormatConverter::convertHump($request));
            $data['source'] = 2;
            //单位端特殊处理一下$data数据源
            if (empty($data['delivery_type'])) {
                //那就去那拿一下公告的回显
                $announcementInfo             = BaseAnnouncement::findOne($data['announcement_id']);
                $data['apply_type']           = $announcementInfo->apply_type;
                $data['apply_address']        = $announcementInfo->apply_address;
                $data['extra_notify_address'] = $announcementInfo->extra_notify_address;
                $data['delivery_type']        = $announcementInfo->delivery_type;
                $data['delivery_way']         = $announcementInfo->delivery_way;
                $data['source']               = 1;
            };

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位的发布按钮权限判断
     * @return Response|\yii\web\Response
     */
    public function actionReleaseButtonAuth()
    {
        try {
            //这里做一下前置权限
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    //    /**
    //     * 发布/编辑职位
    //     * @return Response|\yii\web\Response
    //     */
    //    public function actionCreate()
    //    {
    //        $request = Yii::$app->request->post();
    //        //按钮权限
    //        try {
    //            if (empty($request['jobId'])) {
    //                $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
    //                    ->run();
    //            } else {
    //                $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_EDIT)
    //                    ->run();
    //            }
    //            if (!$res['isAuth']) {
    //                //无权限
    //                return $this->authError($res);
    //            }
    //        } catch (\Exception $e) {
    //            return $this->fail($e->getMessage());
    //        }
    //        $requestCheck = FormatConverter::convertHump($request);
    //        $checkData    = [
    //            'name',
    //            'job_category_id',
    //            'education_type',
    //            'major_id',
    //            'nature_type',
    //            'wage_type',
    //            'is_negotiable',
    //            'amount',
    //            'province_id',
    //            'city_id',
    //            'period_date',
    //            'duty',
    //            'requirement',
    //        ];
    //        foreach ($checkData as $list) {
    //            if (strlen($requestCheck[$list]) < 1) {
    //                return $this->fail('参数' . $list . '不能为空');
    //            }
    //        }
    //
    //        $service   = new AddService();
    //        $memberId  = Yii::$app->user->id;
    //        $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
    //        //$companyId            = BaseCompany::findOneVal(['member_id' => $memberId], 'id');
    //        $request['companyId'] = $companyId;
    //
    //        if (strlen($request['jobId']) < 1) {
    //            $request['status']         = BaseJob::STATUS_WAIT_AUDIT;
    //            $request['auditStatus']    = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
    //            $request['applyAuditTime'] = CUR_DATETIME;
    //        }
    //
    //        $transaction = Yii::$app->db->beginTransaction();
    //        //特殊处理一下单位端不需要传递着两个参数
    //        unset($request['deliveryType'], $request['deliveryWay']);
    //        try {
    //            $jobId = $service->setOperator($memberId, BaseService::OPERATOR_TYPE_COMPANY)
    //                ->setAudit()
    //                ->setDate($request)
    //                ->run();
    //            //下线成功推送一下消息
    //            (new PushEditMessageService())->publish(['jobId' => $jobId]);
    //            $transaction->commit();
    //
    //            return $this->success();
    //        } catch (\Exception $e) {
    //            $transaction->rollBack();
    //
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    //    /**
    //     * 保存职位
    //     * @return Response|\yii\web\Response
    //     */
    //    public function actionSave()
    //    {
    //        $request = Yii::$app->request->post();
    //        //按钮权限
    //        try {
    //            //$request['jobId']
    //            if (empty($request['jobId'])) {
    //                $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
    //                    ->run();
    //            } else {
    //                $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_EDIT)
    //                    ->run();
    //            }
    //            if (!$res['isAuth']) {
    //                //无权限
    //                return $this->authError($res);
    //            }
    //        } catch (\Exception $e) {
    //            return $this->fail($e->getMessage());
    //        }
    //        $service   = new AddService();
    //        $memberId  = Yii::$app->user->id;
    //        $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
    //        //$companyId            = BaseCompany::findOneVal(['member_id' => $memberId], 'id');
    //        $request['companyId'] = $companyId;
    //        $transaction          = Yii::$app->db->beginTransaction();
    //        try {
    //            //Job::create(FormatConverter::convertHump($request), BaseJob::STATUS_WAIT);
    //            // 保存
    //            $jobId = $service->setOperator($memberId, BaseService::OPERATOR_TYPE_COMPANY)
    //                ->setStaging()
    //                ->setDate($request)
    //                ->run();
    //            //下线成功推送一下消息
    //            (new PushEditMessageService())->publish(['jobId' => $jobId]);
    //
    //            $transaction->commit();
    //
    //            return $this->success(['job_id' => $jobId]);
    //        } catch (\Exception $e) {
    //            $transaction->rollBack();
    //
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    //    public function actionCreate()
    //    {
    //        $data      = Yii::$app->request->post();
    //        $companyId = Member::getMainId();
    //
    //        $service     = new AddService();
    //        $transaction = Yii::$app->db->beginTransaction();
    //        try {
    //            $data['companyId'] = $companyId;
    //            $service->setOperator(AddService::OPERATOR_TYPE_COMPANY, $companyId)
    //                ->setStaging()
    //                ->setDate($data)
    //                ->run();
    //
    //            $transaction->commit();
    //
    //            return $this->success();
    //        } catch (\Exception $e) {
    //            $transaction->rollBack();
    //
    //            return $this->fail($e->getMessage());
    //        }
    //    }
    //
    //    /**
    //     * 保存职位
    //     * @return Response|\yii\web\Response
    //     */
    //    public function actionSave()
    //    {
    //        $data      = Yii::$app->request->post();
    //        $companyId = Member::getMainId();
    //        $service   = new AddService();
    //        $transaction = Yii::$app->db->beginTransaction();
    //        try {
    //            $data['companyId'] = $companyId;
    //            $service->setOperator(AddService::OPERATOR_TYPE_COMPANY, $companyId)
    //                ->setAudit()
    //                ->setDate($data)
    //                ->run();
    //
    //            $transaction->commit();
    //
    //            return $this->success();
    //        } catch (\Exception $e) {
    //            $transaction->rollBack();
    //
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    /**
     * 获取可发布数量
     * @return Response|\yii\web\Response
     */
    public function actionGetReleaseAmount()
    {
        try {
            return $this->success([
                'notRelease' => Job::getReleaseAmount(),
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 根据搜索关键词获取职位名称列表
     * @return Response|\yii\web\Response
     */
    public function actionSearchKeyWordsList()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Job::searchJobNameList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 根据搜索关键词获取职位名称列表
     * @return Response|\yii\web\Response
     */
    public function actionGetDeleteJobNameList()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Job::searchDeleteJobNameList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取职位状态列表
     * @return Response|\yii\web\Response
     */
    public function actionGetStatusList()
    {
        try {
            $list = Job::getJobStatusList();

            return $this->success(ArrayHelper::obj2Arr($list));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取职位状态列表(在线、下线)
     * @return Response|\yii\web\Response
     */
    public function actionGetSearchStatusList()
    {
        try {
            $list = Job::getSearchStatusList();

            return $this->success(ArrayHelper::obj2Arr($list));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取职位数量和简历数量接口
     * @return Response|\yii\web\Response
     */
    public function actionGetAmount()
    {
        try {
            $list = Job::getJobAmount();

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位列表
     * @return Response|\yii\web\Response
     */
    public function actionJobList()
    {
        $request = Yii::$app->request->get();

        try {
            $list = Job::JobList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位刷新
     * @return Response|\yii\web\Response
     */
    public function actionRefresh()
    {
        //按钮权限
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_REFRESH)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new RefreshService())->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                ->run();

            //            Job::jobRefresh(FormatConverter::convertHump($request));
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位批量刷新检查
     * @return Response|\yii\web\Response
     */
    public function actionBatchRefreshCheck()
    {
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_BATCH_REFRESH)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
        try {
            return $this->success((new RefreshService())->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                ->batchRefreshCheck());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位批量刷新
     * @return Response|\yii\web\Response
     */
    public function actionBatchRefresh()
    {
        //按钮权限
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_BATCH_REFRESH)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new RefreshService())->setBatch()
                ->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位单条操作前置检查
     * @return Response|\yii\web\Response
     */
    public function actionOfflineCheck()
    {
        try {
            return $this->success((new OfflineService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->runCheck());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }


    /**
     * 职位下线
     * @return Response|\yii\web\Response
     */
    public function actionOffline()
    {
        // 按钮权限
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_OFFLINE)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new OfflineService())->setPlatform(OfflineService::PLATFORM_WEB_COMPANY)
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位再发布
     * @return Response|\yii\web\Response
     */
    public function actionRepublish()
    {
        //按钮权限
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISHING)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new RepublishService())->setPlatform(RepublishService::PLATFORM_WEB_COMPANY)
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取状态职位数量
     * @return Response|\yii\web\Response
     */
    public function actionGetColumnAmount()
    {
        try {
            //$list = Job::getColumnAmount();
            $list = Job::getJobColumnAmount();

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 根据条件搜索职位列表
     * @return Response|\yii\web\Response
     */
    public function actionSearchList()
    {
        $request = Yii::$app->request->get();

        try {
            $list = Job::getSearchList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    //    /**
    //     * 职位批量导入
    //     * @return Response|\yii\web\Response
    //     */
    //    public function actionJobImport()
    //    {
    //        //按钮权限
    //        try {
    //            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
    //                ->run();
    //            if (!$res['isAuth']) {
    //                //无权限
    //                return $this->authError($res);
    //            }
    //        } catch (\Exception $e) {
    //            return $this->fail($e->getMessage());
    //        }
    //        $request = Yii::$app->request->post();
    //
    //        $filePath = $request['filePath'];
    //        if (!$filePath) {
    //            return $this->fail('参数缺失');
    //        }
    //        $transaction = \Yii::$app->db->beginTransaction();
    //
    //        try {
    //            $data = Job::jobTemporaryBatchImport($request);
    //
    //            $transaction->commit();
    //
    //            return $this->success($data, '成功批量导入数据');
    //        } catch (Exception $e) {
    //            $transaction->rollBack();
    //
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    /**
     * 上传模版文件
     * @return Response|\yii\web\Response
     */
    public function actionUploadTemplate()
    {
        //按钮权限
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
        $model = new BaseUploadForm();
        $model->setUploadType('file');
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $path = 'job_excel';
            $data = $model->temporaryUploadExcel('file', $path);

            $transaction->commit();

            return $this->success([
                'url'     => $data['path'],
                'fullUrl' => \Yii::$app->params['homeUrl'] . '/' . $data['path'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 回收箱职位列表
     * @return Response|\yii\web\Response
     */
    public function actionGetRecoveryJobList()
    {
        $request = Yii::$app->request->get();

        try {
            $list = Job::getRecoveryJobList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取详情页面信息
     */
    public function actionDetail()
    {
        $id = Yii::$app->request->get('id');
        if (empty($id)) {
            $this->notFound();
        }
        $memberId = Yii::$app->user->id;

        //判断职位状态
        $jobStatus = Job::findOneVal(['id' => $id], 'status');
        if ($jobStatus == Job::STATUS_DELETE) {
            echo Yii::$app->view->renderFile('@app/views/home/<USER>', ['message' => '该职位已被删除']);
            exit;
        }

        $info                 = Job::getJobDetail($id, $memberId);
        $info['isEmailApply'] = StringHelper::strToBool($info['isEmailApply']);

        //获取默认的简历id
        $defaultResumeToken = ResumeAttachment::getDefaultResumeToken($memberId);
        //设置默认值
        $resumeAttachmentList  = [];
        $resumeCompletePercent = 0;
        $info['resumeStatus']  = Resume::STATUS_UN_COMPLETE_BASE_INFO;

        $info['userStatus'] = Member::USER_STATUS_UN_LOGIN;

        if (!empty($memberId)) {
            $resumeAttachmentList = ResumeAttachment::getList($memberId);

            //判断用户当前的状态（是否完成简历前三步）
            $info['userStatus'] = Member::getUserResumeStatus($memberId);
            if ($info['userStatus'] == Member::USER_STATUS_COMPLETE_RESUME) {
                //如果简历已经完成了，获取简历完成度
                $resumeCompletePercent = BaseResume::getComplete($memberId);
                //todo:暂时写死
                $info['resumeStatus'] = Resume::STATUS_COMPLETE_BASE_INFO;
            }
        }

        //获取推荐职位
        $recommendJobList = Job::getRecommendList(['pageSize' => Yii::$app->params['recommendJobCount']]);

        //职位附件
        if (!empty($info['announcementId'])) {
            $fileIds  = BaseAnnouncement::findOneVal(['id' => $info['announcementId']], 'file_ids');
            $fileList = BaseAnnouncement::getAppendixList($fileIds);
        } else {
            $fileList = BaseAnnouncement::getAppendixList($info['file_ids']);
        }

        // BaseJobClickLog::create($id);

        return $this->success([
            'info'                  => $info,
            'resumeAttachmentList'  => $resumeAttachmentList,
            'resumeCompletePercent' => $resumeCompletePercent,
            'recommendJobList'      => $recommendJobList,
            'defaultResumeToken'    => $defaultResumeToken,
            'fileList'              => $fileList,
        ]);
    }

    /**
     * 获取报名方式
     * @return Response|\yii\web\Response
     */
    public function actionGetSignUpList()
    {
        try {
            $data = Job::getSignUpList();

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取模版职位详情
     * @return Response|\yii\web\Response
     */
    public function actionGetJobDetails()
    {
        $request = Yii::$app->request->get();
        try {
            return $this->success(Job::getJobDetails(FormatConverter::convertHump($request)));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 报名方式筛选项
     * @return array|Response|\yii\web\Response
     */
    public function actionGetDeliveryWaySelect()
    {
        $type = Yii::$app->request->get('type');
        if ($type == 1) {
            $result = [
                'delivery_way' => ArrayHelper::obj2Arr(BaseJobApplyRecord::OUTER_DELIVERY_WAY),
            ];
        } elseif ($type == 2) {
            $result = [
                'delivery_way' => ArrayHelper::obj2Arr(BaseJobApplyRecord::OUTSIDE_DELIVERY_WAY),
            ];
        } else {
            $result = [
                'delivery_way' => ArrayHelper::obj2Arr(BaseJobApplyRecord::DELIVERY_WAY_NAME),
            ];
        }

        return $this->success($result);
    }

    /**
     * 获取单位的在线职位列表 - 用于人才推荐
     */
    public function actionOnlineList()
    {
        $memberId = Yii::$app->user->id;
        //获取单位ID
        $company_member_info = BaseCompanyMemberInfo::findOne([
            'member_id' => $memberId,
        ]);
        if (!$company_member_info) {
            return $this->fail('单位终审未通过，请耐心等待！');
        }
        $companyId    = $company_member_info->company_id;
        $company_info = BaseCompany::findOne($companyId);
        if (!$company_info) {
            return $this->fail('单位信息获取失败');
        }
        $list = [];
        if ($company_info->status == BaseCompany::STATUS_ACTIVE) {
            $job_ids_str = Cache::get(Cache::COMPANY_JOB_ONLINE_SORT . ':' . $memberId);
            $jobIds      = empty($job_ids_str) ? [] : explode(',', $job_ids_str);
            $list        = BaseJob::onlineList($memberId, $jobIds);
        }

        return $this->success([
            'list' => $list,
        ]);
    }

    /**
     * 获取单位的缓存在线职位列表 - 用于人才推荐
     */
    public function actionCacheOnlineList()
    {
        $memberId = Yii::$app->user->id;
        //获取单位ID
        $company_id   = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
        $company_info = BaseCompany::findOne($company_id);
        if (!$company_info) {
            return $this->fail('单位信息获取失败');
        }
        $list = [];
        if ($company_info->status == BaseCompany::STATUS_ACTIVE) {
            //获取缓存
            $job_ids_str = Cache::get(Cache::COMPANY_JOB_ONLINE_SORT . ':' . $memberId);
            if (!empty($job_ids_str)) {
                //进行排序
                $sort_job_ids = explode(',', $job_ids_str);
                $list         = BaseJob::cacheOnlineList($sort_job_ids);
            }
        }

        return $this->success([
            'list' => $list,
        ]);
    }

    /**
     * 缓存单位在线职位的前置排序职位
     * 该接口不要做业务
     */
    public function actionCacheJobSort()
    {
        $job_ids = Yii::$app->request->post('jobIds');
        if (empty($job_ids)) {
            return $this->success(['result' => true]);
        }
        $memberId = Yii::$app->user->id;
        //缓存Key
        $key = Cache::COMPANY_JOB_ONLINE_SORT . ':' . $memberId;
        //缓存七天 7=604800 30=2592000
        Cache::setex($key, 604800, $job_ids);

        return $this->success(['result' => true]);
    }

    /**
     * 拿单位的子账号列表
     */
    public function actionMemberAccountFilter()
    {
        try {
            $keywords = Yii::$app->request->get();
            //获取单位ID
            $memberId          = Yii::$app->user->id;
            $companyMemberInfo = BaseCompanyMemberInfo::findOne([
                'member_id' => $memberId,
            ]);
            if (!$companyMemberInfo) {
                return $this->fail('账号信息错误');
            }
            $keywords['companyId'] = $companyMemberInfo->company_id;
            $data                  = BaseCompanyMemberInfo::memberAccountFilter($keywords, 1);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 拿单位的职位联系人
     */
    public function actionJobContactList()
    {
        try {
            //获取单位ID
            $memberId          = Yii::$app->user->id;
            $companyMemberInfo = BaseCompanyMemberInfo::findOne([
                'member_id' => $memberId,
            ]);
            if (!$companyMemberInfo) {
                return $this->fail('账号信息错误');
            }
            $keywords['companyId'] = $companyMemberInfo->company_id;

            return $this->success(BaseCompanyMemberInfo::memberAccountFilter($keywords));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 拿单位的子账号列表
     */
    public function actionGetMemberAccountFilter()
    {
        try {
            $keywords = Yii::$app->request->get();
            //获取单位ID
            $memberId          = Yii::$app->user->id;
            $companyMemberInfo = BaseCompanyMemberInfo::findOne([
                'member_id' => $memberId,
            ]);
            if (!$companyMemberInfo) {
                return $this->fail('账号信息错误');
            }
            $keywords['companyId'] = $companyMemberInfo->company_id;
            $data                  = BaseCompanyMemberInfo::memberAccountFilter($keywords, 2);
            $data[]                = [
                'id'             => '-1',
                'contact'        => '无',
                'contact_text'   => '无',
                'job_contact_id' => '-1',
            ];

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取公告名称列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAnnouncementNameList()
    {
        return $this->success(Announcement::getListByName(Yii::$app->request->get('title'), Member::getMainId()));
    }
}