<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseChatRoom;
use common\base\models\BaseMember;
use common\base\models\BaseSmsLog;
use common\libs\SmsQueue;
use common\service\chat\ChatApplication;
use common\service\commonResume\CommonResumeApplication;
use Yii;
use yii\db\Exception;

class ChatController extends BaseFrontPcApiCompanyController
{

    /**
     * 创建聊天室前置检查
     * @return \yii\console\Response|\yii\web\Response
     */
    // public function actionCreateRoomPreCheck()
    // {
    //     try {
    //         $chatApp = ChatApplication::getInstance();
    //         $params  = [
    //             'resumeId'        => \Yii::$app->request->post('resumeId'),
    //             'companyMemberId' => \Yii::$app->user->id,
    //             'creatorType'     => BaseChatRoom::CREATOR_TYPE_COMPANY,
    //             'jobId'           => \Yii::$app->request->post('jobId'),
    //         ];
    //         //对单位权限进行前置检查
    //         $data = $chatApp->companyCheckRoom($params);
    //
    //         //如果没有问题，返回
    //         return $this->success($data);
    //     } catch (Exception $e) {
    //         return $this->fail($e->getMessage());
    //     }
    // }

    /**
     * 创建一个或批量创建聊天室
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCreateRoom()
    {
        $memberLastLoginTime = BaseMember::findOneVal([
            'id' => Yii::$app->user->id,
        ], 'last_login_time');

        // 转时间戳
        $memberLastLoginTime = strtotime($memberLastLoginTime);

        if ($memberLastLoginTime && $memberLastLoginTime <= 1701340480) {
            // 返回403
            Yii::$app->user->logout();
            echo json_encode([
                'code'   => 403,
                'result' => 0,
                'msg'    => '请您先进行登录后再操作',
            ]);
            exit;
        }

        // 事务
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $chatApp = ChatApplication::getInstance();
            $params  = [
                // 先改成get
                'resumeId'          => \Yii::$app->request->post('resumeId'),
                'jobId'             => \Yii::$app->request->post('jobId'),
                'applyRecordId'     => \Yii::$app->request->post('applyRecordId'),
                'isChatWindow'      => \Yii::$app->request->post('isChatWindow'),
                'currentJobId'      => \Yii::$app->request->post('currentJobId'),
                'isRememberSmsChat' => \Yii::$app->request->post('isRememberSmsChat'),
                'companyMemberId'   => \Yii::$app->user->id,
                'companyId'         => $this->getCompanyId(),
                'creatorType'       => BaseChatRoom::CREATOR_TYPE_COMPANY,
            ];
            $data    = $chatApp->companyCreateRoom($params);

            $transaction->commit();

            return $this->success($data);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionChangeJob()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $chatApp = ChatApplication::getInstance();
            $params  = [
                // 先改成get
                'chatId'          => \Yii::$app->request->post('chatId'),
                'jobId'           => \Yii::$app->request->post('jobId'),
                'companyMemberId' => \Yii::$app->user->id,
            ];
            $chatApp->companychangeJob($params);

            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位判断是否可以同意发送附件
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckRequestFile()
    {
        $jobId  = Yii::$app->request->post('jobId');
        $chatId = Yii::$app->request->post('chatId');

        try {
            $app  = ChatApplication::getInstance();
            $data = $app->checkCompanyRequestFile($jobId, $chatId);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查单位用户邀请投递权限
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckInviteApply()
    {
        $companyMemberId = Yii::$app->user->id;
        try {
            $app  = ChatApplication::getInstance();
            $data = $app->checkCompanyInviteApply($companyMemberId);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    // 获取聊天室内容
    public function actionGetResumeInfo()
    {
        $chatId          = Yii::$app->request->get('chatId');
        $companyMemberId = Yii::$app->user->id;

        $app  = ChatApplication::getInstance();
        $data = $app->companyGetRoomResumeInfo($chatId, $companyMemberId);

        return $this->success($data);
    }

    /**
     * 获取职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetJobList()
    {
        $chatId   = Yii::$app->request->get('chatId');
        $memberId = Yii::$app->user->id;
        try {
            $app  = ChatApplication::getInstance();
            $data = $app->getChatJobList($chatId, $memberId);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 返回免费、过期、试用会员提示
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetFreeTipsInfo()
    {
        $companyId = $this->getCompanyId();

        // 首先必填参数
        if (!$companyId) {
            return $this->fail('参数错误');
        }

        try {
            $service = CommonResumeApplication::getInstance();
            $data    = $service->getChatTipsInfo($companyId);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发送短信求职者
     */
    public function actionChatRoomSendSmsToResume()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $isOnlyCheck = Yii::$app->request->get('isOnlyCheck');
            $companyId   = $this->getCompanyId();
            $resumeId    = Yii::$app->request->get('resumeId');

            $res = (new ChatApplication())->chatRoomSendSmsToResume($isOnlyCheck, $companyId, $resumeId, \Yii::$app->user->id);
            $transaction->commit();
            return $this->success($res);
        } catch (\Exception $e) {
            $transaction->rollBack();
            return $this->fail($e->getMessage());
        }
    }
}