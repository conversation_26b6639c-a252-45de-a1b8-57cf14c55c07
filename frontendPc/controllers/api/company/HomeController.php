<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseFile;
use common\base\models\BaseMember;
use common\base\models\BaseResumeLibrary;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\FormatConverter;
use common\libs\Cache;
use frontendPc\models\Company;
use frontendPc\models\CompanyCooperationApply;
use Yii;
use yii\base\Exception;
use yii\web\Response;

class HomeController extends BaseFrontPcApiCompanyController
{
    /**
     * 申请合作
     * @return \yii\console\Response|Response
     * @throws Exception
     */
    public function actionAddApplyInfo()
    {
        $result = Yii::$app->request->post();

        CompanyCooperationApply::create(FormatConverter::convertHump($result));

        return $this->success('', '');
    }

    /**
     * 等待审核中不可用
     * 获取审核信息
     */
    public function actionLoadAuthInfo()
    {
        return $this->success(Company::getAuthInfo());
    }

    /**
     * 获取审核信息里面所需的字典
     */
    public function actionLoadAuthInfoParams()
    {
        return $this->success([
            'natureList'   => ArrayHelper::obj2Arr(Company::getNatureList()),
            'typeList'     => ArrayHelper::obj2Arr(Company::getTypeList()),
            'industryList' => ArrayHelper::obj2Arr(Company::getIndustryList()),
            'areaList'     => ArrayHelper::obj2Arr(Company::getAreaList()),
        ]);
    }

    /**
     * 等待认证/通过认证后不可用
     * 提交第一阶段的认证
     */
    public function actionSubmitAuthPhaseOne()
    {
        $data = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $model = new Company();
            $model->submitAuthPhaseOne($data);

            $transaction->commit();
            return $this->success('');
        } catch (\Exception $e) {
            $transaction->rollBack();
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 等待认证/通过认证后不可用
     * 提交第二阶段的认证
     */
    public function actionSubmitAuthPhaseTwo()
    {
        $data = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $model = new Company();
            $model->submitAuthPhaseTwo($data);

            $transaction->commit();
            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 等待认证/通过认证后不可用
     * 提交第三阶段的认证
     */
    public function actionSubmitAuthPhaseThree()
    {
        $data = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $model = new Company();
            $model->submitAuthPhaseThree($data);

            $transaction->commit();
            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取公司联系信息
     * @return \yii\console\Response|Response
     */
    public function actionGetContactInfo()
    {
        try {
            $model = new Company();
            $info  = $model->getContactInfo();

            return $this->success($info);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 静态文件
     * @return \yii\console\Response|Response
     */
    public function actionGetCertificateUrl()
    {
        $list = Yii::$app->params['localStatic']['company']['certificate'];
        foreach ($list as &$item) {
            $item = FileHelper::getFullUrl($item, BaseFile::PLATFORM_TYPE_LOCAL);
        }

        return $this->success($list);
    }

    public function actionTalentDataCard()
    {
        // 这里暂时定给前端直接显示的数据
        // http://zentao.jugaocai.com/index.php?m=story&f=view&id=423

        $key       = Cache::COMPANY_TALENT_CARD_KEY;
        $cacheData = Cache::get($key);
        if ($cacheData) {
            $data = json_decode($cacheData, true);
        } else {
            // 近30日活跃人数
            $last30DayActiveCount = BaseMember::find()
                ->where(['type' => BaseMember::TYPE_PERSON])
                ->andWhere([
                    '>',
                    'last_active_time',
                    date('Y-m-d H:i:s', CUR_TIMESTAMP - 30 * 7 * 3600),
                ])
                ->count();

            $count = $last30DayActiveCount * 2.7;
            // 四舍五入
            $count = round($count);

            $totalTalentAmount = BaseResumeLibrary::find()
                ->count();
            // 除以 1w，取整,
            $totalTalentAmount = floor($totalTalentAmount / 10000);

            $data = [
                [
                    'title'  => '累计入库博硕人才',
                    'amount' => $totalTalentAmount,
                    'unit'   => '万+',
                ],
                [
                    /**
                     * “近30日活跃人才”数据规则：
                     * 1、计算规则：
                     * 近7天（不包含当前提）平台活跃人才数据*2.7；
                     * 取整，四舍五入；
                     */
                    'title'  => '近30日活跃人数',
                    'amount' => $count,
                    'unit'   => '人',
                ],
                [
                    'title'  => '海外学历及工作经历人才占比',
                    'amount' => '17',
                    'unit'   => '%',
                ],
                [
                    'title'  => '双一流院校及中科院毕业人才占比',
                    'amount' => '56',
                    'unit'   => '%',
                ],
            ];

            Cache::set($key, json_encode($data), 3600);
        }

        return $this->success($data);
    }

    /*
     * 登录页面的数据统计
     */
    public function actionLoginStatistics()
    {
        $libraryCount = Cache::get(Cache::PC_COMPANY_RESUME_LIBRARY_COUNT_NUMBER);
        if (!$libraryCount) {
            //统计人才库数量
            $libraryCount = BaseResumeLibrary::find()
                ->count();
            //向下取整数万
            $libraryCount = floor($libraryCount / 10000);
            Cache::set(Cache::PC_COMPANY_RESUME_LIBRARY_COUNT_NUMBER, $libraryCount, 86400);
        }

        //【高层次人才注册量】，调整为动态获取数据：实时读取人才注册数量展示，以“万”为计数单位，舍尾保留整数；
        $memberCount = Cache::get(Cache::PC_COMPANY_PERSON_COUNT_NUMBER);
        if (!$memberCount) {
            //统计人才库数量
            $memberCount = BaseMember::find()
                ->where(['type' => BaseMember::TYPE_PERSON])
                ->count();
            //向下取整数万
            $memberCount = floor($memberCount / 10000);
            Cache::set(Cache::PC_COMPANY_PERSON_COUNT_NUMBER, $memberCount, 86400);
        }

        return $this->success([
            'moduleOne' => [
                [
                    'text'   => '日在招职位量',
                    'count'  => 30,
                    'prefix' => '',
                    'suffix' => '万+',
                ],
                [
                    'text'   => '官网日页面浏览量',
                    'count'  => 80,
                    'prefix' => '近',
                    'suffix' => '万',
                ],
                [
                    'text'   => '高层次人才注册量',
                    'count'  => (int)$memberCount,
                    'prefix' => '',
                    'suffix' => '万+',
                ],
                [
                    'text'   => '高层次人才库',
                    'count'  => (int)$libraryCount,
                    'prefix' => '',
                    'suffix' => '万+',
                ],
                [
                    'text'   => '双一流高校及中科院人才占比',
                    'count'  => 56,
                    'prefix' => '',
                    'suffix' => '%',
                ],
            ],
            'moduleTwo' => (int)$libraryCount,
        ]);
    }
}