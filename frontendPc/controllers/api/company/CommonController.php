<?php
/**
 * create user：shannon
 * create time：2024/4/29 17:04
 */
namespace frontendPc\controllers\api\company;

use common\base\models\BaseCompanyMemberInfo;
use common\service\companyAuth\BaseButtonService;
use common\service\companyAuth\ButtonCallAuthService;
use common\service\companyAuth\TipsService;
use common\service\companyPackage\companyEquityConsultNoticeService;
use yii\base\Exception;
use yii\console\Response;
use Yii;

class CommonController extends BaseFrontPcApiCompanyController
{
    /**
     * 按钮权限判断
     * @return Response|\yii\web\Response
     */
    public function actionButtonAuth()
    {
        try {
            $eventKey = $this->request->post('eventKey');
            if (!$eventKey) {
                return $this->fail('参数错误');
            }
            $buttonId = BaseButtonService::BUTTON_EVENT_TO_ID[$eventKey];
            if ($buttonId <= 0) {
                return $this->fail('事件参数错误');
            }
            //这里做一下前置权限
            $res = (new ButtonCallAuthService())->setButtonId($buttonId)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取全局权益提示
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGlobalTips()
    {
        try {
            //获取类型
            $type = Yii::$app->request->get('type');
            if (!empty ($type) && !in_array($type, BaseButtonService::TIPS_TYPE_EQUITY)) {
                throw new Exception('参数错误');
            }
            if (empty ($type)) {
                return $this->success();
            }

            $data = (new TipsService)->run($type);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位触发高才通权益咨询通知
     */
    public function actionEquityConsultNotice()
    {
        //获取登录memberId
        $memberId = Yii::$app->user->id;

        (new companyEquityConsultNoticeService())->run($memberId);

        return $this->result('', 1);
    }
}