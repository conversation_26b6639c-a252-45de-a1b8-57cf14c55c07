<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\libs\Captcha;
use common\libs\SmsQueue;
use Yii;

class MemberController extends BaseFrontPcApiCompanyController
{
    /**
     * 发送手机登录验证码
     */
    public function actionSendMobileLoginCode()
    {
        $request = Yii::$app->request->post();
        $type    = BaseMember::TYPE_COMPANY;

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobileCode = $request['mobileCode'];
        $loginForm->mobile     = $request['mobile'];
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->type       = $type;
        $loginForm->smsType    = SmsQueue::TYPE_LOGIN;

        try {
            $loginForm->sendMobileCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 发送手机号注册验证码
     */
    public function actionSendMobileRegisterCode()
    {
        $request = Yii::$app->request->post();
        $type    = BaseMember::TYPE_COMPANY;

        $memberLoginForm             = new BaseMemberLoginForm();
        $memberLoginForm->mobile     = $request['mobile'];
        $memberLoginForm->mobileCode = $request['mobileCode'];
        $memberLoginForm->smsType    = SmsQueue::TYPE_REGISTER;
        $memberLoginForm->type       = $type;

        // 图形校验
        $ticket  = Yii::$app->request->post('ticket');
        $randStr = Yii::$app->request->post('randstr');
        if (!$randStr || !$ticket) {
            return $this->fail('图形验证码验证失败');
        }

        $captcha = new Captcha();
        if (!$captcha->check($ticket, $randStr)) {
            return $this->fail('图形验证码验证失败');
        }
        try {
            $memberLoginForm->sendMobileCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 手机号注册提交
     */
    public function actionCreateMobileRegister()
    {
        $request = Yii::$app->request->post();
        $type    = BaseMember::TYPE_COMPANY;

        $memberLoginForm             = new BaseMemberLoginForm();
        $memberLoginForm->password   = $request['password'];
        $memberLoginForm->mobileCode = $request['mobileCode'];
        $memberLoginForm->mobile     = $request['mobile'];
        $memberLoginForm->code       = $request['code'];
        $memberLoginForm->type       = $type;
        $memberLoginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = $memberLoginForm->companyMobileRegister();
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     *邮箱注册提交
     */
    public function actionCreateEmailRegister()
    {
        $request = Yii::$app->request->post();
        $type    = BaseMember::TYPE_COMPANY;

        $memberLoginForm           = new BaseMemberLoginForm();
        $memberLoginForm->email    = $request['email'];
        $memberLoginForm->code     = $request['code'];
        $memberLoginForm->password = $request['password'];
        $memberLoginForm->type     = $type;
        $transaction               = Yii::$app->db->beginTransaction();
        try {
            $data = $memberLoginForm->validateEmailCode();
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证邮箱格式，验证是否存在
     */
    public function actionValidateEmail()
    {
        $email = Yii::$app->request->get('email');

        $loginForm = new BaseMemberLoginForm();

        $loginForm->email = $email;
        $loginForm->type  = BaseMember::TYPE_COMPANY;

        try {
            $loginForm->validateEmail();

            return $this->success('邮箱未绑定');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}
