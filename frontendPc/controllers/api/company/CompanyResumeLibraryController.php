<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseJobApplyHandleLog;
use common\base\models\BaseResume;
use common\service\companyResume\CompanyResumeApplication;
use Yii;

class CompanyResumeLibraryController extends BaseFrontPcApiCompanyController
{
    /**
     * 获取列表数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        $searchData              = Yii::$app->request->get();
        $companyId               = $this->getCompanyId();
        $searchData['companyId'] = $companyId;
        $searchData['memberId']  = Yii::$app->user->id;
        // 首先必填参数
        if (!$companyId) {
            return $this->fail('参数错误');
        }

        try {
            $service = CompanyResumeApplication::getInstance();
            $list    = $service->getList($searchData);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionGetPersonDetailInfo()
    {
        $companyId           = $this->getCompanyId();
        $params              = Yii::$app->request->get();
        $params['companyId'] = $companyId;

        //加个判断--普通子账号不能访问
        //        $member_rule = BaseCompanyMemberInfo::findOneVal(['member_id' => Yii::$app->user->id], 'member_rule');
        //        if($member_rule == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL){
        //            return $this->fail('当前账号没有访问权限哦');
        //        }
        if (empty($params['resumeId']) || empty($companyId)) {
            return $this->fail('参数错误');
        }
        //判断简历是否存在
        $resumeInfo = BaseResume::findOne(['id' => $params['resumeId']]);
        if (empty($resumeInfo)) {
            return $this->fail('简历不存在');
        }
        //判断简历是否存在单位简历库中
        $companyResumeLibraryInfo = BaseCompanyResumeLibrary::findOne([
            'company_id' => $companyId,
            'resume_id'  => $params['resumeId'],
        ]);
        if (empty($companyResumeLibraryInfo)) {
            return $this->fail('简历不存在');
        }

        $service = CompanyResumeApplication::getInstance();
        $result  = $service->getDetail($params);
        if ($result['source_bool']) {
            //特殊处理 要注意
            $result['source_type'] = 1;
        }
        //做一个PV统计
        BaseCompanyResumePvTotal::updateDailyTotalPv($params['resumeId']);

        return $this->success($result);
    }

    /**
     * 获取操作日志
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetHandleLog()
    {
        $params              = Yii::$app->request->get();
        $params['companyId'] = $this->getCompanyId();
        if (empty($params['resumeId']) || empty($params['companyId'])) {
            return $this->fail('参数错误');
        }
        //判断简历是否存在
        $resumeInfo = BaseResume::findOne(['id' => $params['resumeId']]);
        if (empty($resumeInfo)) {
            return $this->fail('简历不存在');
        }
        //判断简历是否存在单位简历库中
        $companyResumeLibraryInfo = BaseCompanyResumeLibrary::findOne([
            'company_id' => $params['companyId'],
            'resume_id'  => $params['resumeId'],
        ]);
        if (empty($companyResumeLibraryInfo)) {
            return $this->fail('简历不存在');
        }
        $service = CompanyResumeApplication::getInstance();

        return $this->success($service->getHandleLog($params));
    }

    public function actionDownload()
    {
        $companyId = $this->getCompanyId();
        $id        = Yii::$app->request->get('id');

        $transaction = Yii::$app->db->beginTransaction();

        // 找到这个在单位简历库里面的简历id
        $resumeId = BaseCompanyResumeLibrary::findOneVal([
            'id'         => $id,
            'company_id' => $companyId,
        ], 'resume_id');

        if (!$resumeId) {
            return $this->fail('简历不存在');
        }

        try {
            $service = CompanyResumeApplication::getInstance();

            $service->download($companyId, $resumeId);
            $transaction->commit();

            exit;
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 保存简历标签
     * @return void|\yii\console\Response|\yii\web\Response
     */
    public function actionSaveTag()
    {
        $data      = Yii::$app->request->post();
        $resumeId  = $data['resumeId'];
        $companyId = $this->getCompanyId();

        $tag = $data['tag'];
        if (!$data['resumeId'] || !$companyId) {
            return $this->fail('请求参数错误');
        }
        //判断数据是否存在
        $record = BaseCompanyResumeLibrary::findOne([
            'resume_id'  => $resumeId,
            'company_id' => $companyId,
        ]);
        if (!$record) {
            return $this->fail('简历数据不存在');
        }

        $transaction = Yii::$app->db->beginTransaction();

        try {
            $service = CompanyResumeApplication::getInstance();

            $service->saveTag($companyId, $resumeId, $tag);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 保存评论
     * @return void|\yii\console\Response|\yii\web\Response
     */
    public function actionSaveComment()
    {
        $data      = Yii::$app->request->post();
        $resumeId  = $data['resumeId'];
        $companyId = $this->getCompanyId();

        $comment = $data['comment'];
        if (!$data['resumeId'] || !$companyId) {
            return $this->fail('请求参数错误');
        }
        if (!$comment) {
            return $this->fail('评论内容不能为空');
        }
        //判断数据是否存在
        $record = BaseCompanyResumeLibrary::findOne([
            'resume_id'  => $resumeId,
            'company_id' => $companyId,
        ]);
        if (!$record) {
            return $this->fail('简历数据不存在');
        }

        $transaction = Yii::$app->db->beginTransaction();

        try {
            $service = CompanyResumeApplication::getInstance();

            $service->saveComment($companyId, $resumeId, $comment);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    public function actionSavePrintLog()
    {
        $data      = Yii::$app->request->post();
        $companyId = $this->getCompanyId();
        $resumeId  = $data['resumeId'];

        if (!$resumeId || !$companyId) {
            return $this->fail('请求参数错误');
        }
        //判断数据是否存在
        $record = BaseCompanyResumeLibrary::findOne([
            'resume_id'  => $resumeId,
            'company_id' => $companyId,
        ]);
        if (!$record) {
            return $this->fail('简历数据不存在');
        }

        $transaction = Yii::$app->db->beginTransaction();

        try {
            $data = [
                'resumeId'  => $resumeId,
                'companyId' => $companyId,
                'type'      => BaseJobApplyHandleLog::TYPE_PRINT_RESUME,
                'content'   => '打印了简历',
            ];
            BaseJobApplyHandleLog::saveCompanyHandleLog($data);

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

}