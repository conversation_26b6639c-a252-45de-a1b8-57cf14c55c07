<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseJob;
use common\base\models\BaseMemberMessage;
use common\helpers\FormatConverter;
use common\libs\CompanyAuthority\CompanyAuthorityClassify;
use common\service\commonResume\CommonResumeApplication;
use common\service\CommonService;
use common\service\companyAuth\ButtonGroupAuthService;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\match\CommonMatchService;
use common\service\match\JobToPersonNewService;
use common\service\match\JobToPersonService;
use frontendPc\models\Company;
use frontendPc\models\Job;
use frontendPc\models\MemberSchedule;
use frontendPc\models\Resume;
use frontendPc\models\UnitCenter;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class UnitCenterController extends BaseFrontPcApiCompanyController
{
    /**
     * 单位中心--企业信息
     * @return Response|\yii\web\Response
     */
    public function actionGetCompanyInfo()
    {
        $memberId = Yii::$app->user->id;

        return $this->success(UnitCenter::getCompanyInfo($memberId));
    }

    /**
     * 单位中心--获取消息列
     * @return Response|\yii\web\Response
     */
    public function actionGetNewsList()
    {
        $memberId = Yii::$app->user->id;

        return $this->success(UnitCenter::getNewsList($memberId));
    }

    /**
     * 单位中心--经办人认证
     * @return Response|\yii\web\Response
     */
    public function actionAuthentication()
    {
        $request = Yii::$app->request->post();

        try {
            $model = new Company();
            $model->submitAuthPhaseThree($request);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位中心--统计信息
     * @return Response|\yii\web\Response
     */
    public function actionGetStatisticsList()
    {
        $memberId = Yii::$app->user->id;

        return $this->success(UnitCenter::getStatisticsList($memberId));
    }

    /**
     * 单位中心--我的服务
     * @return Response|\yii\web\Response
     */
    public function actionGetMyService()
    {
        $memberId = Yii::$app->user->id;

        return $this->success(UnitCenter::getMyService($memberId));
    }

    /**
     * 单位中心--我的专属客服
     * @return Response|\yii\web\Response
     */
    public function actionCustomerService()
    {
        return $this->success(UnitCenter::customerService());
    }

    /**
     * 单位中心--我的日程
     * @return Response|\yii\web\Response
     */
    public function actionGetMySchedule()
    {
        $request  = Yii::$app->request->get();
        $memberId = Yii::$app->user->id;

        return $this->success(MemberSchedule::getMySchedule(FormatConverter::convertHump($request), $memberId));
    }

    /**
     * 单位中心--获取日程信息
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetMemberSchedule()
    {
        $request = Yii::$app->request->get();
        if (strlen($request['id']) < 1) {
            throw new Exception('参数id不能为空');
        }

        return $this->success(UnitCenter::getMemberSchedule(FormatConverter::convertHump($request)));
    }

    /**
     * 单位中心--删除日程
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionDeleteMemberSchedule()
    {
        $request = Yii::$app->request->post();
        if (strlen($request['id']) < 1) {
            throw new Exception('参数id不能为空');
        }

        return $this->success(UnitCenter::deleteMemberSchedule($request));
    }

    /**
     * 套餐明细
     * @return Response|\yii\web\Response
     */
    public function actionPackageDetail()
    {
        try {
            return $this->success(UnitCenter::packageDetail());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 消费记录
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetCompanyPackageChangeLog()
    {
        $request = Yii::$app->request->get();

        return $this->success(UnitCenter::getCompanyPackageChangeLog(FormatConverter::convertHump($request)));
    }

    /**
     * 意见建议反馈
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionMemberSuggestion()
    {
        $memberId = Yii::$app->user->id;
        $request  = Yii::$app->request->post();
        if (strlen($request['content']) < 1) {
            throw new Exception('请输入相关建议意见信息');
        }

        return $this->success(UnitCenter::memberSuggestion($request, $memberId));
    }

    /**
     * 单位中心--广告位
     * @return Response|\yii\web\Response
     */
    public function actionGetShowcase()
    {
        return $this->success(UnitCenter::getShowcase());
    }

    /**
     * 单位中心--套餐明细--服务项目
     * @return Response|\yii\web\Response
     */
    public function actionGetPackageTypeList()
    {
        try {
            $searchList = BaseCompanyPackageChangeLog::SEARCH_TYPE_NAME;
            $list       = [];
            foreach ($searchList as $k => $item) {
                if (!in_array($k, [
                    BaseCompanyPackageChangeLog::TYPE_SUB_ACCOUNT,
                    BaseCompanyPackageChangeLog::TYPE_VIP_AUTH,
                ])) {
                    $list[] = [
                        'k' => $k,
                        'v' => $item,
                    ];
                }
            }

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位中心--套餐明细--操作类型
     * @return Response|\yii\web\Response
     */
    public function actionGetPackageHandleTypeList()
    {
        try {
            $searchList = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME;
            $list       = [];
            foreach ($searchList as $k => $item) {
                if ($k != BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_SUB) {
                    $list[] = [
                        'k' => $k,
                        'v' => $item,
                    ];
                }
            }

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位主页人才推荐
     * @return Response|\yii\web\Response
     */
    public function actionRecommendResumeList()
    {
        try {
            $job_id         = Yii::$app->request->get('jobId');
            $job_info       = BaseJob::findOne($job_id);
            $search_bool    = false;
            $recommend_list = [];
            $search_list    = [];
            $page           = 1;
            $memberId       = Yii::$app->user->id;
            if ($job_info) {
                //获取单位信息--这里改成用 账号系统拿单位id
                $companyMemberInfo = BaseCompanyMemberInfo::findOne([
                    'member_id' => $memberId,
                ]);

                if ($job_info->company_id != $companyMemberInfo->company_id) {
                    throw new \Exception('没有权限获取数据');
                }
                $service  = new JobToPersonService();
                $type     = CommonMatchService::LIST_TYPE_RECOMMEND;
                $pageSize = 10;
                $params   = [
                    'jobId'    => $job_id,
                    'type'     => $type,
                    'page'     => $page,
                    'pageSize' => $pageSize,
                    'isButton' => true,
                ];
                $data     = $service->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                    ->init($params)
                    ->run();

                //                $model = new JobToPersonNewService();
                //                $data  = $model->getList([
                //                    'jobId'    => $job_id,
                //                    'type'     => CommonMatchService::LIST_TYPE_RECOMMEND,
                //                    'platform' => CommonService::PLATFORM_WEB_COMPANY,
                //                    'page'     => $page,
                //                    'pageSize' => 10,
                //                ]);
                if (count($data['list']) == 0) {
                    $search_bool = true;
                } else {
                    $recommend_list = $data;
                    // 特殊处理一下按钮组
                    foreach ($recommend_list['list'] as &$item) {
                        $item['buttonGroup'] = (new ButtonGroupAuthService())->setType(ButtonGroupAuthService::TYPE_RESUME_CENTER)
                            ->run($item['resume_id']);
                    }
                }
            } else {
                $search_bool = true;
            }
            if ($search_bool) {
                $pageSize                = 6;
                $searchData              = [
                    'page'     => $page,
                    'pageSize' => $pageSize,
                ];
                $companyId               = $this->getCompanyId();
                $searchData['companyId'] = $companyId;
                // 首先必填参数
                if (!$companyId) {
                    return $this->fail('参数错误');
                }
                $service     = CommonResumeApplication::getInstance();
                $list        = $service->getList($searchData);
                $search_list = $list;
                // 特殊处理一下按钮组
                if ($search_list['list']) {
                    foreach ($search_list['list'] as &$item) {
                        $item['buttonGroup'] = (new ButtonGroupAuthService())->setType(ButtonGroupAuthService::TYPE_RESUME_CENTER)
                            ->run($item['resume_id']);
                    }
                }
            }

            /** 这里拿一下账号的权限*/
            $companyAuthorityList = (new CompanyAuthorityClassify())->justGetCompanyResumeLibraryList([
                'memberId'         => $memberId,
                'companyAuthority' => 'findTalentList',
            ]);

            $result = [
                'recommend_list'         => $recommend_list,
                'search_list'            => $search_list,
                'company_authority_list' => $companyAuthorityList,
            ];

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位中心--套餐明细-操作人
     * @return Response|\yii\web\Response
     */
    public function actionGetHandlerList()
    {
        try {
            $memberId  = Yii::$app->user->id;
            $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');

            return $this->success(BaseCompanyMemberInfo::getAllCompanyMemberInfo($companyId));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位主页权益信息
     * @return Response|\yii\web\Response
     */
    public function actionGetCompanyPackageDetail()
    {
        try {
            $memberId  = Yii::$app->user->id;
            $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');

            $smsSetting = UnitCenter::getCompanyIsRememberSms($memberId);
            $packageDetail = BaseCompanyPackageConfig::getCompanyPackageConfigDetail($companyId);

            $detail = array_merge($packageDetail, $smsSetting);

            return $this->success($detail);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}