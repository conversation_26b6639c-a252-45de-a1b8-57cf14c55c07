<?php

namespace frontendPc\controllers\api\company;

use admin\models\ResumeDownloadLog;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseCompanyViewResume;
use common\base\models\BaseFile;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseMember;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResumeAttachment;
use common\helpers\FileHelper;
use common\helpers\FormatConverter;
use common\helpers\MaskHelper;
use common\libs\ResumeHandle;
use frontendPc\models\JobApply;
use frontendPc\models\Resume;
use frontendPc\models\ResumeAttachment;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class ResumeController extends BaseFrontPcApiCompanyController
{
    /**
     * 获取个人简历信息
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetInfo()
    {
        $request = Yii::$app->request->get();
        if (!$request['jobApplyId']) {
            return $this->fail('投递Id不能为空');
        }
        $memberId = Yii::$app->user->id;

        if (!$memberId) {
            return $this->fail('用户登陆状态错误');
        }

        if ($request['tag'] == BaseJob::DELIVERY_TYPE_OUTSIDE) {
            //单位
            $applyInfo = BaseJobApplyRecord::findOne([
                'apply_site_id' => $request['jobApplyId'],
            ]);
            if (!$applyInfo) {
                return $this->fail('投递记录不存在');
            }
            $company_member_id = BaseCompany::findOneVal(['id' => $applyInfo->company_id], 'member_id');
            //            if ($memberId != $company_member_id) {
            //                return $this->fail('无权访问');
            //            }
            //站外投递简历详情
            $data       = Resume::companyCheckResumeInfoOff($request['jobApplyId']);
            $company_id = $applyInfo->company_id;
            $resume_id  = $applyInfo->resume_id;
        } else {
            $company_id   = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
            $jobApplyInfo = BaseJobApply::findOne([
                'id'         => $request['jobApplyId'],
                //'company_member_id' => $memberId,
                'company_id' => $company_id,
            ]);
            if (!$jobApplyInfo) {
                return $this->fail('非法操作');
            }

            // 这里有不少业务,在这里启动一下事务
            $transaction = Yii::$app->db->beginTransaction();
            try {
                // 简历被查看,记录日志
                $data = Resume::companyCheckResumeInfo($request['jobApplyId']);
                $transaction->commit();
            } catch (Exception $e) {
                $transaction->rollBack();

                return $this->fail($e->getMessage());
            }
            //$company_id = $jobApplyInfo->company_id;
            $resume_id = $jobApplyInfo->resume_id;
        }
        // 之前的消息中心并入这里
        BaseCompanyViewResume::create($company_id, $resume_id);
        //做一个PV统计
        BaseCompanyResumePvTotal::updateDailyTotalPv($resume_id);

        return $this->success($data);
    }

    /**
     * 获取简历附件列表
     * @return Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetResumeAttachmentList()
    {
        $request = Yii::$app->request->get();
        if (!$request['jobApplyId']) {
            return $this->fail('简历申请Id不能为空');
        }

        return $this->success(Resume::getResumeAttachmentList(FormatConverter::convertHump($request)));
    }

    /**
     * 获取求职附件列表
     * @return Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetResumeFileList()
    {
        $request = Yii::$app->request->get();
        if (!$request['jobApplyId']) {
            return $this->fail('简历申请Id不能为空');
        }

        // 对于企业来说,求职附件需要单独设置下载
        $list = Resume::getResumeFileList(FormatConverter::convertHump($request));

        foreach ($list as &$item) {
            // 这里有点丑
            $item['fileUrl'] = '/api/company/job-apply/download-resume-file?jobApplyId=' . $request['jobApplyId'] . '&id=' . $item['id'];
        }

        return $this->success($list);
    }

    /**
     * 获取求职者对该单位投递的附件材料列表
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetCompanyResumeFileList()
    {
        $request              = Yii::$app->request->get();
        $request['companyId'] = $this->getCompanyId();

        if (!$request['resumeId'] || !$request['companyId']) {
            return $this->fail('参数错误');
        }

        // 对于企业来说,求职附件需要单独设置下载
        $list = Resume::getCompanyResumeFileList($request);

        return $this->success($list);
    }

    /**
     * 获取求职者对该单位投递的附件简历列表
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetCompanyResumeAttachmentList()
    {
        $request              = Yii::$app->request->get();
        $request['companyId'] = $this->getCompanyId();

        if (!$request['resumeId'] || !$request['companyId']) {
            return $this->fail('参数错误');
        }

        // 对于企业来说,求职附件需要单独设置下载
        $list = Resume::getCompanyResumeAttachmentList($request);

        return $this->success($list);
    }

    public function actionPreviewAttachment()
    {
        $request = Yii::$app->request->get();
        if (!$request['jobApplyId']) {
            return $this->fail('简历申请Id不能为空');
        }

        // 首先根据申请去拿简历附件的id
        $memberId = Yii::$app->user->id;
        if ($request['tag'] == BaseJob::DELIVERY_TYPE_OUTSIDE) {
            // 站外(判断一下是他的简历)
            $resumeAttachmentId = BaseOffSiteJobApply::findOneVal([
                'id' => $request['jobApplyId'],
            ], 'resume_attachment_id');
            $company_id         = BaseJobApplyRecord::findOneVal([
                'apply_site_id' => $request['jobApplyId'],
            ], 'company_id');
        } else {
            $resumeAttachmentId = JobApply::findOneVal([
                'id' => $request['jobApplyId'],
            ], 'resume_attachment_id');
            $company_id         = JobApply::findOneVal([
                'id' => $request['jobApplyId'],
            ], 'company_id');
        }

        //当前登录人的单位ID
        $checkCompanyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');

        if ($company_id != $checkCompanyId) {
            return $this->fail('无权查看该简历');
        }

        // 找到对应的token
        $rs = ResumeAttachment::findOne([
            'id' => $resumeAttachmentId,
        ]);
        if (!$rs) {
            return $this->fail('简历附件不存在');
        }

        try {
            $model = new ResumeHandle();
            $model->setOperator($memberId, ResumeHandle::OPERATOR_TYPE_MEMBER)
                ->setData($rs->resume_id, $rs->token, $request['jobApplyId'])
                ->preview();

            exit;
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionView()
    {
        $resumeId = Yii::$app->request->get('resumeId');
    }

    /**
     * 获取简历的投递记录
     * @return void|Response|\yii\web\Response
     */
    public function actionGetApplyRecord()
    {
        $member_id    = Yii::$app->user->id;
        $company_info = BaseCompany::findOne([
            'member_id' => $member_id,
            "status"    => BaseCompany::STATUS_ACTIVE,
        ]);
        $resume_id    = Yii::$app->request->get('resumeId');
        if ($resume_id <= 0) {
            return $this->fail('简历不存在');
        }
        $list     = BaseJobApplyRecord::getCompanyApplyRecord($company_info, $resume_id);
        $result   = [];
        $cur_year = date('Y');
        foreach ($list as $item) {
            $item['stuff_list'] = [];
            if (!empty($item['stuff_file_id'])) {
                $stuff_id_arr = explode(',', $item['stuff_file_id']);
                $stuff_list   = BaseFile::getIdsList($stuff_id_arr);
                foreach ($stuff_list as &$value) {
                    if (!empty($value['path'])) {
                        $value['path']   = '/api/company/job-apply/download-resume-file?jobApplyId=' . $item['apply_id'] . '&id=' . $value['id'];
                        $value['suffix'] = FileHelper::getFileSuffixClassName($value['suffix']);
                    }
                    unset($value['add_time'], $value['status'], $value['platform'], $value['size'], $value['width'], $value['height'], $value['creator_id'], $value['creator_type']);
                }
                $item['stuff_list'] = $stuff_list;
            }
            //状态文本
            $item['status_text']          = BaseJobApply::STATUS_LIST[$item['status']];
            $item['resume_attachment_id'] = empty($item['resume_attachment_id']) ? '' : $item['resume_attachment_id'];
            $item['token']                = BaseResumeAttachment::findOneVal(['id' => $item['resume_attachment_id']],
                'token');
            $year                         = date('Y', strtotime($item['add_time']));
            if ($cur_year == $year) {
                $key = date('m.d', strtotime($item['add_time']));
            } else {
                $key = date('Y.m.d', strtotime($item['add_time']));
            }
            unset($item['add_time']);
            if (isset($result[$key])) {
                array_push($result[$key]['list'], $item);
            } else {
                $result[$key]['time'] = $key;
                $result[$key]['list'] = [];
                array_push($result[$key]['list'], $item);
            }
        }
        $result = array_column($result, null);

        $this->success(['result' => $result]);
    }

    /**
     * 获取简历
     */
    public function actionGetResumeInfo()
    {
        try {
            $resumeId = \Yii::$app->request->get('resumeId');
            $resume   = Resume::findOne(['id' => $resumeId]);
            if (!$resume) {
                return $this->fail('简历不存在');
            }
            $memberId               = \Yii::$app->user->id;
            $companyInfo            = BaseCompany::findOne(['member_id' => $memberId]);
            $companyId              = $companyInfo->id;
            $companyResumeLibraryId = BaseCompanyResumeLibrary::findOneVal([
                'resume_id'  => $resumeId,
                'company_id' => $companyId,
            ], 'id');

            //名字是否脱敏
            $resumeName = $companyResumeLibraryId > 0 ? $resume->name : MaskHelper::getName($resume->name);

            return $this->success(['resumeName' => $resumeName]);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}