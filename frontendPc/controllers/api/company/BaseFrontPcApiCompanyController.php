<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseMember;
use common\base\models\BaseSeoUserAgent;
use common\helpers\DebugHelper;
use common\helpers\IpHelper;
use frontendPc\controllers\api\BaseFrontPcApiController;
use frontendPc\models\Member;
use Yii;

class BaseFrontPcApiCompanyController extends BaseFrontPcApiController
{

    public $beginTime = 0;
    public $endTime   = 0;

    public function beforeAction($action)
    {
        $this->beginTime = microtime(true);
        if (!parent::beforeAction($action)) {
            return false;
        }

        if (in_array($action->uniqueID, $this->ignoreLogin())) {
            return parent::beforeAction($action);
        }

        // 这里需要判断这个企业是否是已经通过审核了,如果不是,某些接口是不允许调用的
        if (!$this->checkAuth($action->uniqueID)) {
            $this->loginExpire();
        }

        return parent::beforeAction($action);
    }

    public function afterAction($action, $result)
    {
        $this->endTime = microtime(true);
        $time          = $this->endTime - $this->beginTime;

        if ($time > 2) {
            // $url = Yii::$app->request->url;
            // $ip  = IpHelper::getIp();
            //
            // // 找到单位的名称
            // $name = BaseCompany::findOneVal([
            //     'id' => $this->getCompanyId(),
            // ], 'full_name');
        }

        return parent::afterAction($action, $result);
    }

    /**
     * 这里是不需要任何的权限的
     * @return string[]
     */
    public function ignoreLogin()
    {
        return [
            //申请合作接口
            'api/company/home/<USER>',
            //发送企业用户手机验证码
            'api/company/member/send-mobile-register-code',
            //发送绑定手机号验证码
            'api/company/member/send-mobile-bind-code',
            //绑定手机号提交
            'api/company/member/bind-mobile',
            //企业用户手机号注册提交
            'api/company/member/create-mobile-register',
            //企业用户邮箱注册提交
            'api/company/member/create-email-register',
            //发送手机号登录手机验证码
            'api/company/member/send-mobile-login-code',
            // 获取资料
            'api/company/home/<USER>',
            // 硕博人才卡片信息
            'api/company/home/<USER>',
            // 登录页的数据统计
            'api/company/home/<USER>',
        ];
    }

    /**
     * 根据情况来检查这个用户有没有权限操作
     */
    public function checkAuth($uniqueID): bool
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }
        //登录时缓存company表status字段，初审通过/终审通过需要更新缓存
        $info   = BaseMember::getLoginInfo();
        $status = $info['status'];
        $type   = $info['type'];
        if ($type !== Member::TYPE_COMPANY) {
            return false;
        }

        // 等待复审状态下可访问
        $getActionWaitSecondAudit = [
            //提交单位认证信息第三阶段
            'api/company/home/<USER>',
            //获取已保存单位认证信息
            'api/company/home/<USER>',
            'api/company/unit-center/customer-service',
            'api/company/unit-center/get-company-info',
            'api/company/unit-center/get-statistics-list',
            'api/company/unit-center/get-news-list',
            'api/company/unit-center/get-my-service',
            'api/company/unit-center/get-my-schedule',
            'api/company/unit-center/get-company-package-detail',
            'api/company/job/online-list',
            'api/company/unit-center/get-showcase',
            'api/company/unit-center/recommend-resume-list',

        ];

        // 等待初审状态下可访问
        $getActionWaitFirstAudit = [
            //提交单位认证信息第一阶段
            'api/company/home/<USER>',
            //提交单位认证信息第二阶段
            'api/company/home/<USER>',
            //基本信息下拉菜单
            'api/company/home/<USER>',
            //获取已保存单位认证信息
            'api/company/home/<USER>',
            'api/company/unit-center/customer-service',
            'api/company/unit-center/get-company-package-detail',
            'api/company/job/online-list',
            'api/company/unit-center/get-showcase',
            'api/company/unit-center/recommend-resume-list',
        ];

        if ($status == BaseCompany::STATUS_ACTIVE) {
            // 正常用户
            return true;
        }

        //等待复审状态判断
        if ($status == BaseCompany::STATUS_WAIT_FIRST_AUDIT) {
            if (in_array($uniqueID, $getActionWaitFirstAudit)) {
                return true;
            }

            return false;
        }

        //等待复审状态判断
        if ($status == BaseCompany::STATUS_WAIT_SECOND_AUDIT) {
            if (in_array($uniqueID, $getActionWaitSecondAudit)) {
                return true;
            }

            return false;
        }

        return true;
    }

    /**
     * 过期
     */
    public function loginExpire()
    {
        echo json_encode([
            'code'   => 403,
            'result' => 0,
            'msg'    => '非法操作',
        ]);
        exit;
    }

    public function getCompanyId()
    {
        $info      = BaseMember::getLoginInfo();
        $companyId = $info['mainId'];
        if (empty($companyId)) {
            $memberId  = Yii::$app->user->id;
            $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
            //            $companyId = BaseCompany::findOneVal([
            //                'member_id' => $memberId,
            //            ], 'id');
        }

        return $companyId;
    }

    /**
     * @return mixed
     * 单位账户拿单位ID
     */
    public function getMemberCompanyId()
    {
        $info      = BaseMember::getLoginInfo();
        $companyId = $info['mainId'];
        if (empty($companyId)) {
            $memberId = Yii::$app->user->id;

            $companyId = BaseCompanyMemberInfo::findOneVal([
                'member_id' => $memberId,
            ], 'company_id');
        }

        return $companyId;
    }

    /**
     * 单位按钮权限返回
     * @param $data
     * @param $code
     * @param $msg
     * @return \yii\console\Response|\yii\web\Response
     */
    public function authError($data, $code = 307, $msg = '')
    {
        $response         = Yii::$app->response;
        $response->format = yii\web\Response::FORMAT_JSON;
        $ret              = [
            'data' => $data,
            'msg'  => $msg,
            'code' => $code,
        ];
        $response->data   = $ret;

        return $response;
    }
}