<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseJob;
use common\helpers\FormatConverter;
use frontendPc\models\JobApplyHandleLog;
use Yii;
use yii\web\Response;

class JobApplyHandleLogController extends BaseFrontPcApiCompanyController
{

    /**
     * 获取日志
     * @return \yii\console\Response|Response
     */
    public function actionGetList()
    {
        $request   = Yii::$app->request->get();
        if($request['jobApplyId']<0){
            return $this->fail('参数错误');
        }

        try {
            $list = JobApplyHandleLog::getList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 创建面试记录日志
     * @return \yii\console\Response|Response
     */
    public function actionCreate()
    {
        $request   = Yii::$app->request->post();
        $checkData = [
            'jobApplyId',
            'content'
        ];
        foreach ($checkData as $list) {
            if (strlen($request[$list]) < 1) {
                return $this->fail('参数' . $list . '不能为空');
            }
        }

        try {
            JobApplyHandleLog::create(FormatConverter::convertHump($request));

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}