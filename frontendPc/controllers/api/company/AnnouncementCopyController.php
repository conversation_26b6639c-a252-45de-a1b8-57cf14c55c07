<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseJobTemp;
use common\base\models\BaseUploadForm;
use common\helpers\FormatConverter;
use common\service\announcement\AddJobTempService;
use common\service\announcement\AddService;
use common\service\announcement\BaseService;
use common\service\announcement\OfflineJobService;
use common\service\announcement\OfflineService;
use common\service\announcement\OnlineJobService;
use common\service\announcement\OnlineOfflineJobService;
use common\service\announcement\OnlineOfflineService;
use common\service\announcement\OnlineService;
use common\service\announcement\RefreshService;
use common\service\company\PushEditMessageService;
use common\service\companyAuth\BaseButtonService;
use common\service\companyAuth\ButtonCallAuthService;
use frontendPc\models\Announcement;
use frontendPc\models\Job;
use frontendPc\models\Member;
use Yii;
use yii\base\Exception;

/**
 * 公告管理类
 */
class AnnouncementCopyController extends BaseFrontPcApiCompanyController
{
    /**
     * 选择职位模版填充编辑数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobEditData()
    {
        $id = Yii::$app->request->get('id');
        if (!$id) {
            return $this->fail('参数缺失');
        }
        $info = Job::getJobEditData($id);
        unset($info['delivery_limit_type']);

        return $this->success($info);
    }

    /**
     * 获取职位名称列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobNameList()
    {
        $params              = Yii::$app->request->get();
        $params['companyId'] = Member::getMainId();

        return $this->success(Job::getJobNameList($params));
    }

    /**
     * 公告发布按钮权限
     */
    public function actionReleaseButtonAuth()
    {
        try {
            //这里做一下前置权限
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_ANNOUNCEMENT_PUBLISH)
                ->run();
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告保存/提交
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionSubmitAdd()
    {
        $data = Yii::$app->request->post();

        try {
            $authModel = new ButtonCallAuthService();
            if ($data['announcementId']) {
                //这里做一下前置权限
                $res = $authModel->setButtonId(BaseButtonService::BUTTON_ANNOUNCEMENT_EDIT)
                    ->run();
            } else {
                //这里做一下前置权限
                $res = $authModel->setButtonId(BaseButtonService::BUTTON_ANNOUNCEMENT_PUBLISH)
                    ->run();
            }
            if (!$res['isAuth']) {
                //无权限
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $addService        = new AddService();
            $data['companyId'] = Member::getMainId(Yii::$app->user->id);
            if ($data['announcementId']) {
                // 编辑
                $addService->setOperator(Yii::$app->user->id, AddService::OPERATOR_TYPE_COMPANY)
                    ->setAudit()
                    ->setEditData($data)
                    ->run();
            } else {
                // 保存发布
                $addService->setOperator(Yii::$app->user->id, AddService::OPERATOR_TYPE_COMPANY)
                    ->setStaging($data['submitType'])
                    ->setAddData($data)
                    ->run();
            }

            $transaction->commit();
            if ($data['submitType'] == $addService::SAVE_TYPE_STAGING) {
                return $this->success(['id' => $addService->announcementId ?: 0], '保存成功');
            } else {
                return $this->success();
            }
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取公告状态数量
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetColumnAmount()
    {
        $list = Announcement::getColumnAmount();

        return $this->success($list);
    }

    /**
     * 获取公告列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        $params = Yii::$app->request->get();

        $params['memberId'] = Yii::$app->user->id;

        return $this->success(Announcement::getList(FormatConverter::convertHump($params)));
    }

    /**
     * 获取公告职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobList()
    {
        $params = Yii::$app->request->get();
        if (!$params['id'] || !$params['type']) {
            return $this->fail('参数缺失');
        }

        return $this->success(Job::getCompanyAnnouncementJobList($params, $params['type']));
    }

    /**
     * 公告详情预览
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDetail()
    {
        $params = Yii::$app->request->get();
        if (!$params['id'] || !$params['type']) {
            return $this->fail('参数缺失');
        }

        return $this->success(Announcement::getAnnouncementDetailInfo($params['id'], $params['type']));
    }

    /**
     * 公告操作-编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $id = Yii::$app->request->get('id');
        if (!$id) {
            return $this->fail('参数缺失');
        }

        try {
            return $this->success(Announcement::getEditInfo($id));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取公告剩余可发布数量
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSurplusReleaseAmount()
    {
        $memberId = Yii::$app->user->id;

        return $this->success(Announcement::getSurplusReleaseAmount($memberId));
    }

    /**
     * 临时职位-复制添加
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionTemporaryJobCopy()
    {
        try {
            //这里做一下权限限制
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
                ->run();
            if (!$res['isAuth']) {
                return $this->authError($res);
            }
            $params = Yii::$app->request->post();
            if (!$params['id']) {
                return $this->fail('参数缺失');
            }
            $addJobTempService = new AddJobTempService();
            $addJobTempService->setOperator(Yii::$app->user->id, AddService::OPERATOR_TYPE_COMPANY)
                ->setCopy($params)
                ->run();

            return $this->success(['jobTempData' => $addJobTempService->jobTempData]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 临时职位-编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionTemporaryJobEdit()
    {
        //这里做一下权限限制
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
                ->run();
            if (!$res['isAuth']) {
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
        $params = Yii::$app->request->get();
        if (!$params['id']) {
            return $this->fail('参数缺失');
        }

        try {
            if ($params['isTemp'] == BaseJobTemp::IS_TEMP_YES) {
                $data = BaseJobTemp::getTemporaryJobEdit($params);
            } elseif ($params['isTemp'] == BaseJobTemp::IS_TEMP_NO) {
                $data = Job::getEdit($params, true);
            } else {
                throw new Exception('非法请求');
            }
            $data['source'] = 2;
            //单位端特殊处理一下$data数据源
            if (empty($data['delivery_type'])) {
                //那就去那拿一下公告的回显
                $announcementInfo             = BaseAnnouncement::findOne($data['announcement_id']);
                $data['apply_type']           = $announcementInfo->apply_type;
                $data['apply_address']        = $announcementInfo->apply_address;
                $data['extra_notify_address'] = $announcementInfo->extra_notify_address;
                $data['delivery_type']        = $announcementInfo->delivery_type;
                $data['delivery_way']         = $announcementInfo->delivery_way;
                $data['source']               = 1;
            }

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 临时职位-添加/编辑保存
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionTemporaryJobSave()
    {
        try {
            //这里做一下权限限制
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
                ->run();
            if (!$res['isAuth']) {
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
        $data = Yii::$app->request->post();
        $data = FormatConverter::convertHump($data);
        //特殊处理一下单位端不需要传递着两个参数
        unset($data['delivery_type'], $data['delivery_way']);

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $addJobTempService = new AddJobTempService();

            $addJobTempService->setOperator(Yii::$app->user->id, AddService::OPERATOR_TYPE_COMPANY)
                ->setSingle()
                ->setData($data)
                ->run();

            $transaction->commit();

            $responseData = $addJobTempService->jobTempData;

            return $this->success(['jobTempData' => $responseData]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传临时Excel文件
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadExcel()
    {
        $model = new BaseUploadForm();
        $model->setUploadType('file');

        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $path = 'job_temp_excel';
            $data = $model->temporaryUploadExcel('file', $path);

            $transaction->commit();

            return $this->success([
                'url'     => $data['path'],
                'fullUrl' => \Yii::$app->params['homeUrl'] . '/' . $data['path'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 职位批量导入
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobTemporaryBatchImport()
    {
        //这里做一下权限限制
        try {
            $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_JOB_PUBLISH)
                ->run();
            if (!$res['isAuth']) {
                return $this->authError($res);
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }

        $request = Yii::$app->request->post();

        $filePath = $request['filePath'];
        if (!$filePath) {
            return $this->fail('参数缺失');
        }
        //加入companyId到参数
        $companyInfo          = BaseCompany::findOne(['member_id' => Yii::$app->user->id]);
        $request['companyId'] = $companyInfo->id;
        $transaction          = \Yii::$app->db->beginTransaction();
        try {
            $data = BaseJobTemp::jobTemporaryBatchImport($request,
                BaseCompany::getDeliveryTypeCate($companyInfo->delivery_type));
            $transaction->commit();

            return $this->success($data, '成功批量导入数据');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-再发布/下线
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionOnlineOffline()
    {
        $params = Yii::$app->request->post();
        if (!$params['id'] || !$params['actionType']) {
            return $this->fail('参数缺失');
        }
        //按钮权限-params['actionType']
        try {
            if ($params['actionType'] == 1) {
                $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_ANNOUNCEMENT_PUBLISHING)
                    ->run();
            } elseif ($params['actionType'] == 2) {
                $res = (new ButtonCallAuthService())->setButtonId(BaseButtonService::BUTTON_ANNOUNCEMENT_OFFLINE)
                    ->run();
            } else {
                return $this->fail('非法操作');
            }
            if (!$res['isAuth']) {
                return $this->authError($res);
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $onlineService  = new OnlineService();
            $offlineService = new OfflineService();
            if ($params['actionType'] == 1) {
                //这里处理一下  再发布时候如果是违规下线的公告,则需要则报错
                $announcementInfo = BaseAnnouncement::findOne($params['id']);
                if ($announcementInfo->offline_type == BaseAnnouncement::OFFLINE_TYPE_VIOLATION) {
                    throw new Exception('当前内容违规，请联系运营人员');
                }
                $beforeStatusText = BaseArticle::STATUS_LIST[BaseArticle::findOne($announcementInfo->article_id)->status];
                // 操作再发布
                $onlineService->setOperator(Yii::$app->user->id, BaseService::OPERATOR_TYPE_COMPANY)
                    ->setOnline()
                    ->setData($params)
                    ->run();
                $afterStatusText = BaseArticle::STATUS_LIST[BaseArticle::findOne($announcementInfo->article_id)->status];
                (new PushEditMessageService())->republish([
                    'announcementId'   => $params['id'],
                    'beforeStatusText' => $beforeStatusText,
                    'afterStatusText'  => $afterStatusText,
                ]);
            } elseif ($params['actionType'] == 2) {
                // 操作下线
                $offlineService->setOperator(Yii::$app->user->id, BaseService::OPERATOR_TYPE_COMPANY)
                    ->setOffline()
                    ->setData($params)
                    ->run();
                (new PushEditMessageService())->offline(['announcementId' => $params['id']]);
            } else {
                throw new Exception('非法操作');
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告职位操作-再发布/下线
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionOnlineOfflineJob()
    {
        $params = Yii::$app->request->post();
        if (!$params['id'] || !$params['actionType']) {
            return $this->fail();
        }

        $transaction = Yii::$app->db->beginTransaction();

        try {
            $onlineJobService  = new OnlineJobService();
            $offlineJobService = new OfflineJobService();
            if ($params['actionType'] == 1) {
                // 操作再发布
                $onlineJobService->setOperator(Yii::$app->user->id, BaseService::OPERATOR_TYPE_COMPANY)
                    ->setOnline()
                    ->setData($params)
                    ->run();
            } elseif ($params['actionType'] == 2) {
                // 操作下线
                $offlineJobService->setOperator(Yii::$app->user->id, BaseService::OPERATOR_TYPE_COMPANY)
                    ->setOffline()
                    ->setData($params)
                    ->run();
            } else {
                throw new Exception('非法操作');
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-刷新
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRefresh()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail('参数缺失');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $refreshService = new RefreshService();
            $refreshService->setOperator(Yii::$app->user->id, BaseService::OPERATOR_TYPE_COMPANY)
                ->setRefresh()
                ->setData($params)
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告修改单位主页排序值
     * @return void
     */
    public function actionChangeHomeSort()
    {
        $id       = Yii::$app->request->post('id');
        $memberId = Yii::$app->user->id;

        $announcement = BaseAnnouncement::findOne($id);
        if (!$announcement || $announcement['member_id'] != $memberId) {
            return $this->fail('公告不存在');
        }

        try {
            $announcement->home_sort = Yii::$app->request->post('homeSort');
            if (!$announcement->save()) {
                throw new Exception($announcement->getFirstErrorsMessage());
            }

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取公告名称列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAnnouncementNameList()
    {
        return $this->success(Announcement::getListByName(Yii::$app->request->get('title'), Member::getMainId()));
    }

    //    /**
    //     * 删除职位附件文件
    //     * @return \yii\console\Response|\yii\web\Response
    //     */
    //    public function actionDeleteAppendix()
    //    {
    //        $id = Yii::$app->request->post('id');
    //        try {
    //            return $this->success(BaseFile::deleteAppendix($id), '删除成功');
    //        } catch (\Exception $e) {
    //            return $this->result($e->getMessage());
    //        }
    //    }

}