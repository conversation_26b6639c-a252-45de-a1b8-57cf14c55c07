<?php

namespace frontendPc\controllers\api\company;

use common\helpers\FormatConverter;
use frontendPc\models\WelfareLabel;
use yii\console\Response;
use Yii;

class WelfareLabelController extends BaseFrontPcApiCompanyController
{
    /**
     * 创建企业福利标签
     * @return Response|\yii\web\Response
     */
    public function actionCreate()
    {
        $request = Yii::$app->request->post();
        try {
            $companyWelfareLabelId = WelfareLabel::create(FormatConverter::convertHump($request));

            return $this->success(['id' => $companyWelfareLabelId]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除企业添加的福利标签
     * @return Response|\yii\web\Response
     */
    public function actionDel()
    {
        $request = Yii::$app->request->post();
        try {
            WelfareLabel::del(FormatConverter::convertHump($request));

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}