<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseJob;
use common\base\models\BaseResume;
use common\libs\ResumeHandle;
use common\service\match\CommonMatchService;
use common\service\match\JobToPersonNewService;
use Yii;
use common\service\CommonService;
use common\service\match\JobToPersonService;

class ResumeRecommendController extends BaseFrontPcApiCompanyController
{
    /**
     * 推荐人才列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        try {
            $memberId            = Yii::$app->user->id;
            $company_member_info = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
            //单位信息
            $company_info = BaseCompany::findOne($company_member_info->company_id);
            $job_id       = Yii::$app->request->get('jobId');
            if ($job_id <= 0) {
                //做一个假数据给前端
                $job_id = BaseJob::getLastInfo()['id'];
            } else {
                $job_info = BaseJob::findOne($job_id);
                if ($job_info->company_id != $company_info->id) {
                    throw new \Exception('没有权限获取数据');
                }
            }
            //主账号就判断是不是高级会员，子账号就判断是不是VIP权限
            if ($company_member_info->company_member_type == BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN) {
                $rule_bool = $company_info->package_type == BaseCompany::PACKAGE_TYPE_SENIOR;
            } else {
                $rule_bool = $company_member_info->member_rule == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP;
            }
            $page = Yii::$app->request->get('page');
            $page = $page ?: 1;
            if (!$rule_bool && $page > 3) {
                $page = 3;
            }
            $pageSize = Yii::$app->request->get('pageSize');
            $service  = new JobToPersonService();
            $params   = [
                'jobId'                    => $job_id,
                'type'                     => Yii::$app->request->get('type'),
                'page'                     => $page,
                'pageSize'                 => $pageSize,
                'checkedInSevenDays'       => Yii::$app->request->get('checkedInSevenDays'),
                'invitedInSevenDays'       => Yii::$app->request->get('invitedInSevenDays'),
                'hideViewLess65Percentage' => Yii::$app->request->get('hideViewLess65Percentage'),
                'accepted'                 => Yii::$app->request->get('accepted'),
                'memberId'                 => Yii::$app->user->id,
            ];
            $data     = $service->setPlatform(CommonService::PLATFORM_WEB_COMPANY)
                ->init($params)
                ->run();
            //            $model = new JobToPersonNewService();
            //            $data  = $model->getList([
            //                'jobId'              => $job_id,
            //                'type'               => Yii::$app->request->get('type'),
            //                'platform'           => CommonService::PLATFORM_WEB_COMPANY,
            //                'page'               => $page,
            //                'pageSize'           => $pageSize,
            //                'checkedInSevenDays' => Yii::$app->request->get('checkedInSevenDays'),
            //                'invitedInSevenDays' => Yii::$app->request->get('invitedInSevenDays'),
            //                'accepted'           => Yii::$app->request->get('accepted'),
            //            ]);
            if (!$rule_bool && $data['total'] > 3 * $pageSize) {
                $data['total'] = 3 * $pageSize;
            }

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}