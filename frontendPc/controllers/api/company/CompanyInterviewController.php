<?php

namespace frontendPc\controllers\api\company;

use common\helpers\FormatConverter;
use frontendPc\controllers\api\BaseFrontPcApiController;
use frontendPc\models\CompanyInterview;
use Yii;
use yii\web\Response;

class CompanyInterviewController extends BaseFrontPcApiController
{
    /**
     * 创建面试邀请信息
     * @return \yii\console\Response|Response
     */
    public function actionCreate()
    {
        $request        = Yii::$app->request->post();
        $request['ids'] = $request['jobApplyId'];
        $memberId       = Yii::$app->user->id;
        $transaction    = Yii::$app->db->beginTransaction();

        try {
            CompanyInterview::createCompanyInterview(FormatConverter::convertHump($request), $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取面试邀请信息
     * @return \yii\console\Response|Response
     */
    public function actionGetInfo()
    {
        $request = Yii::$app->request->get();

        try {
            $info = CompanyInterview::getCompanyInterviewInfo(FormatConverter::convertHump($request));

            return $this->success($info);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}