<?php

namespace frontendPc\controllers\api\company;

use admin\models\Member;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseCompanyViewResume;
use common\base\models\BaseResume;
use common\base\models\BaseResumeLibrary;
use common\base\models\BaseSystemConfig;
use common\libs\Cache;
use common\service\commonResume\CommonResumeApplication;
use common\service\companyAuth\BaseButtonService;
use common\service\companyAuth\TipsService;
use Yii;

class ResumeLibraryController extends BaseFrontPcApiCompanyController
{

    /**
     * 获取列表数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        // 内存开到1g
        ini_set('memory_limit', '1024M');
        
        $searchData = Yii::$app->request->get();
        //$companyId               = $this->getMemberCompanyId();
        $memberId            = Yii::$app->user->id;
        $company_member_info = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
        //单位信息
        $company_info            = BaseCompany::findOne($company_member_info->company_id);
        $searchData['companyId'] = $company_info->id;
        $searchData['memberId']  = $memberId;
        // 首先必填参数
        if (!$memberId || !$company_info || !$company_member_info) {
            return $this->fail('参数错误');
        }
        //主账号就判断是不是高级会员，子账号就判断是不是VIP权限
        if ($company_member_info->company_member_type == BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN) {
            $rule_bool = $company_info->package_type == BaseCompany::PACKAGE_TYPE_SENIOR;
        } else {
            $rule_bool = $company_member_info->member_rule == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP;
        }
        $searchData['rule_bool'] = $rule_bool;
        try {
            $service = CommonResumeApplication::getInstance();
            $list    = $service->getList($searchData);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 邀请投递
     * @throws \yii\base\Exception
     */
    public function actionInvite()
    {
        $companyId   = $this->getCompanyId();
        $resumeId    = Yii::$app->request->post('resumeId');
        $jobId       = Yii::$app->request->post('jobId');
        $isSendEmail = Yii::$app->request->post('isSendEmail');
        $isSendSms   = Yii::$app->request->post('isSendSms');
        $remark      = Yii::$app->request->post('remark');
        // 首先必填参数
        if (!$companyId || !$resumeId || !$jobId) {
            return $this->fail('参数错误');
        }

        // 账号信息
        $companyMemberInfo = BaseCompanyMemberInfo::getCompanyMemberInfoByAccount(Yii::$app->user->id);
        if ($companyMemberInfo['companyRole'] == BaseCompanyPackageConfig::COMPANY_ROLE_SENIOR && $companyMemberInfo['memberRule'] == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_NORMAL) {
            return [
                'memberRule'      => $companyMemberInfo['memberRule'],
                'companyRole'     => $companyMemberInfo['companyRole'],
                'companyRoleName' => $companyMemberInfo['companyRoleName'],
                'message'         => "<p>当前账号暂无人才库相关权限！请向</p><p>单位管理员申请授权，或联系专属客</p><p>服升级账号权益，享受超值人才服</p><p>务！</p><p>咨询热线：020-********</p>",
                'qq'              => '**********',
            ];
        }

        if ($companyMemberInfo['companyRole'] != BaseCompanyPackageConfig::COMPANY_ROLE_SENIOR) {
            return [
                'memberRule'      => $companyMemberInfo['memberRule'],
                'companyRole'     => $companyMemberInfo['companyRole'],
                'companyRoleName' => $companyMemberInfo['companyRoleName'],
                'message'         => "<p>您当前是" . $companyMemberInfo['companyRoleName'] . "，暂不支持此操作。</p><p>升级高级会员，可享受超值人才服务!</p><p>咨询热线：020-********</p>",
                'qq'              => '**********',
            ];
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = CommonResumeApplication::getInstance();

            $res = $service->invite($companyId, $resumeId, $jobId, $remark, $isSendEmail, $isSendSms);
            $transaction->commit();

            return $this->success(['tips' => '已成功邀约' . $res['successInviteAmount'] . '位候选人！该邀约信息已同步至【找人才-邀约记录】中，请前往查看']);
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 批量邀请投递
     */
    public function actionInviteBatch()
    {
        $companyId   = $this->getCompanyId();
        $resumeId    = Yii::$app->request->post('resumeIds');
        $jobId       = Yii::$app->request->post('jobId');
        $isSendEmail = Yii::$app->request->post('isSendEmail');
        $remark      = Yii::$app->request->post('remark');

        // 首先必填参数
        if (!$companyId || !$resumeId || !$jobId) {
            return $this->fail('参数错误');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = CommonResumeApplication::getInstance();

            $res = $service->inviteBatch($companyId, $resumeId, $jobId, $remark, $isSendEmail);
            $transaction->commit();

            return $this->success(['tips' => '已成功邀约' . $res['successInviteAmount'] . '位候选人！该邀约信息已同步至【找人才-邀约记录】中，请前往查看']);
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 单个下载时检查
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionBeforeDownloadCheck()
    {
        $companyId = $this->getCompanyId();
        $resumeId  = Yii::$app->request->get('resumeId');
        if (!$companyId || !$resumeId) {
            return $this->fail('参数错误');
        }
        try {
            $service = CommonResumeApplication::getInstance();

            $data = $service->beforeDownload($companyId, $resumeId);

            return $this->success($data);
        } catch (\Exception $e) {
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 批量下载时检查
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionBeforeBatchDownloadCheck()
    {
        $companyId = $this->getCompanyId();
        $resumeIds = Yii::$app->request->get('resumeIds');
        if (!$companyId || !$resumeIds) {
            return $this->fail('参数错误');
        }
        try {
            $service     = CommonResumeApplication::getInstance();
            $resumeIdArr = explode(',', $resumeIds);
            $data        = $service->beforeBatchDownload($companyId, $resumeIdArr);

            return $this->success($data);
        } catch (\Exception $e) {
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 下载简历并且到人才库
     */
    public function actionDownload()
    {
        $companyId = $this->getCompanyId();
        $resumeId  = Yii::$app->request->post('resumeId');

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = CommonResumeApplication::getInstance();

            $id = $service->download($companyId, $resumeId);
            $transaction->commit();

            return $this->success(['id' => $id]);
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 批量下载
     */
    public function actionBatchDownload()
    {
        $companyId = $this->getCompanyId();
        $resumeIds = Yii::$app->request->post('resumeIds');

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = CommonResumeApplication::getInstance();

            $ids = $service->batchDownLoad($companyId, $resumeIds);
            $transaction->commit();

            return $this->success(['ids' => $ids]);
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 收藏
     */
    public function actionCollect()
    {
        $companyId = $this->getCompanyId();
        $resumeId  = Yii::$app->request->post('resumeId');

        if (!$resumeId) {
            return $this->fail('参数错误');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = CommonResumeApplication::getInstance();

            $service->collect($companyId, $resumeId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 批量收藏
     */
    public function actionBatchCollect()
    {
        $companyId = $this->getCompanyId();
        $resumeIds = Yii::$app->request->post('resumeIds');

        if (!$resumeIds) {
            return $this->fail('参数错误');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = CommonResumeApplication::getInstance();

            $service->batchCollect($companyId, $resumeIds);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    public function actionLoadList()
    {
    }

    public function actionShare()
    {
        $companyId = $this->getCompanyId();
        $resumeId  = Yii::$app->request->post('resumeId');
        $password  = Yii::$app->request->post('password');

        if (!$companyId || !$resumeId || !$password) {
            return $this->fail('参数错误');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = CommonResumeApplication::getInstance();

            $data = $service->companyShare($companyId, $resumeId, $password);
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 获取分享所需信息
     */
    public function actionGetShareInfo()
    {
        $companyId = $this->getCompanyId();
        $resumeId  = Yii::$app->request->get('resumeId');

        if (!$companyId || !$resumeId) {
            return $this->fail('参数错误');
        }

        try {
            $service = CommonResumeApplication::getInstance();

            $rs = $service->companyGetShareInfo($companyId, $resumeId);

            return $this->success($rs);
        } catch (\Exception $e) {
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 邀请列表
     */
    public function actionGetInviteList()
    {
        $companyId           = $this->getMemberCompanyId();
        $params              = Yii::$app->request->get();
        $params['companyId'] = $companyId;

        $service = CommonResumeApplication::getInstance();

        return $this->success($service->getInviteList($params));
    }

    /**
     * 可邀请职位检测
     */
    public function actionGetInviteJobCheck()
    {
        $companyId = $this->getCompanyId();
        $resumeId  = Yii::$app->request->get('resumeId');
        $jobId     = Yii::$app->request->get('jobId', 0);
        if (!$companyId || !$resumeId || !$jobId) {
            return $this->fail('参数错误');
        }
        if (count(explode(',', $resumeId)) <= 1) {
            return $this->success([
                'notice'     => '',
                'noticeTips' => '',
            ]);
        }

        try {
            $service = CommonResumeApplication::getInstance();

            $rs = $service->getInviteJobCheck($companyId, $resumeId, $jobId);

            return $this->success($rs);
        } catch (\Exception $e) {
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 可邀请职位列表
     */
    public function actionGetInviteJobList()
    {
        $companyId = $this->getCompanyId();
        $resumeId  = Yii::$app->request->get('resumeId');
        $jobId     = Yii::$app->request->get('jobId', 0);
        if (!$companyId || !$resumeId) {
            return $this->fail('参数错误');
        }

        try {
            $service = CommonResumeApplication::getInstance();

            $rs = $service->getInviteJobList($companyId, $resumeId, $jobId);

            return $this->success($rs);
        } catch (\Exception $e) {
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 已邀请职位列表
     */
    public function actionGetJobInviteList()
    {
        $companyId           = $this->getCompanyId();
        $params              = Yii::$app->request->get();
        $params['companyId'] = $companyId;

        $service = CommonResumeApplication::getInstance();

        return $this->success($service->getJobInviteList($params));
    }

    /**
     * 已邀请公告列表
     */
    public function actionGetAnnouncementInviteList()
    {
        $companyId           = $this->getCompanyId();
        $params              = Yii::$app->request->get();
        $params['companyId'] = $companyId;

        $service = CommonResumeApplication::getInstance();

        return $this->success($service->getAnnouncementInviteList($params));
    }


    //
    //    public function actionCheckResumeStatus()
    //    {
    //        $companyId = $this->getCompanyId();
    //        $resumeId  = Yii::$app->request->get('resumeId');
    //
    //        if (empty($params['resumeId'])) {
    //            return $this->fail('参数错误');
    //        }
    //        //判断简历是否存在
    //        $resumeInfo = BaseResume::findOne(['id' => $params['resumeId']]);
    //        if (empty($resumeInfo)) {
    //            return $this->fail('简历不存在');
    //        }
    //
    //        //判断简历是否下载到简历库中了
    //        $companyResumeLibrary = BaseCompanyResumeLibrary::findOneVal([
    //            'resume_id'  => $params['resumeId'],
    //            'company_id' => $companyId,
    //        ], 'id');
    //        if (!empty($companyResumeLibrary)) {
    //            return $this->success(['inCompanyResumeLibrary'=>1]);
    //        }else{
    //            return $this->success(['inCompanyResumeLibrary'=>2]);
    //        }
    //    }

    /**
     * 获取人才详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetPersonDetailInfo()
    {
        $params = Yii::$app->request->get();
        if (empty($params['resumeId'])) {
            return $this->fail('参数错误');
        }
        //memberId
        $memberId            = Yii::$app->user->id;
        $company_member_info = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
        //单位信息
        $company_info = BaseCompany::findOne($company_member_info->company_id);
        //主账号就判断是不是高级会员，子账号就判断是不是VIP权限
        if ($company_member_info->company_member_type == BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN) {
            $rule_bool = $company_info->package_type == BaseCompany::PACKAGE_TYPE_SENIOR;
        } else {
            $rule_bool = $company_member_info->member_rule == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP;
        }
        if (!$rule_bool) {
            //普通权限每天限制10次
            $cache_key = Cache::PC_COMPANY_RESUME_LIBRARY_LIMIT_KEY . ':' . $memberId;
            $get_total = Cache::get($cache_key);
            if ($get_total >= 10) {
                //                $data = [
                //                    'content'    => '<p>对不起，今日免费查看简历次数已用完。</p><p>升级高级会员，享受超值人才服务！</p><p>咨询热线：020-********</p>',
                //                    'connectUrl' => 'http://www.gaoxiaojob.com/zhaopin/aboutUs/productService.html',
                //                    'qq'         => '**********',
                //                    'title'      => '升级享权益',
                //                    'toast_type' => 1,
                //                ];
                //
                //                return $this->success($data);
                return $this->authError([
                    'isAuth'  => false,
                    //是否弹窗
                    'isPopup' => true,
                    //弹窗的数据
                    'popup'   => [
                        'type' => BaseButtonService::TIPS_TYPE_EQUITY_COMPARE,
                        'data' => (new TipsService())->run(BaseButtonService::TIPS_TYPE_EQUITY_COMPARE),
                    ],
                    //是否需要提示
                    'isTips'  => true,
                    //文案
                    'tips'    => (new TipsService())->run(BaseButtonService::TIPS_TYPE_TOAST_RESUME_VIEW_LIMIT),
                ]);
            } else {
                //当前时间到今天结束时间的秒数
                $now_time     = time();
                $day_end_time = strtotime(date('Y-m-d 23:59:59'));
                $time         = $day_end_time - $now_time;
                $get_total    = $get_total > 0 ? $get_total + 1 : 1;
                Cache::setex($cache_key, $time, $get_total);
            }
        }
        $params['companyId'] = $company_info->id;
        //判断简历是否存在
        $resumeInfo = BaseResume::findOne(['id' => $params['resumeId']]);
        if (empty($resumeInfo)) {
            return $this->fail('简历不存在');
        }
        //判断简历是否下载到简历库中了
        $companyResumeLibrary = BaseCompanyResumeLibrary::findOneVal([
            'resume_id'  => $params['resumeId'],
            'company_id' => $company_info->id,
        ], 'id');
        if (!empty($companyResumeLibrary)) {
            return $this->success(['isCompanyResumeLibrary' => 1]);
        }

        $service = CommonResumeApplication::getInstance();

        $data = $service->getDetail($params);
        //能拿到数据了，肯定是正常，不是单位简历库
        $data['isCompanyResumeLibrary'] = 2;
        // 之前的消息中心并入这里
        BaseCompanyViewResume::create($company_info->id, $params['resumeId']);
        //做一个PV统计
        BaseCompanyResumePvTotal::updateDailyTotalPv($params['resumeId']);

        return $this->success($data);
    }

    /**
     * 获取操作日志
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetHandleLog()
    {
        $params              = Yii::$app->request->get();
        $params['companyId'] = $this->getCompanyId();
        if (empty($params['resumeId']) || empty($params['companyId'])) {
            return $this->fail('参数错误');
        }
        //判断简历是否存在
        $resumeInfo = BaseResume::findOne(['id' => $params['resumeId']]);
        if (empty($resumeInfo)) {
            return $this->fail('简历不存在');
        }

        $service = CommonResumeApplication::getInstance();

        return $this->success($service->getHandleLog($params));
    }

    /**
     * 获取收藏列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCollectList()
    {
        $searchData              = Yii::$app->request->get();
        $companyId               = $this->getMemberCompanyId();
        $searchData['companyId'] = $companyId;
        // 首先必填参数
        if (!$companyId) {
            return $this->fail('参数错误');
        }

        try {
            $service = CommonResumeApplication::getInstance();
            $list    = $service->getCollectList($searchData);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 返回简历下载说明
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetDownloadExplain()
    {
        $data = BaseResumeLibrary::getResumeLevelInfoList();

        return $this->success($data);
    }

    /**
     * 返回免费、过期、试用会员提示
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetFreeTipsInfo()
    {
        $companyId = $this->getCompanyId();

        // 首先必填参数
        if (!$companyId) {
            return $this->fail('参数错误');
        }

        try {
            $service = CommonResumeApplication::getInstance();
            $data    = $service->getFreeTipsInfo($companyId);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}