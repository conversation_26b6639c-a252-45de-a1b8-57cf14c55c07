<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseFile;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseOffSiteJobApply;
use common\helpers\FormatConverter;
use common\libs\ResumeHandle;
use common\service\job\JobApplyHandleService;
use common\service\job\BaseService;
use common\service\jobApply\CompanyJobApplyInfoService;
use common\service\resumeBatchDownload\ResumeBatchDownloadApplication;
use frontendPc\models\Company;
use frontendPc\models\JobApply;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class JobApplyController extends BaseFrontPcApiCompanyController
{
    /**
     * 获取根据关键词搜索简历信息列表
     * @return Response|\yii\web\Response
     */
    public function actionSearchList()
    {
        $request  = Yii::$app->request->get();
        $memberId = Yii::$app->user->id;
        try {
            $list = JobApply::getSearchJobApplyList(FormatConverter::convertHump($request), $memberId);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新标签
     * @return Response|\yii\web\Response
     */
    public function actionSaveCompanyTag()
    {
        $request = Yii::$app->request->post();

        try {
            JobApply::saveCompanyTag(FormatConverter::convertHump($request));

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取所有状态下简历数量
     * @return Response|\yii\web\Response
     */
    public function actionGetStatusAmount()
    {
        try {
            $list = JobApply::getStatusAmount(Yii::$app->user->id);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 通过初筛
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionThroughFirst()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_THROUGH_FIRST, $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 简历不合适
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionInappropriate()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_INAPPROPRIATE, $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 撤销简历不合适
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionRevocationInappropriate()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_INAPPROPRIATE_REVOCATION,
        //         $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 未接通
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionBlockCall()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_BLOCK_CALL, $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 无意向
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionNoIntention()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_INTENTION_NO, $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 已录用
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionEmployed()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_EMPLOYED, $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 待录用
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionWaitEmployed()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_EMPLOYED_WAIT, $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 撤销录用
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionRevocationEmployed()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_EMPLOYED_REVOCATION,
        //         $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 未到面
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionNoInterview()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_INTERVIEW_NO, $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 已入职
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionEntry()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_ENTRY, $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 撤销已入职
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionRevocationEntry()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_HANDLE_WAIT, $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 未入职
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionNoEntry()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_ENTRY_NO, $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    /**
     * 未入职
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionRevocationNoEntry()
    {
        return $this->fail('接口已废弃');
        // $request = Yii::$app->request->post();
        // if (!$request['id']) {
        //     throw new Exception('标识Id不能为空');
        // }
        // $memberId = Yii::$app->user->id;
        // try {
        //     JobApply::saveStatus(FormatConverter::convertHump($request), BaseJobApply::STATUS_ENTRY_NO_REVOCATION,
        //         $memberId);
        //
        //     return $this->success();
        // } catch (\Exception $e) {
        //     return $this->fail($e->getMessage());
        // }
    }

    //////////////////////////////新接口

    /**
     * 按公告查看--简历列表
     * @return Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetResumeByAnnouncement()
    {
        $request = Yii::$app->request->get();
        try {
            $list = JobApply::getResumeByAnnouncement(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 按职位查看查看--简历列表
     * @return Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetResumeByJob()
    {
        $request = Yii::$app->request->get();
        try {
            $list = JobApply::getResumeByJob(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 全部简历（条件查询）
     * @return Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetResumeList()
    {
        $request = Yii::$app->request->get();
        try {
            //            //模拟分页请求
            //            $request['offApplyId'] = 0;
            //            $request['applyId']    = 0;
            //            $page                  = $request['page'] ?: 1;
            //            $list                  = [];
            //            for ($i = 1; $i <= $page; $i++) {
            //                $list                  = JobApply::getResumeListNew(FormatConverter::convertHump($request));
            //                $request['offApplyId'] = $list['page']['off_apply_id'];
            //                $request['applyId']    = $list['page']['apply_id'];
            //            }
            // $list = JobApply::getResumeList(FormatConverter::convertHump($request),Yii::$app->user->id);
            $list = (new CompanyJobApplyInfoService())->getList($request,Yii::$app->user->id);
            //
            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 简历管理操作
     * @return Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionHandle()
    {
        $request      = Yii::$app->request->post();
        $memberId     = Yii::$app->user->id;
        $auditService = new JobApplyHandleService();
        $params       = [
            'id'                  => $request['id'],
            'handle_type'         => $request['handleType'],
            'status'              => $request['status'],
            'company_mark_status' => $request['companyMarkStatus'],
        ];

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $auditService->setOperator($memberId, BaseService::OPERATOR_HANDLE_TYPE_COMPANY)
                ->setData($params)
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 简历管理操作（批量）
     * @return Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionHandleBatch()
    {
        $request      = Yii::$app->request->post();
        $memberId     = Yii::$app->user->id;
        $auditService = new JobApplyHandleService();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $ids = explode(',', $request['ids']);
            foreach ($ids as $id) {
                $params = [
                    'id'                  => $id,
                    'handle_type'         => $request['handleType'],
                    'status'              => $request['status'],
                    'company_mark_status' => $request['companyMarkStatus'],
                ];
                $auditService->setOperator($memberId, BaseService::OPERATOR_HANDLE_TYPE_COMPANY)
                    ->setData($params)
                    ->run();
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 简历下载
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDownload()
    {
        $memberId        = Yii::$app->user->id;
        $resumeId        = Yii::$app->request->get('resumeId');
        $attachmentToken = Yii::$app->request->get('attachmentToken');
        $jobApplyId      = Yii::$app->request->get('jobApplyId');
        $tag             = Yii::$app->request->get('tag');

        if (!$resumeId || $memberId < 0) {
            return $this->fail();
        }
        if ($tag == BaseJob::DELIVERY_TYPE_OUTSIDE) {
            $jobApplyInfo = BaseOffSiteJobApply::findOne(['id' => $jobApplyId]);
            if (!$jobApplyInfo) {
                return $this->fail();
            }
        } else {
            $jobApplyInfo = BaseJobApply::findOne(['id' => $jobApplyId]);
            $companyId    = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId],'company_id');
            if ($jobApplyInfo->company_id != $companyId) {
                return $this->fail();
            }
            $tag = BaseJob::DELIVERY_TYPE_INSIDE;
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $resumeDown = new ResumeHandle();
            $resumeDown->setOperator($memberId, ResumeHandle::OPERATOR_TYPE_MEMBER)
                ->setData($resumeId, $attachmentToken, $jobApplyId, $tag)
                ->download();

            $transaction->commit();
            exit();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionDownloadResumeFile()
    {
        $memberId = Yii::$app->user->id;

        $fileId     = Yii::$app->request->get('id');
        $jobApplyId = Yii::$app->request->get('jobApplyId');

        $jobApplyInfo = BaseJobApply::findOne(['id' => $jobApplyId]);
        if ($jobApplyInfo->company_member_id != $memberId) {
            return $this->fail();
        }

        // 检查一下这份附件是否属于这次投递的
        $stuffFileIds = $jobApplyInfo->stuff_file_id;
        if (!in_array($fileId, explode(',', $stuffFileIds))) {
            return $this->fail();
        }

        try {
            return BaseFile::getResources($fileId);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取待处理状态数量
     * @return Response|\yii\web\Response
     */
    public function actionGetWaitStatusAmount()
    {
        $memberId = Yii::$app->user->id;
        try {
            $amount = (int)JobApply::getWaitStatusAmount($memberId);

            return $this->success(['amount' => $amount]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 其他应聘列表）
     * @return Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetResumeListRest()
    {
        $request = Yii::$app->request->get();
        try {
            $list = JobApply::getResumeListRest(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionCreateBatchDownloadUrl()
    {
        $jobApplyId = Yii::$app->request->post('jobApplyIds');
        $companyId  = $this->getCompanyId();

        if (!$jobApplyId) {
            return $this->fail();
        }

        try {
            // 先检查?(这里只检查数量)
            $app = ResumeBatchDownloadApplication::getInstance();
            $url = $app->createCompanyByApply($jobApplyId, $companyId);

            return $this->success(['url' => $url]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionDownloadByToken()
    {
        $token     = Yii::$app->request->get('token');
        $companyId = $this->getCompanyId();

        if (!$token) {
            return $this->fail();
        }

        try {
            // 先检查?(这里只检查数量)
            $app = ResumeBatchDownloadApplication::getInstance();

            return $app->downloadByToken($token, $companyId);
        } catch (\Exception $e) {
        }
    }

}