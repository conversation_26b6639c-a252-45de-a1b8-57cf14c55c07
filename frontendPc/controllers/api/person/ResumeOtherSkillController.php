<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeOtherSkill;
use frontendPc\models\ResumeSkill;

class ResumeOtherSkillController extends BaseFrontPcApiPersonController
{
    public function actionSave()
    {
        $data = \Yii::$app->request->post();
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            ResumeOtherSkill::saveInfo($data);
            $transaction->commit();
            $memberId = \Yii::$app->user->id;

            return $this->success(ResumeOtherSkill::getInfoList($memberId));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeOtherSkill::delSkill($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}