<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeAcademicPatent;

class ResumeAcademicPatentController extends BaseFrontPcApiPersonController
{
    /**
     * 保存/编辑信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data = \Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $data['memberId'] = \Yii::$app->user->id;
            ResumeAcademicPatent::saveInfo($data);
            $transaction->commit();

            return $this->success(ResumeAcademicPatent::getPatentList($data['memberId']));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除专利
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeAcademicPatent::delPatent($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}