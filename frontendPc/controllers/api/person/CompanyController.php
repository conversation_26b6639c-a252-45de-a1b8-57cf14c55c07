<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseMember;
use common\service\search\CommonSearchApplication;
use common\service\specialNeedService\CompanyInformationService;
use frontendPc\models\Announcement;
use frontendPc\models\Area;
use frontendPc\models\Company;
use frontendPc\models\CompanyCollect;
use frontendPc\models\Job;
use frontendPc\models\JobCollect;
use yii\base\Exception;
use Yii;

class CompanyController extends BaseFrontPcApiPersonController
{
    public function actionGetList()
    {
        $searchData = Yii::$app->request->get();
        try {
            return $this->success(Company::search($searchData));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 收藏单位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCollect()
    {
        $ids = Yii::$app->request->post('companyId');
        try {
            $companyArr = explode(',', $ids);
            foreach ($companyArr as $k => $id) {
                //判断单位是否存在
                $companyInfo = Company::find()
                    ->where(['id' => $id])
                    ->select(['status'])
                    ->asArray()
                    ->one();
                if (!$companyInfo) {
                    return $this->fail('单位不存在');
                }
                if ($companyInfo['status'] != Company::STATUS_ACTIVE) {
                    return $this->fail('单位状态错误');
                }
            }
            CompanyCollect::saveInfo($companyArr, Yii::$app->user->id);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取收藏单位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCollectList()
    {
        try {
            $searchData             = Yii::$app->request->get();
            $searchData['memberId'] = Yii::$app->user->id;

            return $this->success(CompanyCollect::getList($searchData, CompanyCollect::NEED_PAGE_INFO_YES));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取收藏的单位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetRecommendList()
    {
        try {
            return $this->success(Company::getRecommendList());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位下的公告列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetAnnouncementList()
    {
        try {
            $searchData = Yii::$app->request->get();

            return $this->success(Announcement::getCompanyDetailList($searchData, true));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位下的职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetJobList()
    {
        try {
            $searchData = Yii::$app->request->get();

            return $this->success(BaseJob::getCompanyJobList($searchData));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位主页-引才活动第二+页数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetActivityList()
    {
        try {
            $searchData = Yii::$app->request->get();
            //判断companyId
            if (empty($searchData['companyId'])) {
                throw new Exception('单位id不能为空');
            }

            $data = Company::getActivityList($searchData);
            $list = $this->renderPartial('/company/detailModule/activityList.html', [
                'list' => $data['list'],
            ]);

            return $this->success([
                'page'  => (int)$data['page']['page'] ?: 1,
                'size'  => (int)$data['page']['limit'] ?: 20,
                'total' => (int)$data['page']['count'] ?: 0,
                'list'  => $list,
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionGetJobListHtml()
    {
        try {
            $searchData = Yii::$app->request->get();
            $memberId   = Yii::$app->user->id;
            //判断companyId
            if (empty($searchData['companyId'])) {
                throw new Exception('单位id不能为空');
            }
            //判断是否合作单位
            $isCooperation = BaseCompany::checkIsCooperation($searchData['companyId']);

            //            $jobList = BaseJob::getCompanyJobList($searchData);
            //获取在招职位
            $commonSearchApp = new CommonSearchApplication();

            $jobList = $commonSearchApp->companyJobListSearch($searchData);
            //获取简历信息
            $resumeId = BaseMember::getMainId($memberId);
            foreach ($jobList['list'] as &$item) {
                if ($item['is_cooperation'] == BaseCompany::COOPERATIVE_UNIT_NO) {
                    $item['userEmail'] = BaseMember::findOneVal(['id' => $memberId], 'email');
                }
                $item['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resumeId, $item['jobId']);
            }

            $jobList = (new CompanyInformationService())->handelCompanyJobList($jobList, $searchData['companyId']);

            if ($isCooperation) {
                $temp = 'jobList.html';
            } else {
                $temp = 'unCooperationJobList.html';
            }
            $list = $this->renderPartial('/company/detailModule/' . $temp, [
                'list' => $jobList['list'],
            ]);

            return $this->success([
                'page'  => (int)$searchData['page'] ?: 1,
                'size'  => (int)$searchData['pageSize'] ?: 20,
                'total' => (int)$jobList['totalNum'] ?: 0,
                'list'  => $list,
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionGetAnnouncementListHtml()
    {
        try {
            $searchData       = Yii::$app->request->get();
            $announcementList = Announcement::getCompanyDetailList($searchData, true);
            //判断companyId
            if (empty($searchData['companyId'])) {
                throw new Exception('单位id不能为空');
            }
            //判断是否合作单位
            $isCooperation = BaseCompany::checkIsCooperation($searchData['companyId']);

            if ($isCooperation) {
                $temp = 'announcementList.html';
            } else {
                $temp = 'unCooperationAnnouncementList.html';
            }
            $list = $this->renderPartial('/company/detailModule/' . $temp, [
                'list' => $announcementList['list'],
            ]);

            return $this->success([
                'page'  => (int)$searchData['page'] ?: 1,
                'size'  => (int)$searchData['pageSize'] ?: 20,
                'total' => (int)$announcementList['totalNum'] ?: 0,
                'list'  => $list,
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}