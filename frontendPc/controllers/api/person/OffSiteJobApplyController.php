<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseJobApplyRecord;
use common\helpers\ValidateHelper;
use common\service\CommonService;
use frontendPc\models\OffSiteJobApply;
use Yii;

class OffSiteJobApplyController extends BaseFrontPcApiPersonController
{
    /**
     * 获取站外应聘列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        try {
            $searchData             = Yii::$app->request->get();
            $searchData['memberId'] = Yii::$app->user->id;

            return $this->success(OffSiteJobApply::getOffSiteApplyList($searchData,
                OffSiteJobApply::NEED_PAGE_INFO_YES));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 新增/编辑站外投递信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data     = Yii::$app->request->post();
        $memberId = Yii::$app->user->id;
        //判断参数
        if (empty($data['link']) || empty($data['jobName']) || empty($data['applyStatus']) || empty($data['applyDate'])) {
            return $this->fail('缺失必填参数');
        }
        if (!empty($data['id'])) {
            //判断编辑的投递信息是否存在
            $model = OffSiteJobApply::find()
                ->where(['id' => $data['id']])
                ->andWhere(['status' => OffSiteJobApply::STATUS_ACTIVE])
                ->one();
            if (empty($model)) {
                return $this->fail('该站外投递信息不存在');
            }
        }
        if (!empty($data['email'])) {
            if (!ValidateHelper::isEmail($data['email'])) {
                return $this->fail('邮箱格式错误');
            }
        }
        $tran = Yii::$app->db->beginTransaction();
        try {
            $data['delivery_way']       = BaseJobApplyRecord::DELIVERY_WAY_CUSTOM;//自主录入
            $data['platform']           = BaseJobApplyRecord::PLATFORM_PC;
            $data['operation_platform'] = CommonService::PLATFORM_WEB;
            OffSiteJobApply::saveInfo($data, $memberId);
            $tran->commit();

            return $this->success();
        } catch (\Exception $e) {
            $tran->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除站外投递
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDel()
    {
        $id       = Yii::$app->request->post('id');
        $memberId = Yii::$app->user->id;
        //判断记录是否属于当前用户
        $model = OffSiteJobApply::find()
            ->where(['id' => $id])
            ->select([
                'status',
                'member_id',
            ])
            ->asArray()
            ->one();
        if ($model['status'] != OffSiteJobApply::STATUS_ACTIVE) {
            return $this->fail('该站外投递信息状态错误');
        }

        if ($model['member_id'] != $memberId) {
            return $this->fail('该站外投递信息不属于当前用户');
        }

        try {
            OffSiteJobApply::delInfo($id);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}