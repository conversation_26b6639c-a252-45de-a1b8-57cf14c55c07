<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\Announcement;
use frontendPc\models\AnnouncementCollect;
use frontendPc\models\Company;
use frontendPc\models\News;
use frontendPc\models\NewsCollect;
use Yii;
use yii\base\Exception;

class NewsController extends BaseFrontPcApiPersonController
{
    /**
     * 获取资讯热点列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCollectList()
    {
        try {
            $searchData             = Yii::$app->request->get();
            $searchData['memberId'] = Yii::$app->user->id;

            return $this->success(NewsCollect::getList($searchData, NewsCollect::NEED_PAGE_INFO_YES));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 收藏资讯
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCollect()
    {
        $ids = Yii::$app->request->post('newsId');
        try {
            $idArr = explode(',', $ids);
            foreach ($idArr as $k => $id) {
                //判断职位是否存在
                $NewsInfo = News::find()
                    ->where(['id' => $id])
                    ->select(['status'])
                    ->asArray()
                    ->one();
                if (!$NewsInfo) {
                    return $this->fail('资讯不存在');
                }
                if ($NewsInfo['status'] != News::STATUS_ACTIVE) {
                    return $this->fail('资讯状态错误');
                }
            }
            NewsCollect::saveInfo($idArr, Yii::$app->user->id);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

}