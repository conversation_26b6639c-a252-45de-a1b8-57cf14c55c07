<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseResumeJobFootprint;

class JobFootprintController extends BaseFrontPcApiPersonController
{
    public function actionGetList()
    {
        $params   = \Yii::$app->request->get();
        $resumeId = $this->getResumeId();

        return $this->success(BaseResumeJobFootprint::getListForResume($params, $resumeId));
    }
}