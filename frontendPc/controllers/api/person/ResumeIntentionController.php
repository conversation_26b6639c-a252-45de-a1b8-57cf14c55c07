<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseCategoryJob;
use common\base\models\BaseResume;
use common\base\models\BaseResumeIntention;
use frontendPc\models\ResumeIntention;
use yii\base\Exception;
use Yii;

class ResumeIntentionController extends BaseFrontPcApiPersonController
{
    /**
     * 保存简历求职意向
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data     = \Yii::$app->request->post();
        $memberId = \Yii::$app->user->id;

        $data['memberId'] = $memberId;
        try {
            ResumeIntention::saveInfo($data);

            return $this->success(ResumeIntention::getInfoList($memberId));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除求职意向
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeIntention::delIntention($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取求职意向职位类型列表
     */
    public function actionGetCategoryJobList()
    {
        $member_id      = Yii::$app->user->id;
        $resume_id      = BaseResume::findOneval([
            'member_id' => $member_id,
            'status'    => BaseResume::STATUS_ACTIVE,
        ], 'id');
        $intention_list = BaseResumeIntention::getIntentionList($resume_id);
        $result=[];
        foreach ($intention_list as $k=>$value) {
            $result[$k]['id'] = $value['id'];
            $result[$k]['name'] = BaseCategoryJob::getName($value['job_category_id']);
        }

        $this->success($result);
    }
}