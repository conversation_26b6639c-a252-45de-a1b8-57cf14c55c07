<?php

namespace frontendPc\controllers\api\person;

use common\helpers\DebugHelper;
use common\service\payment\ResumeServer as ResumePaymentServer;
use frontendPc\models\ResumeOrder;
use Yii;
use yii\base\Exception;
use yii\helpers\Url;

class ResumeOrderController extends BaseFrontPcApiPersonController
{
    /**
     * 支付
     * 创建预支付订单
     */
    public function actionPay()
    {
        try {
            $equityPackageId = Yii::$app->request->post('equityPackageId');
            $uuid            = Yii::$app->request->post('uuid');
            if (empty($equityPackageId)) {
                throw new Exception('equityPackageId 不能为空');
            }

            // 求职者简历id
            $resumeId = $this->getResumeId();

            if (empty($resumeId)) {
                throw new Exception('resumeId 不能为空');
            }

            $service = new ResumePaymentServer();
            $res     = $service->setPlatform(ResumeOrder::PLATFORM_WEB)
                ->setPayway(ResumeOrder::PAYWAY_WXPAY)
                ->setPayChannel(ResumePaymentServer::CHANNEL_NATIVE)
                ->setNotifyUrl(Url::toRoute('notify', true) . '/payway/' . ResumeOrder::PAYWAY_WXPAY)
                ->setOparetion(ResumePaymentServer::PAY_ORDER)
                ->setParams([
                    'resumeId'        => $resumeId,
                    'uuid'            => $uuid,
                    'equityPackageId' => $equityPackageId,
                ])
                ->run();

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 支付回调
     */
    public function actionNotify()
    {
        try {
            DebugHelper::payNotifyLog('支付回调1', Yii::$app->request->get());
            DebugHelper::payNotifyLog('支付回调2', Yii::$app->request->post());

            $payway = Yii::$app->request->get('payway');

            $service = new ResumePaymentServer();
            $res     = $service->setPayway($payway)
                ->setOparetion(ResumePaymentServer::PAY_NOTIFY)
                ->run();

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 支付查询
     */
    public function actionQuery()
    {
        try {
            $orderId = Yii::$app->request->get('orderId');
            if (empty($orderId)) {
                throw new Exception('OrderId 不能为空');
            }

            // 求职者简历id
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('resumeId 不能为空');
            }

            $service = new ResumePaymentServer();
            $res     = $service->setOparetion(ResumePaymentServer::PAY_QUERY)
                ->setPlatform(ResumeOrder::PLATFORM_WEB)
                ->setParams([
                    'resumeId' => $resumeId,
                    'orderId'  => $orderId,
                ])
                ->run();

            //            $res['jobResourcesInfo'] = [];
            // 支付成功并且购买的权益组合包含求职资源
            // if ($res['status'] == ResumeOrder::STATUS_PAID) {
            //     // 查询订单的权益组合id
            //     $equityPackageId = ResumeOrder::findOneVal(['id' => $orderId], 'equity_package_id');
            //     // 查询权益组合下面的权益
            //     $equityIds = ResumeEquityPackageRelationSetting::getEquityIdsByPackageId($equityPackageId,
            //         ResumeEquitySetting::STATUS_ONLINE);
            //     // 查询权益id是否包含求职资源
            //     if (in_array(ResumeEquitySetting::ID_JOB_RESOURCES, $equityIds)) {
            //         // 获取权益组合信息
            //         $equityPackageRow = ResumeEquityPackageSetting::findOne($equityPackageId);
            //         // 获取求职资源二维码链接
            //         $info = ResumeEquity::getJobResources($resumeId);
            //         // $info['title']           = "已为您开通{$equityPackageRow['subname']}，服务时长为{$equityPackageRow['days']}天";
            //         $info['title']           = "您已经成功下单{$equityPackageRow['name']}服务！";
            //         $info['successContent']  = "套餐内所包含的“求职资料包”权益，需您扫码关注【高校人才网服务号】，回复“求职”，领取VIP专属求职学习资料包！（开通“钻石VIP”套餐 或 “黄金VIP·180天”套餐的会员用户，需回复“会员课程”，领取“高才优课”课程学习。）";
            //         $res['jobResourcesInfo'] = $info;
            //     }
            // }

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 订单状态修改
     * 跳过真实支付
     * 修改未支付订单为已支付
     * 方便测试使用
     */
    public function actionUpdateOrder()
    {
        //关闭测试接口
        try {
            $orderNo = Yii::$app->request->get('orderNo');
            if (empty($orderNo)) {
                throw new Exception('orderNo 不能为空');
            }

            if (Yii::$app->params['environment'] == 'prod') {
                throw new Exception('操作异常');
            }

            $service = new ResumePaymentServer();
            $res     = $service->setOparetion(ResumePaymentServer::PAY_UPDATEORDER)
                ->setParams([
                    'orderNo' => $orderNo,
                    'tradeNo' => "cs{$orderNo}",
                    'notify'  => [],
                ])
                ->run();
            if ($res) {
                return $this->success('操作成功');
            } else {
                return $this->fail('操作失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 支付订单列表
     */
    public function actionGetList()
    {
        try {
            $pageSize = Yii::$app->request->get('pageSize', Yii::$app->params['defaultPageSize']);
            $page     = Yii::$app->request->get('page');

            if (empty($page)) {
                throw new Exception('page 不能为空');
            }

            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('resumeId 不能为空');
            }

            $res = ResumeOrder::getList($resumeId, $pageSize, $page);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}