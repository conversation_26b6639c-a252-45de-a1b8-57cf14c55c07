<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeEducation;
use frontendPc\models\ResumeWork;
use yii\base\Exception;

class ResumeWorkController extends BaseFrontPcApiPersonController
{
    /**
     * 保存简历工作经历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data = \Yii::$app->request->post();
        try {
            ResumeWork::saveInfo($data);

            $memberId = \Yii::$app->user->id;

            return $this->success(ResumeWork::getInfoList($memberId));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeWork::delWork($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }
}