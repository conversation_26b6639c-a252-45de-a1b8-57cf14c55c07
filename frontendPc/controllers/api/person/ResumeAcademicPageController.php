<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeAcademicPage;
use yii\base\BaseObject;
use yii\base\Exception;

class ResumeAcademicPageController extends BaseFrontPcApiPersonController
{
    /**
     * 保存/编辑学术论文
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data = \Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $data['memberId'] = \Yii::$app->user->id;
            ResumeAcademicPage::saveInfo($data);
            $transaction->commit();

            return $this->success(ResumeAcademicPage::getPageList($data['memberId']));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除论文
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeAcademicPage::delPage($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}