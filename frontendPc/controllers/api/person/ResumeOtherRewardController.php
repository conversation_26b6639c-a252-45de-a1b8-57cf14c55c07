<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeOtherReward;
use yii\base\BaseObject;
use yii\base\Exception;

class ResumeOtherRewardController extends BaseFrontPcApiPersonController
{
    /**
     * 保存奖励
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data = \Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeOtherReward::saveInfo($data);

            $transaction->commit();

            $memberId = \Yii::$app->user->id;

            return $this->success(ResumeOtherReward::getRewardList($memberId));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除奖励
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeOtherReward::delReward($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}