<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseJobCollect;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\service\CommonService;
use common\service\job\RecommendService;
use common\service\resume\DeliveryService;
use frontendPc\models\Announcement;
use frontendPc\models\Job;
use frontendPc\models\JobCollect;
use frontendPc\models\ResumeJobReportRecord;
use Yii;
use yii\base\Exception;

class JobController extends BaseFrontPcApiPersonController
{
    /**
     * 收藏职位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCollect()
    {
        $jobIds = Yii::$app->request->post('jobId');
        try {
            $jobArr = explode(',', $jobIds);
            foreach ($jobArr as $k => $jobId) {
                //判断职位是否存在
                $jobInfo = Job::find()
                    ->where(['id' => $jobId])
                    ->select(['status'])
                    ->asArray()
                    ->one();
                if (!$jobInfo) {
                    return $this->fail('职位不存在');
                }

                // 这里有一个很特殊的逻辑,就是这个职位对应的公告如果是双会的话,那么就不允许收藏,templateId=3
                $announcement = Announcement::find()
                    ->where(['job.id' => $jobId])
                    ->innerJoin('job', 'job.announcement_id = announcement.id')
                    ->select(['announcement.template_id'])
                    ->asArray()
                    ->one();

                if ($announcement['template_id'] == Announcement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
                    return $this->fail('对不起，当前职位暂不支持收藏');
                }
            }

            JobCollect::saveInfo($jobArr, Yii::$app->user->id);

            // 如果是单个,收藏成功就返回isCollect = 1,取消收藏成功就返回isCollect = 2
            if (count($jobArr) == 1) {
                $isCollect = BaseJobCollect::checkIsCollect(Yii::$app->user->id, $jobId);
                if ($isCollect) {
                    return $this->success(['isCollect' => '1']);
                } else {
                    return $this->success(['isCollect' => '2']);
                }
            }

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 获取收藏职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCollectList()
    {
        try {
            $searchData             = Yii::$app->request->get();
            $searchData['memberId'] = Yii::$app->user->id;
            $searchData['resumeId'] = $this->getResumeId();

            return $this->success(JobCollect::getList($searchData, JobCollect::NEED_PAGE_INFO_YES));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查用户简历完成度，用于投递前的检查
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckMemberCompleteStatus()
    {
        $memberId = Yii::$app->user->id;

        if (!$memberId) {
            return $this->fail('用户登陆状态错误');
        }
        $resumeId = BaseMember::getMainId($memberId);

        $completePercent = Yii::$app->params['completeResumePercent'];

        //获取用户简历完成度
        $percent                      = BaseResume::getComplete($memberId) ?: 0;
        $data                         = [];
        $data['resume_complete_text'] = "简历完成度{$percent}%";
        if ($percent < $completePercent) {
            //获取用户简历完成步数
            $data['resumeStepNum'] = BaseResumeComplete::getResumeStep($resumeId);
            $data['title']         = '提示';
            $data['content']       = '您的在线简历完善度' . $percent . '%，简历完善度达' . $completePercent . '%方可投递。请先完善简历';

            return $this->success($data);
        } else {
            return $this->success($data);
        }
    }

    /**
     * 申请职位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionApply()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //调用服务
            $service = new DeliveryService();
            $result  = $service->setPlatform(CommonService::PLATFORM_WEB)
                ->setOparetion(DeliveryService::OPERATION_TYPE_APPLY)
                ->init()
                ->run();

            $transaction->commit();
            $data = [
                'toastType'            => $result['toast_type'] ?? '',
                'link'                 => $result['link'] ?? '',
                'successContentUp'     => $result['apply_success_tips'] ?? '',
                'successContentDown'   => $result['apply_success_qrcode_tips'] ?? '',
                'qrcodeLink'           => $result['qrcode_link'] ?? '',
                'applyStatus'          => $result['apply_status'],
                'wxBindQrCodeImageUrl' => $result['wxBindQrCodeImageUrl'] ?? '',
                'applySuccessMsg'      => $result['applySuccessMsg'] ?? '',
                'applyId'              => $result['applyId'] ?? '',
            ];

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    //    /**
    //     * 获取推荐职位列表
    //     * @return \yii\console\Response|\yii\web\Response
    //     */
    //    public function actionGetRecommendList()
    //    {
    //        try {
    //            $data['list'] = Job::getRecommendList(['pageSize' => Yii::$app->params['recommendJobCount']]);
    //            $data['url']  = BaseJob::getRecommendJobUrl();
    //
    //            return $this->success($data);
    //        } catch (\Exception $e) {
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    //
    /**
     * 获取详情推荐模块
     * @return \yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetRecommendList()
    {
        $memberId = Yii::$app->user->id;
        $id       = Yii::$app->request->post('id');
        if (empty($id)) {
            return $this->success(['html' => '']);
        }

        //推荐公告
        $recommendService    = new RecommendService();
        $recommendSearchData = [
            'memberId' => $memberId ?: '',
            'jobId'    => $id,
            'limit'    => 8,
        ];
        $recommendList       = $recommendService->setData($recommendSearchData)
            ->getRecommendList();
        $recommendList0      = array_slice($recommendList, 0, 4);
        $recommendList1      = array_slice($recommendList, 4, 4);
        //推荐列表会分成2块
        $html = $this->renderPartial('/job/recommend.html', [
            'recommendList0' => $recommendList0,
            'recommendList1' => $recommendList1,
        ]);

        return $this->success(['html' => $html]);
    }

    /**
     * 获取职位使用分析报告列表
     */
    public function actionGetReportRecordList()
    {
        try {
            $pageSize = Yii::$app->request->get('pageSize', Yii::$app->params['defaultPageSize']);
            $page     = Yii::$app->request->get('page');

            if (empty($page)) {
                throw new Exception('page 不能为空');
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }

            $res = ResumeJobReportRecord::getReportRecordList($resumeId, $pageSize, $page);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 用于生成报告前的检查
     */
    public function actionCheckGenerateReport()
    {
        try {
            // 获取职位id
            $jobId = Yii::$app->request->get('jobId');
            // 是否需要确认,非必要参数
            $isConfirm = Yii::$app->request->get('isConfirm', 0);

            if (empty($jobId)) {
                throw new Exception('jobId 不能为空');
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }

            // 校验
            $res = ResumeJobReportRecord::checkGenerateReport($resumeId, $jobId, $isConfirm);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 生成报告
     * 记录查看记录
     * 记录权益消耗
     */
    public function actionCreateReport()
    {
        try {
            // 获取职位id
            $jobId = Yii::$app->request->get('jobId');
            if (empty($jobId)) {
                throw new Exception('jobId 不能为空');
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }
            // 校验
            $res = ResumeJobReportRecord::checkGenerateReport($resumeId, $jobId);
            if ($res['jump_type'] == 1) {
                // 写记录
                ResumeJobReportRecord::saveReportAndEquityActionRecord($resumeId, $jobId);

                return $this->success(['jump_url' => Job::getReportUrl($resumeId, $jobId)]);
            } else {
                throw new Exception('操作异常');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}