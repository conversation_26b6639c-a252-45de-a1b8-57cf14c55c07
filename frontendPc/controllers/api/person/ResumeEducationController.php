<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeEducation;
use yii\base\Exception;

class ResumeEducationController extends BaseFrontPcApiPersonController
{
    /**
     * 保存简历教育经历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data = \Yii::$app->request->post();
        try {
            $data['memberId'] = \Yii::$app->user->id;

            ResumeEducation::saveInfo($data);

            return $this->success(ResumeEducation::getInfoList($data['memberId']));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除教育经历
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDel()
    {
        $id = \Yii::$app->request->post('id');

        $transaction = \Yii::$app->db->beginTransaction();
        $memberId    = \Yii::$app->user->id;

        try {
            ResumeEducation::delEducation($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }
}