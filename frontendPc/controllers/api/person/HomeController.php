<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\base\models\BaseUploadForm;
use common\helpers\FileHelper;
use common\libs\SmsQueue;
use frontendPc\models\HomeColumn;
use frontendPc\models\Member;
use Yii;

class HomeController extends BaseFrontPcApiPersonController
{
    public function actionLogin()
    {
    }

    /**
     * 发送绑定手机号验证码
     */
    public function actionSendBindMobileCode()
    {
        $request = Yii::$app->request->post();

        $memberLoginForm             = new BaseMemberLoginForm();
        $memberLoginForm->mobile     = $request['mobile'];
        $memberLoginForm->mobileCode = $request['mobileCode'];
        $memberLoginForm->smsType    = SmsQueue::TYPE_BIND_MOBILE;
        $memberLoginForm->type       = Member::TYPE_PERSON;

        try {
            $memberLoginForm->sendMobileCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证绑定手机验证码
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionValidateBindMobileCode()
    {
        $request = Yii::$app->request->post();

        $memberLoginForm             = new BaseMemberLoginForm();
        $memberLoginForm->mobile     = $request['mobile'];
        $memberLoginForm->mobileCode = $request['mobileCode'];
        $memberLoginForm->code       = $request['code'];
        $memberLoginForm->type       = Member::TYPE_PERSON;

        try {
            $memberLoginForm->bindMobile();

            return $this->success('手机号绑定成功');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传头像
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadAvatar()
    {
        $model = new BaseUploadForm();

        $transaction = Yii::$app->db->beginTransaction();
        $memberId    = Yii::$app->user->id;
        try {
            $data = $model->uploadAvatar();

            $transaction->commit();

            //新增操作日志
            $log_data = [
                'content' => '上传头像，memberId：' . $memberId,
            ];
            // 写日志
            BaseMemberActionLog::log($log_data);
            //更新简历最后更新时间
            $resumeId = BaseMember::getMainId($memberId);
            BaseResume::updateLastUpdateTime($resumeId);

            return $this->success([
                'id'      => (string)$data['id'],
                'url'     => $data['path'],
                'fullUrl' => FileHelper::getFullUrl($data['path']),
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    public function actionGetDefaultAvatar()
    {
        $avatar = [
            '0' => '111',
            '1' => '222',
            '2' => '333',
        ];
    }

    /**
     * 修改头像
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeAvatar()
    {
        $data = Yii::$app->request->post();
        try {
            Member::changeAvatar($data);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}