<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\AnnouncementCollect;
use frontendPc\models\CompanyCollect;
use frontendPc\models\JobCollect;
use frontendPc\models\NewsCollect;
use Yii;

class CollectController extends BaseFrontPcApiPersonController
{




    /**
     * 修改收藏
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChangeCollectCompanyStatus(){
        try {
            $memberId = Yii::$app->user->id;
            CompanyCollect::changeStatus($memberId);
            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

//    public function action


}