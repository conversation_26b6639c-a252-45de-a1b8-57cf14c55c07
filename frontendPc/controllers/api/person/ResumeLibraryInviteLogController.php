<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseMember;
use common\base\models\BaseResumeLibraryInviteLog;
use common\service\commonResume\CommonResumeApplication;
use yii\base\Exception;

class ResumeLibraryInviteLogController extends BaseFrontPcApiPersonController
{
    public function actionGetList()
    {
        try {
            $searchData = \Yii::$app->request->get();

            $searchData['memberId'] = \Yii::$app->user->id;
            $searchData['resumeId'] = BaseMember::getMainId($searchData['memberId']);

            $service = CommonResumeApplication::getInstance();

            return $this->success($service->getPersonInviteJobList($searchData));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}