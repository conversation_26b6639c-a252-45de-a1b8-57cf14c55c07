<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseResume;
use common\base\models\BaseResumeEquityPackageSetting;
use common\service\CommonService;
use common\service\resume\DeliveryService;
use frontendPc\models\HomeColumn;
use frontendPc\models\Member;
use frontendPc\models\MemberLoginForm;
use frontendPc\models\Resume;
use frontendPc\models\ResumeEquity;
use Yii;
use yii\base\Exception;

class MemberController extends BaseFrontPcApiPersonController
{
    /**
     * 保存邮箱注册账号（未激活状态）
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveEmailAccount()
    {
        $email    = Yii::$app->request->post('email');
        $password = Yii::$app->request->post('password');
        $type     = Yii::$app->request->post('type', Member::TYPE_PERSON);

        $loginForm = new MemberLoginForm();

        $loginForm->email     = $email;
        $loginForm->password  = $password;
        $loginForm->loginType = MemberLoginForm::LOGIN_TYPE_EMAIL;
        $loginForm->type      = $type;

        try {
            $rel = $loginForm->saveEmailAccount();
            if ($rel) {
                return $this->success();
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取个人中心信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetPersonCenterInfo()
    {
        try {
            $memberId = Yii::$app->user->id;

            return $this->success(Member::getPersonCenterInfo($memberId));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取用户列表页码的个人信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetListPageMemberInfo()
    {
        try {
            $memberId = Yii::$app->user->id;

            return $this->success(Member::getListPageMemberInfo($memberId));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取用户的基本信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetUserInfo()
    {
        try {
            $memberId = Yii::$app->user->id;
            if (empty($memberId)) {
                return $this->fail('用户未登录');
            }

            return $this->success(Resume::getResumeUserInfo($memberId));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * vip筛选部分获取用户信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetVipFilterInfo()
    {
        $data['isLogin'] = false;
        $data['isVip']   = false;
        $memberId        = Yii::$app->user->id;
        if (!empty($memberId)) {
            $data['isLogin'] = true;
            //判断是否是VIP
            $data['isVip'] = BaseResume::checkVip($memberId);
        }

        return $this->success($data);
    }

    /**
     * 检查用户状态
     */
    public function actionCheckUserApplyStatus()
    {
        try {
            $service = new DeliveryService();
            $result  = $service->setPlatform(CommonService::PLATFORM_WEB)
                ->setOparetion(DeliveryService::OPERATION_TYPE_CHECK_APPLY)
                ->init()
                ->run();

            return $this->success($result);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionGetHeadNav()
    {
        $announcementAndChaptersList = Yii::$app->params['announcementAndChapters'];
        $homeSubNav                  = Yii::$app->params['homeSubNav'];
        $homeNavArea                 = Yii::$app->params['homeNavArea'];
        $hotColumn                   = $announcementAndChaptersList['hotColumn'];
        $provinceColumn              = $homeNavArea['province'];
        $cityColumn                  = $homeNavArea['city'];
        $majorColumn                 = $announcementAndChaptersList['majorColumn'];

        foreach ($hotColumn as &$item) {
            $item['url'] = HomeColumn::getDetailUrl($item['id']);
        }

        foreach ($provinceColumn as &$item) {
            $item['url'] = HomeColumn::getDetailUrl($item['id']);
        }

        foreach ($cityColumn as &$item) {
            $item['url'] = HomeColumn::getDetailUrl($item['id']);
        }

        foreach ($majorColumn as &$item) {
            $item['url'] = HomeColumn::getDetailUrl($item['id']);
        }

        return $this->success([
            'hotColumn'      => $hotColumn,
            'provinceColumn' => $provinceColumn,
            'cityColumn'     => $cityColumn,
            'majorColumn'    => $majorColumn,
            'areaMoreUrl'    => '/region.html',
            'majorMoreUrl'   => '/major.html',
        ]);
    }

    // public function actionBindQrcode()
    // {
    //     $resumeId = $this->getResumeId();
    //
    //     try {
    //         $url = WxPublic::getInstance()
    //             ->createBindQrCode($resumeId);
    //
    //         bb($url);
    //     } catch (\Exception $e) {
    //         return $this->fail($e->getMessage());
    //     }
    // }
    //
    // public function actionCheckLoginQrcode()
    // {
    // }
    //
    // public function actionLoginQrcode()
    // {
    //     try {
    //         $url  = WxPublic::getInstance()
    //             ->createLoginQrcode();
    //         $html = '<img src="' . $url . '" alt="微信扫码绑定" />';
    //         echo $html;
    //         exit;
    //     } catch (\Exception $e) {
    //         return $this->fail($e->getMessage());
    //     }
    // }

    /**
     * 获取个人中心底部vip广告信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetPersonCenterVipCardInfo()
    {
        $resume_id       = $this->getResumeId();
        $memberId        = Yii::$app->user->id;
        $dailyFirstClick = ResumeEquity::getDailyPersonCenterVipShowcase($memberId);
        $isCloseShowcase = ResumeEquity::getClosePersonCenterVipShowcase($memberId);
        $data            = [];

        if (!$isCloseShowcase && $dailyFirstClick) {
            $data = BaseResumeEquityPackageSetting::getPersonCenterVipCardInfo($resume_id);
            ResumeEquity::setDailyPersonCenterVipShowcase($memberId);
        }

        return $this->success($data);
    }

    /**
     * 个人中心关闭底部广告·
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionClosePersonCenterVipCardInfo()
    {
        if (Yii::$app->user->isGuest) {
            return $this->fail();
        }

        $memberId = Yii::$app->user->id;

        ResumeEquity::setClosePersonCenterVipShowcase($memberId);

        return $this->success();
    }

    /**
     * 验证用户手机号
     * @throws Exception
     */
    public function actionVerifyMemberMobile()
    {
        try {
            $mobile = Yii::$app->request->post('mobile');
            if (!$mobile) {
                return $this->fail();
            }

            return $this->success(Member::verifyMemberMobile($mobile));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}