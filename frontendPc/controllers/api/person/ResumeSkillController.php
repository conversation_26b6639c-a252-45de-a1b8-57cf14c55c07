<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeSkill;
use yii\base\BaseObject;
use yii\base\Exception;

class ResumeSkillController extends BaseFrontPcApiPersonController
{
    public function actionSave()
    {
        $data = \Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeSkill::saveInfo($data);
            $transaction->commit();

            $memberId = \Yii::$app->user->getId();

            return $this->success(ResumeSkill::getSkillList($memberId));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeSkill::delSkill($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}