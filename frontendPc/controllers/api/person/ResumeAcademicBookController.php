<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeAcademicBook;
use yii\base\BaseObject;
use yii\base\Exception;

class ResumeAcademicBookController extends BaseFrontPcApiPersonController
{
    /**
     * 保存著作
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data = \Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $data['memberId'] = \Yii::$app->user->id;
            ResumeAcademicBook::saveInfo($data);
            $transaction->commit();

            return $this->success(ResumeAcademicBook::getBookList($data['memberId']));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除著作
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeAcademicBook::delBook($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}