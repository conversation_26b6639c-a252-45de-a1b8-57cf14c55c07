<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BasePayTransformBuriedPointLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use frontendPc\models\ResumeEquityPackageCategorySetting;
use frontendPc\models\ResumeEquityPackageSetting;
use Yii;
use yii\base\Exception;

class ResumeEquityPackageController extends BaseFrontPcApiPersonController
{
    /**
     * 查询购买组合
     */
    public function actionGetBuyPackageList()
    {
        try {
            $equityPackageCategoryId = Yii::$app->request->get('equityPackageCategoryId');

            if (empty($equityPackageCategoryId)) {
                throw new Exception('equityPackageCategoryId 不能为空');
            }
            $isLogin = !(Yii::$app->user->isGuest);
            //如果登录了就获取一下resumeId
            $resumeId = 0;
            if ($isLogin) {
                //登录了就多返回一下信息
                $resumeInfo = BaseResume::findOne(['member_id' => Yii::$app->user->id]);
                $resumeId   = $resumeInfo->id;
            }

            $res = ResumeEquityPackageSetting::getPackageListByCidForBuy($equityPackageCategoryId, $resumeId);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionClosePackage()
    {
        $equityPackageCategoryId = Yii::$app->request->post('equityPackageCategoryId', 1);

        if (Yii::$app->user->isGuest) {
            return $this->fail();
        }

        if (!$equityPackageCategoryId) {
            return $this->fail();
        }

        $resumeInfo = BaseResume::findOne(['member_id' => Yii::$app->user->id]);
        $resumeId   = $resumeInfo->id;

        ResumeEquityPackageSetting::setIsFirstClose($equityPackageCategoryId, $resumeId);

        return $this->success('');
    }

    // 这一个和上面的几乎是一样的，只是竞争力的页面会多一个引导购买黄金的
    public function actionGetMorePackage()
    {
        try {
            $res = ResumeEquityPackageSetting::getMorePackageForInsight();

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 查询弹窗组合
     */
    public function actionGetPopupPackageList()
    {
        try {
            $equityPackageCategoryId = Yii::$app->request->get('equityPackageCategoryId');

            if (empty($equityPackageCategoryId)) {
                throw new Exception('equityPackageCategoryId 不能为空');
            }

            //这里判断一下当前人的会员类型
            //如果是钻石会员，且想开通的是黄金会员就提示不能开通
            ///获取简历信息
            $resumeInfo = BaseResume::findOne(['member_id' => Yii::$app->user->id]);
            $res        = ResumeEquityPackageSetting::getPackageListByCidForBuy($equityPackageCategoryId,
                $resumeInfo->id);

            //埋点
            // let productName = $('.list:eq('+index+') .title').text()
            // let logData = {
            //     params : { productId:id, productName:productName, clickPosition:position },
            //     actionType : '1',
            //     actionId : '10010003'
            // }
            $index    = Yii::$app->request->get('index');
            $uuid     = Yii::$app->request->get('uuid');
            $position = Yii::$app->request->get('position');
            (new BasePayTransformBuriedPointLog())->setPlatform(BasePayTransformBuriedPointLog::PLATFORM_TYPE_PC)
                ->setActionId(BasePayTransformBuriedPointLog::getEquityActionId(BasePayTransformBuriedPointLog::PLATFORM_TYPE_PC,
                    $equityPackageCategoryId))
                ->setActionType(BasePayTransformBuriedPointLog::ACTION_TYPE_CLICK)
                ->setUuid($uuid)
                ->setEventParams([
                    'productId'     => $res['list'][$index]['equity_package_id'],
                    'productName'   => $res['list'][$index]['name'],
                    'productType'   => $res['list'][$index]['subname'],
                    'clickPosition' => $position,
                ])
                ->createLog();
            if ($resumeInfo->vip_level == BaseResume::VIP_LEVEL_DIAMOND) {
                if ($equityPackageCategoryId == BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP) {
                    throw new Exception('尊敬的钻石VIP会员，您当前已拥有最高会员权益，请于钻石VIP套餐过期后再购买黄金VIP套餐。');
                }
            }
            //这里判断一下是会员就不让开洞察
            if ($resumeInfo->vip_type == BaseResume::VIP_TYPE_ACTIVE && $equityPackageCategoryId == BaseResumeEquityPackageCategorySetting::ID_INSIGHT) {
                throw new Exception('当前VIP套餐已含竞争力洞察相关权益，无须重复购买');
            }
            // 弹窗配置
            $config = ResumeEquityPackageCategorySetting::PAY_POPOVER_CONFIG[$equityPackageCategoryId] ?? [];
            //升级
            $isUpgrade = false;
            if ($equityPackageCategoryId == BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP) {
                if ($resumeInfo->vip_level == BaseResume::VIP_LEVEL_GOLD) {
                    $config['name'] = $config['upgradeName'];
                    $isUpgrade      = true;
                }
                unset($config['upgradeName']);
            }

            $res['config']    = $config;
            $res['isUpgrade'] = $isUpgrade;

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 查询购买组合
     * 活动
     */
    public function actionGetActivityBuyPackageList()
    {
        try {
            $equityPackageCategoryId = Yii::$app->request->get('equityPackageCategoryId');

            if (empty($equityPackageCategoryId)) {
                throw new Exception('equityPackageCategoryId 不能为空');
            }
            $isLogin = !(Yii::$app->user->isGuest);
            //如果登录了就获取一下resumeId
            $resumeId = 0;

            if ($isLogin) {
                //登录了就多返回一下信息
                $resumeInfo = BaseResume::findOne(['member_id' => Yii::$app->user->id]);
                $resumeId   = $resumeInfo->id;
            }

            $res = ResumeEquityPackageSetting::getPackageListByCidForBuy($equityPackageCategoryId, $resumeId, true);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 查询弹窗组合
     * 活动
     */
    public function actionGetActivityPopupPackageList()
    {
        try {
            $equityPackageCategoryId = Yii::$app->request->get('equityPackageCategoryId');

            if (empty($equityPackageCategoryId)) {
                throw new Exception('equityPackageCategoryId 不能为空');
            }
            //这里判断一下当前人的会员类型
            //如果是钻石会员，且想开通的是黄金会员就提示不能开通
            ///获取简历信息
            $resumeInfo = BaseResume::findOne(['member_id' => Yii::$app->user->id]);
            if ($resumeInfo->vip_level == BaseResume::VIP_LEVEL_DIAMOND) {
                if ($equityPackageCategoryId == BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP) {
                    throw new Exception('尊敬的钻石VIP会员，您当前已拥有最高会员权益，请于钻石VIP套餐过期后再购买黄金VIP套餐。');
                }
            }
            //这里判断一下是会员就不让开洞察
            if ($resumeInfo->vip_type == BaseResume::VIP_TYPE_ACTIVE && $equityPackageCategoryId == BaseResumeEquityPackageCategorySetting::ID_INSIGHT) {
                $vip_name = $resumeInfo->vip_level == BaseResume::VIP_LEVEL_DIAMOND ? BaseResume::VIP_LEVEL_DIAMOND_TEXT : BaseResume::VIP_LEVEL_GOLD_TEXT;
                throw new Exception('当前VIP套餐已含竞争力洞察相关权益，无须重复购买');
            }
            // 弹窗配置
            $config         = ResumeEquityPackageCategorySetting::PAY_POPOVER_CONFIG[$equityPackageCategoryId] ?? [];
            $config['name'] = ResumeEquityPackageCategorySetting::findOneVal(['id' => $equityPackageCategoryId],
                'name');
            $res            = ResumeEquityPackageSetting::getPackageListByCidForBuy($equityPackageCategoryId,
                $resumeInfo->id, true);
            $res['config']  = $config;

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}