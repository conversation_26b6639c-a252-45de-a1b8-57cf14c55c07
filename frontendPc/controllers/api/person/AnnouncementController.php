<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseAnnouncement;
use common\models\ResumeAnnouncementFootprint;
use common\service\announcement\RecommendService;
use common\service\CommonService;
use common\service\resume\DeliveryService;
use frontendPc\models\Announcement;
use frontendPc\models\AnnouncementCollect;
use frontendPc\models\Company;
use frontendPc\models\Job;
use frontendPc\models\ResumeAnnouncementReportRecord;
use Yii;
use yii\base\Exception;

class AnnouncementController extends BaseFrontPcApiPersonController
{
    /**
     * 获取收藏公告列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCollectList()
    {
        try {
            $searchData             = Yii::$app->request->get();
            $searchData['memberId'] = Yii::$app->user->id;
            $searchData['resumeId'] = $this->getResumeId();

            return $this->success(AnnouncementCollect::getList($searchData, AnnouncementCollect::NEED_PAGE_INFO_YES));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 收藏公告
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCollect()
    {
        $ids = Yii::$app->request->post('announcementId');
        if (empty($ids)) {
            // 前端很多时候都传错为id
            $ids = Yii::$app->request->post('id');
        }

        if (empty($ids)) {
            return $this->fail('参数错误');
        }

        try {
            $idArr = explode(',', $ids);
            foreach ($idArr as $k => $id) {
                //判断是否存在
                $announcementInfo = Announcement::find()
                    ->where(['id' => $id])
                    ->select(['status'])
                    ->asArray()
                    ->one();
                if (!$announcementInfo) {
                    return $this->fail('公告不存在');
                }
                if ($announcementInfo['status'] != Announcement::STATUS_ACTIVE) {
                    return $this->fail('公告状态错误');
                }
            }

            AnnouncementCollect::saveInfo($idArr, Yii::$app->user->id);

            return $this->success();
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    //    /**
    //     * 获取推荐列表
    //     * @return \yii\console\Response|\yii\web\Response
    //     */
    //    public function actionGetRecommendList()
    //    {
    //        try {
    //            $searchData = Yii::$app->request->get();
    //
    //            return $this->success(Announcement::getRecommendList($searchData));
    //        } catch (\Exception $e) {
    //            return $this->fail($e->getMessage());
    //        }
    //    }
    //
    /**
     * 获取公告详情推荐模块
     * @return \yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetRecommendList()
    {
        $id       = Yii::$app->request->post('id');
        if (empty($id)) {
            return $this->success(['html' => '']);
        }

        //推荐公告
        $recommendService    = new RecommendService();
        $recommendSearchData = [
            'announcementId' => $id,
            'limit'          => 8,
        ];
        $recommendList       = $recommendService->setData($recommendSearchData)
            ->getRecommendList();
        $recommendList0      = array_slice($recommendList, 0, 4);
        $recommendList1      = array_slice($recommendList, 4, 4);
        $html                = $this->renderPartial('/announcement/recommend.html', [
            'recommendList0' => $recommendList0,
            'recommendList1' => $recommendList1,
        ]);

        return $this->success(['html' => $html]);
    }

    /**
     * 公告下投递检查
     * @param int announcementId 公告ID
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckAnnouncementApply()
    {
        try {
            $service = new DeliveryService();
            $result  = $service->setPlatform(CommonService::PLATFORM_WEB)
                ->setOparetion(DeliveryService::OPERATION_TYPE_CHECK_ANNOUNCEMENT_APPLY)
                ->init()
                ->run();

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取公告使用分析报告列表
     */
    public function actionGetReportRecordList()
    {
        try {
            $pageSize = Yii::$app->request->get('pageSize', Yii::$app->params['defaultPageSize']);
            $page     = Yii::$app->request->get('page');

            if (empty($page)) {
                throw new Exception('page 不能为空');
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }
            $res = ResumeAnnouncementReportRecord::getReportRecordList($resumeId, $pageSize, $page);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 用于生成报告前的检查
     */
    public function actionCheckGenerateReport()
    {
        try {
            // 获取公告id
            $announcementId = Yii::$app->request->get('announcementId');
            if (empty($announcementId)) {
                throw new Exception('announcementId 不能为空');
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }
            // 校验
            $res = ResumeAnnouncementReportRecord::checkGenerateReport($resumeId, $announcementId);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 生成报告
     * 记录查看记录
     * 记录权益消耗
     */
    public function actionCreateReport()
    {
        try {
            // 获取公告id
            $announcementId = Yii::$app->request->get('announcementId');
            if (empty($announcementId)) {
                throw new Exception('announcementId 不能为空');
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }
            // 校验
            $res = ResumeAnnouncementReportRecord::checkGenerateReport($resumeId, $announcementId);
            if ($res['jump_type'] == 1) {
                // 写记录
                ResumeAnnouncementReportRecord::saveReportAndEquityActionRecord($resumeId, $announcementId);

                return $this->success(['jump_url' => Announcement::getReportUrl($resumeId, $announcementId)]);
            } else {
                throw new Exception('操作异常');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}