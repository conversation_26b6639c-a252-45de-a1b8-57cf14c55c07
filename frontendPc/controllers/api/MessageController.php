<?php

namespace frontendPc\controllers\api;

use common\base\models\BaseMemberMessage;
use frontendPc\models\MemberMessage;
use Yii;
use yii\base\Exception;
use yii\web\Response;

class MessageController extends BaseFrontPcApiController
{

    /**
     * 面包屑部分
     * @return \yii\console\Response|Response
     */
    public function actionGetBreadcrumb()
    {
        $memberId = Yii::$app->user->id;

        return $this->success(MemberMessage::getBreadcrumb($memberId));
    }

    /**
     * 获取求职者面包屑部分消息
     * @return \yii\console\Response|Response
     */
    public function actionGetPersonBreadcrumb()
    {
        $memberId = Yii::$app->user->id;

        return $this->success(MemberMessage::getPersonBreadcrumb($memberId));
    }

    /**
     * 获取用户未读消息数量
     * @return \yii\console\Response|Response
     */
    public function actionGetUnreadCount()
    {
        $memberId = Yii::$app->user->id;

        return $this->success(['amount' => MemberMessage::getUnreadCountNew($memberId)]);
    }

    /**
     * 获取消息列表接口
     * @return \yii\console\Response|Response
     */
    public function actionGetInfo()
    {
        return $this->success(MemberMessage::getMessageInfo(Yii::$app->request->get(), Yii::$app->user->id));
    }

    /**
     * 获取消息类型数量
     * @return \yii\console\Response|Response
     * @throws Exception
     */
    public function actionGetTypeAmount()
    {
        return $this->success(MemberMessage::getTypeAmount(Yii::$app->request->get(), Yii::$app->user->id));
    }

    /**
     * 批量删除用户消息
     * @return \yii\console\Response|Response
     */
    public function actionDel()
    {
        $memberId = Yii::$app->user->id;

        try {
            $this->success(MemberMessage::updateMessages(Yii::$app->request->post(), BaseMemberMessage::IS_DELETE_YES,
                $memberId));

            return $this->success('成功');
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量已读消息
     * @return \yii\console\Response|Response
     */
    public function actionHaveRead()
    {
        $memberId = Yii::$app->user->id;
        try {
            $this->success(MemberMessage::updateMessages(Yii::$app->request->post(), BaseMemberMessage::READ_YES,
                $memberId));

            return $this->success('成功');
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量已读消息
     * @return \yii\console\Response|Response
     */
    public function actionUnread()
    {
        $memberId = Yii::$app->user->id;
        try {
            $this->success(MemberMessage::updateMessages(Yii::$app->request->post(), BaseMemberMessage::READ_NO,
                $memberId));

            return $this->success('成功');
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取求职者未读消息列表(个人中心滚动)
     * @return \yii\console\Response|Response
     */
    public function actionGetPersonUnreadList()
    {
        $searchData             = Yii::$app->request->get();
        $searchData['memberId'] = Yii::$app->user->id;
        $searchData['limit']    = 6;
        try {
            return $this->success(MemberMessage::getPersonUnreadMessage($searchData));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}