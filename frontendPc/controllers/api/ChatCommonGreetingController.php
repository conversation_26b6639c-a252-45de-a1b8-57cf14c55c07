<?php

namespace frontendPc\controllers\api;

use common\service\chat\ChatApplication;
use frontendPc\controllers\api\BaseFrontPcApiController;

class ChatCommonGreetingController extends BaseFrontPcApiController
{
    /**
     * 获取打招呼语列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        $memberId = \Yii::$app->user->id;

        try {
            $app  = ChatApplication::getInstance();
            $list = $app->getCommonGreeting($memberId);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除打招呼语
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelete()
    {
        $memberId = \Yii::$app->user->id;
        $id       = \Yii::$app->request->post('id');

        try {
            $app = ChatApplication::getInstance();
            $app->deleteCommonGreeting($memberId, $id);

            return $this->success('删除成功');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 添加打招呼语
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $memberId = \Yii::$app->user->id;
        $content  = \Yii::$app->request->post('content');

        if (!$content) {
            return $this->fail('内容不能为空');
        }

        try {
            $app = ChatApplication::getInstance();
            $app->AddOrEditCommonGreeting($memberId, $content);

            return $this->success('保存成功');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑打招呼语
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $memberId = \Yii::$app->user->id;
        $id       = \Yii::$app->request->post('id');
        $content  = \Yii::$app->request->post('content');
        if ($id <= 0) {
            return $this->fail('参数错误');
        }
        if (!$content) {
            return $this->fail('内容不能为空');
        }

        try {
            $app = ChatApplication::getInstance();
            $app->AddOrEditCommonGreeting($memberId, $content, $id);

            return $this->success('保存成功');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改打招呼语配置开关
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditIsGreeting()
    {
        $memberId = \Yii::$app->user->id;
        try {
            $app = ChatApplication::getInstance();
            $app->EditIsGreetingCommonGreeting($memberId);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 修改默认打招呼语
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditDefaultGreeting()
    {
        $memberId = \Yii::$app->user->id;
        $id       = \Yii::$app->request->post('id');
        $type     = \Yii::$app->request->post('type');
        try {
            $app = ChatApplication::getInstance();
            $app->EditDefaultGreetingCommonGreeting($memberId, $id, $type);

            return $this->success('设置成功');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}