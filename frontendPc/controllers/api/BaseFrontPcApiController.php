<?php

namespace frontendPc\controllers\api;

use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseSeoUserAgent;
use common\libs\JwtAuth;
use frontendPc\controllers\BaseFrontendPcController;
use Yii;

class BaseFrontPcApiController extends BaseFrontendPcController
{
    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        $this->testLogin();

        if (in_array($action->uniqueID, $this->ignoreLogin())) {
            return parent::beforeAction($action);
        }

        $this->testLogin();

        if (Yii::$app->user->isGuest) {
            echo json_encode([
                'code'   => 403,
                'result' => 0,
                'msg'    => '请您先进行登录后再操作',
            ]);
            exit;
        }

        // 判断token是否过去
        $token = Yii::$app->request->getHeaders()
            ->get('token');

        if ($token) {
            // 检查token是否过期
            $jwt = new JwtAuth();
            $id  = $jwt->checkToken($token);
            if (!$id) {
                echo json_encode([
                    'code'   => 403,
                    'result' => 0,
                    'msg'    => '请您先进行登录后再操作',
                ]);
                exit;
            }
        }

        return parent::beforeAction($action);
    }

    public function actionLoginOut()
    {
        Yii::$app->user->logout();
    }

    public function testLogin()
    {
        if (Yii::$app->params['environment'] != 'prod') {
            // header里面有一个memberId参数
            $memberId = Yii::$app->request->getHeaders()
                ->get('memberId');
            if (Yii::$app->user->id == $memberId) {
                return true;
            }

            if ($memberId) {
                return (new BaseMemberLoginForm())->loginById($memberId);
            }
        }
    }

    /**
     * 无需登录就可以操作的控制器
     * @return string[]
     */
    public function ignoreLogin()
    {
        return [
            'api/member/send-mobile-login-code',
            'api/member/validate-mobile-login-code',
            'api/member/validate-email',
            'api/member/send-email-register-code',
            'api/member/save-email-account',
            'api/member/activate-account',
            'api/member/account-login',
            'api/member/get-captcha',
            'api/member/check-captcha',
            'api/member/get-info',
            'api/member/login',
            'api/login',
            'api/upload/file',
            'api/upload/image',
            'api/config/load-country-mobile-code',
            'api/member/send-change-password-code',
            'api/member/change-password',
            'api/company/home/<USER>',
            'api/company/member/send-mobile-register-code',
            'api/company/member/send-mobile-bind-code',
            'api/company/member/bind-mobile',
            'api/company/member/create-mobile-register',
            'api/company/member/create-email-register',
            'api/company/member/send-mobile-login-code',
            'api/company/member/send-validate-company-info-code',
            'api/company/member/send-mobile-register-code',
            'api/company/company/get-company-info',
            'api/config/get-dictionary-list',
            'api/member/get-captcha-config',
            'api/member/change-password-by-old-password',
            'api/config/get-company-experience-list',
            'api/config/get-company-type-list',
            'api/config/get-company-nature-list',
            'api/config/get-emote-list',
            'api/member/send-change-password-code',
            'api/member/change-password',
            'api/config/get-private',
            'api/config/carry-column-hot-to-position',
            'api/resume/get-share-resume-info',
            'api/member/get-login-qrcode',
            'api/member/check-login-qrcode',
            'api/member/send-wx-bind-mobile-code',
            'api/member/validate-wx-bind-mobile-code',
            'api/config/get-login-register',
            'api/config/get-position-token',
            'api/config/get-area-info',
            'api/person/member-login-log/create',
            // 这里获取一些基础的url,包含ws的那些
            'api/config/get-base-url',
            'api/member/check-mini-code-login',
            'api/member/get-mobile-info-by-mini-code',
            'api/member/login-by-mini-mobile',
            'api/member/get-mini-login-qrcode',
            'api/member/check-mini-login-qrcode',
        ];
    }
}