<?php

namespace frontendPc\controllers;

use common\base\models\BaseActivity;
use common\service\CommonService;
use Yii;

class ActivityController extends BaseFrontendPcController
{

    // 活动也应该有特定的头部
    public $layout = 'activity';

    /**
     */
    public function actionIndex()
    {
        try {
            $token    = Yii::$app->request->get('token');
            $optionId = Yii::$app->request->get('optionId');
            $model    = BaseActivity::findOne(['token' => $token]);
            if (!$model) {
                $this->notFound();
            }
            $this->layout = 'main';
            $view         = $model->view;

            //这里规则根据需求拓展

            // 设置 seo 相关信息
            $seoConfig = Yii::$app->params['seo']['activity'];

            $title       = str_replace('【表单名称】', $model->name, $seoConfig['title']);
            $description = str_replace('【表单名称】', $model->name, $seoConfig['description']);

            $this->setSeo([
                'title'       => $title,
                'keywords'    => $seoConfig['keywords'],
                'description' => $description,
            ]);

            return $this->render($view . '.html', ['optionId' => $optionId]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionGetForm()
    {
        // 获取表单的内容
    }

    public function actionSubmit()
    {
        // 提交表单的内容

    }

}
