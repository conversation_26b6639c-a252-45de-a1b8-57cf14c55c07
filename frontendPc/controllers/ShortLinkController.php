<?php

namespace frontendPc\controllers;

use common\libs\ShortLink;
use common\libs\WxMiniApp;
use Yii;

class ShortLinkController extends BaseFrontendPcController
{
    /**
     * 短链的基本逻辑
     */
    public function actionIndex()
    {
        $code = Yii::$app->request->get('code');

        if (!$code) {
            $this->notFound();
        }

        try {
            $url = ShortLink::getByCode($code);
        } catch (\Exception $e) {
            $this->notFound();
        }

        if (!$url) {
            $this->notFound();
        }

        // 301过去
        return $this->redirect($url, 301);
    }

    /**
     * 直聊邀约会发送的链接
     * @return string
     */
    public function actionTChat()
    {
        $this->layout = false;

        return $this->render('/short-link/t.html',
            [
                'link' => WxMiniApp::getPersonSchemeUrl('chat'),
                'title' => '点击直达微信小程序消息页面'
            ]);
    }

    /**
     * 投递邀约会发送的链接
     * @return string
     */
    public function actionTInvite()
    {
        $this->layout = false;

        return $this->render('/short-link/t.html',
            [
                'link' => WxMiniApp::getPersonSchemeUrl('jobInvite'),
                'title' => '点击直达微信小程序职位邀约页面'
            ]);
    }
}
