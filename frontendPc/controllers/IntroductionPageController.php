<?php

namespace frontendPc\controllers;

use common\base\models\BasePayTransformBuriedPointLog;
use Yii;

class IntroductionPageController extends BaseFrontendPcController
{
    /**
     * vip介绍页
     */
    public function actionVip()
    {
        $apiPopup = '/api/person/resume-equity-package/get-popup-package-list';
        $apiBuy   = '/api/person/resume-equity-package/get-buy-package-list';

        // 设置 SEO 信息
        $config = Yii::$app->params['seo']['vip'];

        $this->setSeo($config);
        $uuid = (new BasePayTransformBuriedPointLog())->setPlatform(BasePayTransformBuriedPointLog::PLATFORM_TYPE_PC)
            ->setActionId(BasePayTransformBuriedPointLog::ACTION_ID_PC_VIP_VIEW)
            ->setActionType(BasePayTransformBuriedPointLog::ACTION_TYPE_QUEST)
            ->setEventParams([
                'stopTimes'   => 0,
                'sourceEntry' => Yii::$app->request->getReferrer() ?: '',
            ])
            ->createLog();
        $data = [
            'api_popup' => $apiPopup,
            'api_buy'   => $apiBuy,
            'uuid'      => $uuid,
        ];

        return $this->render('vipIntroduce.html', ['data' => $data]);
    }

    /**
     * 竞争力介绍页
     */
    public function actionCompetition()
    {
        // 设置 SEO 信息
        $config = Yii::$app->params['seo']['competitivePower'];
        $this->setSeo($config);

        $uuid = (new BasePayTransformBuriedPointLog())->setPlatform(BasePayTransformBuriedPointLog::PLATFORM_TYPE_PC)
            ->setActionId(BasePayTransformBuriedPointLog::ACTION_ID_PC_INSIGHT_VIEW)
            ->setActionType(BasePayTransformBuriedPointLog::ACTION_TYPE_QUEST)
            ->setEventParams([
                'stopTimes'   => 0,
                'sourceEntry' => Yii::$app->request->getReferrer() ?: '',
            ])
            ->createLog();
        $data = [
            'uuid' => $uuid,
        ];

        return $this->render('competitivePower.html', ['data' => $data]);
    }

    /**
     * vip介绍页(活动)
     */
    public function actionActivityVip()
    {
        $apiPopup = '/api/person/resume-equity-package/get-activity-popup-package-list';
        $apiBuy   = '/api/person/resume-equity-package/get-activity-buy-package-list';

        $data = [
            'api_popup' => $apiPopup,
            'api_buy'   => $apiBuy,
        ];

        return $this->render('vipIntroduce.html', ['data' => $data]);
    }

    /**
     * 求职快介绍页
     */
    public function actionJobFast()
    {
        $apiPopup = '/api/person/resume-equity-package/get-popup-package-list';
        $apiBuy   = '/api/person/resume-equity-package/get-buy-package-list';

        $uuid = (new BasePayTransformBuriedPointLog())->setPlatform(BasePayTransformBuriedPointLog::PLATFORM_TYPE_PC)
            ->setActionId(BasePayTransformBuriedPointLog::ACTION_ID_PC_HUNT_JOB_VIEW)
            ->setActionType(BasePayTransformBuriedPointLog::ACTION_TYPE_QUEST)
            ->setEventParams([
                'stopTimes'   => 0,
                'sourceEntry' => Yii::$app->request->getReferrer() ?: '',
            ])
            ->createLog();
        $data = [
            'api_popup' => $apiPopup,
            'api_buy'   => $apiBuy,
            'uuid'      => $uuid,
        ];

        // 设置 SEO
        $config = Yii::$app->params['seo']['jobFast'];
        $this->setSeo($config);

        return $this->render('jobFast.html', ['data' => $data]);
    }
}