<?php

namespace frontendPc\controllers;

use admin\models\Trade;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseFile;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobClickLog;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobReport;
use common\base\models\BaseJobSubscribe;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEquity;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeWxBind;
use common\base\models\BaseShowcase;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\libs\BaiduTimeFactor;
use common\libs\Cache;
use common\libs\ToutiaoTimeFactor;
use common\libs\WxPublic;
use common\service\chat\CommonRule;
use common\service\job\RecommendService;
use common\service\match\MatchCompleteService;
use common\service\search\CommonSearchApplication;
use common\service\specialNeedService\JobInformationService;
use frontendPc\models\Announcement;
use frontendPc\models\Area;
use frontendPc\models\CategoryJob;
use frontendPc\models\Company;
use frontendPc\models\Dictionary;
use frontendPc\models\HomePosition;
use frontendPc\models\Job;
use frontendPc\models\JobReport;
use frontendPc\models\Major;
use frontendPc\models\Member;
use frontendPc\models\Resume;
use frontendPc\models\ResumeAttachment;
use frontendPc\models\ResumeJobReportRecord;
use frontendPc\models\WelfareLabel;
use Yii;

class JobController extends BaseFrontendPcController
{

    private function handleUrl($parr, $key, $list)
    {
        if ($key == 'areaId' || $key == 'welfareLabelId' || $key == 'companyNature' || $key == 'companyType') {
            $urlCityId    = $parr[$key];
            $urlCityIdArr = explode('_', $urlCityId);
            foreach ($list as $item_key => &$item) {
                if (is_array($item)) {
                    $currentCityIdArr = $urlCityIdArr;
                    // 拿到城市的参数
                    if (in_array($item['id'], $urlCityIdArr)) {
                        $arr           = $parr;
                        $item['class'] = 'active';
                        //如果重复点击，去除active标签
                        $activeKey = array_search($item['id'], $urlCityIdArr);
                        array_splice($currentCityIdArr, $activeKey, 1);
                        $arr[$key] = implode('_', $currentCityIdArr);
                        $arr['p']  = 1;
                        $newParams = http_build_query($arr);
                        // 把最左边的_去掉,
                        $newParams   = ltrim($newParams, '_');
                        $item['url'] = '/job?' . $newParams;
                    } else {
                        $arr         = $parr;
                        $arr[$key]   = ltrim($arr[$key] . '_' . $item['id'], '_');
                        $arr['p']    = 1;
                        $newParams   = http_build_query($arr);
                        $item['url'] = '/job?' . $newParams;
                    }
                } else {
                    //companyNature+companyType
                    $arr         = [];
                    $arr['name'] = $item;
                    $arr['id']   = $item_key;
                    if (in_array($item_key, $urlCityIdArr)) {
                        $company_arr  = $parr;
                        $arr['class'] = 'active';
                        //如果重复点击，去除active标签
                        $activeKey  = array_search($item_key, $urlCityIdArr);
                        $split_parr = $urlCityIdArr;
                        array_splice($split_parr, $activeKey, 1);
                        $company_arr[$key] = implode('_', $split_parr);
                        $company_arr['p']  = 1;
                        $newParams         = http_build_query($company_arr);
                        // 把最左边的_去掉,
                        $newParams  = ltrim($newParams, '_');
                        $arr['url'] = '/job?' . $newParams;
                    } else {
                        $company_arr       = $parr;
                        $company_arr[$key] = ltrim($company_arr[$key] . '_' . $item_key, '_');
                        $company_arr['p']  = 1;
                        $newParams         = http_build_query($company_arr);
                        $arr['url']        = '/job?' . $newParams;
                    }

                    $item = $arr;
                }
            }
        } else {
            foreach ($list as $k => &$item) {
                if (is_array($item)) {
                    $urlId = $parr[$key];
                    if ($item['id'] == $urlId) {
                        $arr           = $parr;
                        $item['class'] = 'active';
                        $arr[$key]     = '';
                        $arr['p']      = 1;
                        $newParams     = http_build_query($arr);
                        $item['url']   = '/job?' . $newParams;
                    } else {
                        $arr         = $parr;
                        $arr[$key]   = $item['id'];
                        $arr['p']    = 1;
                        $newParams   = http_build_query($arr);
                        $item['url'] = '/job?' . $newParams;
                    }
                } else {
                    $arr         = [];
                    $arr['name'] = $item;
                    $arr['id']   = $k;

                    // 拿到城市的参数
                    $urlId = $parr[$key];
                    if ($k == $urlId) {
                        $newArr       = $parr;
                        $newArr[$key] = '';
                        $arr['class'] = 'active';
                        $arr['p']     = 1;
                        $newParams    = http_build_query($newArr);
                        $arr['url']   = '/job?' . $newParams;
                    } else {
                        $newParr       = $parr;
                        $newParr['p']  = 1;
                        $newParr[$key] = $k;
                        $newParams     = http_build_query($newParr);

                        $arr['url'] = '/job?' . $newParams;
                    }
                    $item = $arr;
                }
            }
        }

        return $list;
    }

    public function actionIndex()
    {
        // 未登录,编制筛选,重定向到job路由
        $queryParams = Yii::$app->request->getQueryParams();
        if (Yii::$app->user->isGuest && isset($queryParams['isEstablishment'])) {
            return $this->redirect('/job');
        }

        $seoConfig = Yii::$app->params['seo']['jobList'];

        $this->setSeo([
            'title'       => $seoConfig['title'],
            'keywords'    => $seoConfig['keywords'],
            'description' => $seoConfig['description'],
        ]);

        return $this->render('newIndex2023.html');
    }

    public function actionOldIndex2022()
    {
        // $fullUrl = Yii::$app->request->getHostInfo() . Yii::$app->request->url;
        // $params  = parse_url($fullUrl);
        //
        // $parr = [];
        // if ($params['query']) {
        //     parse_str($params['query'], $parr);//获取URL中的参数，并赋值到$parr数组中
        // }
        // $popover = $parr['popover'] ?: 2;
        // if (!empty($parr['majorId']) && $popover == 2) {
        //     $parentMajorId = Major::getCurrentSearchParentId($parr['majorId']);
        // } else {
        //     $parentMajorId = 0;
        // }
        // //获取广告位的公司列表
        // $showcaseList = HomePosition::getJobShowcase();
        // //获取推荐公司列表
        // //$companyList = Company::getRecommendList();//查看界面没使用
        // //获取工作地点
        // $cityList = self::handleUrl($parr, 'areaId', Area::getSearchHotList());
        // //获取单位性质
        // $companyNatureList = self::handleUrl($parr, 'companyNature', BaseDictionary::getCompanyNatureList());
        // //获取单位类型
        // $companyTypeList = self::handleUrl($parr, 'companyType', BaseDictionary::getCompanyTypeList());
        // //学科分类
        // $tempParr            = $parr;
        // $tempParr['popover'] = 2;
        // $majorList           = self::handleUrl($tempParr, 'majorId',
        //     Major::getMajorList($parr['majorId'], BaseMajor::ADD_UNLIMITED_NO, $popover));
        // //职位福利
        // //$welfareLabelList = self::handleUrl($parr, 'welfareLabelId', WelfareLabel::getWelfareLabelList());
        // //学历要求
        // $educationRequire = self::handleUrl($parr, 'educationType', BaseDictionary::getEducationList());
        // //行业类别
        // $tradeList = self::handleUrl($parr, 'industryId', Trade::getTradeList());
        // //工作年限
        // $experienceList = self::handleUrl($parr, 'experienceType', BaseDictionary::getExperienceList());
        // //发布时间
        // $releaseTimeList = self::handleUrl($parr, 'releaseTimeType', BaseDictionary::getReleaseTimeList());
        // //薪资范围
        // $wageRangeList = self::handleUrl($parr, 'wageId', BaseDictionary::getWageRangeList());
        // //单位规模
        // $companyScaleList = self::handleUrl($parr, 'companyScaleType', BaseDictionary::getCompanyScaleList());
        // //职位性质
        // $jobNatureList = self::handleUrl($parr, 'natureType', BaseDictionary::getNatureList());
        // //岗位类型
        // //$jobTypeList = Dictionary::getJobTypeList();//查看界面没使用
        // //职称
        // $titleTypeList = self::handleUrl($parr, 'titleType', BaseDictionary::getTitleList());
        // // 学科分类一二级回显
        // $majorAllList = Major::getAllListByLevel2();
        //
        // //这里去职位列表热门搜索广告位广告
        // $jobNumber        = 'zhiweiliebiao_remensousuo';
        // $jobHotId         = BaseHomePosition::findOneVal(['number' => $jobNumber], 'id');
        // $jobHotSearchList = BaseShowcase::getByPositionConfig($jobHotId, $jobNumber);
        // foreach ($jobHotSearchList as $k => $value) {
        //     if (strlen($value['real_target_link']) < 1) {
        //         $jobHotSearchList[$k]['url'] = "/job?keyword=" . $value['title'];
        //     }
        // }
        //
        // //设置默认值
        // //       $resumeCompletePercent = 0;
        // //       $resumeAttachmentList  = [];
        // //       $resumeStatus          = Resume::STATUS_UN_COMPLETE_BASE_INFO;
        // //       $userStatus            = Member::USER_STATUS_UN_LOGIN;
        // //       //判断是否已经登录，获取用户信息
        // //       $memberId = Yii::$app->user->id;
        // //       //获取默认的简历id
        // //       $defaultResumeToken = ResumeAttachment::getDefaultResumeToken($memberId);
        // //
        // //       if (!empty($memberId)) {
        // //           //获取用户附件简历列表
        // //           $resumeAttachmentList = ResumeAttachment::getList($memberId);
        // //
        // //           //判断用户当前的状态（是否完成简历前三步）
        // //           $userStatus = Member::getUserResumeStatus($memberId);
        // //           if ($userStatus == Member::USER_STATUS_COMPLETE_RESUME) {
        // //               //如果简历已经完成了，获取简历完成度
        // //               $resumeCompletePercent = BaseResume::getComplete($memberId);
        // //               //已经完成，这时候把简历状态改成已完成
        // //               $resumeStatus = Resume::STATUS_COMPLETE_BASE_INFO;
        // //           }
        // //       } else {
        // //           $memberId = 0;
        // //       }
        //
        // $getInfo = Yii::$app->request->get();
        //
        // // 限制一下
        // if ($getInfo['pageSize'] > 100) {
        //     $getInfo['pageSize'] = 100;
        // }
        // //        $userEmail = '';
        // if (!empty(Yii::$app->user->id)) {
        //     $getInfo['memberId'] = Yii::$app->user->id;
        //     //            //获取用户邮箱
        //     //            $userEmail = Member::findOneVal(['id' => $getInfo['memberId']], 'email');
        // }
        // $categoryJobList = CategoryJob::getPersonCategoryJobList();
        // try {
        //     $data = Job::newSearchForList($getInfo);
        // } catch (\Exception $e) {
        //     $data = [];
        // }
        // $jobList  = $data['list'] ?: [];
        // $totalNum = $data['totalNum'] ?: 0;
        // //拿一下职位类型
        // $jobType    = array_filter(explode('_', $getInfo['jobType']));
        // $jobTypeTxt = BaseCategoryJob::getMultipleJobCategory($jobType);
        //
        // if (!empty($jobList)) {
        //     foreach ($jobList as &$job) {
        //         $job['jobNameTitle'] = str_replace('"', "'", $job['jobName']);
        //         // 公告的title也会面临上面的情况
        //         $job['announcementName'] = str_replace('"', "'", $job['announcementName']);
        //     }
        // }
        //
        // $seoConfig = Yii::$app->params['seo']['jobList'];
        //
        // $this->setSeo([
        //     'title'       => $seoConfig['title'],
        //     'keywords'    => $seoConfig['keywords'],
        //     'description' => $seoConfig['description'],
        // ]);
        //
        // return $this->render('newIndex.html', [
        //     'totalNum'          => $totalNum,
        //     'cityList'          => $cityList,
        //     'companyNatureList' => $companyNatureList,
        //     'companyTypeList'   => $companyTypeList,
        //     'majorList'         => $majorList,
        //     //'welfareLabelList'      => $welfareLabelList,
        //     'educationRequire'  => $educationRequire,
        //     'tradeList'         => $tradeList,
        //     'jobNatureList'     => $jobNatureList,
        //     'experienceList'    => $experienceList,
        //     'wageRangeList'     => $wageRangeList,
        //     //'jobTypeList'           => $jobTypeList,
        //     'companyScaleList'  => $companyScaleList,
        //     'jobList'           => $jobList,
        //     'releaseTimeList'   => $releaseTimeList,
        //     //'resumeAttachmentList'  => $resumeAttachmentList,
        //     //'resumeCompletePercent' => $resumeCompletePercent,
        //     //'userStatus'            => $userStatus,
        //     //'resumeStatus'          => $resumeStatus,
        //     'fullUrl'           => $fullUrl,
        //     'getInfo'           => $getInfo,
        //     //'defaultResumeToken'    => $defaultResumeToken,
        //     //'companyList'           => $companyList,
        //     'parentMajorId'     => $parentMajorId,
        //     'majorId'           => $parr['majorId'],
        //     'titleTypeList'     => $titleTypeList,
        //     //'userEmail'             => $userEmail,
        //     'categoryJobList'   => $categoryJobList,
        //     'showcaseList'      => $showcaseList,
        //     'popover'           => $popover,
        //     'majorAllList'      => $majorAllList,
        //     'jobHotSearchList'  => $jobHotSearchList,
        //     'jobTypeTxt'        => $jobTypeTxt,
        // ]);
    }

    /**
     * @throws \Exception
     */
    public function actionOldIndex()
    {
        $fullUrl = Yii::$app->request->getHostInfo() . Yii::$app->request->url;
        $params  = parse_url($fullUrl);

        $parr = [];
        if ($params['query']) {
            parse_str($params['query'], $parr);//获取URL中的参数，并赋值到$parr数组中
        }

        $popover = $parr['popover'] ?: 2;
        if (!empty($parr['majorId']) && $popover == 2) {
            $parentMajorId = Major::getCurrentSearchParentId($parr['majorId']);
        } else {
            $parentMajorId = 0;
        }

        //获取广告位的公司列表
        $showcaseList = HomePosition::getJobShowcase();

        //获取推荐公司列表
        $companyList = Company::getRecommendList();
        //获取工作地点
        $cityList = self::handleUrl($parr, 'areaId', Area::getSearchHotList());
        //获取单位性质
        $companyNatureList = self::handleUrl($parr, 'companyNature', Dictionary::getCompanyNatureList());
        //获取单位类型
        $companyTypeList = self::handleUrl($parr, 'companyType', Dictionary::getCompanyTypeList());

        //学科分类
        $tempParr            = $parr;
        $tempParr['popover'] = 2;
        $majorList           = self::handleUrl($tempParr, 'majorId',
            Major::getMajorList($parr['majorId'], BaseMajor::ADD_UNLIMITED_NO, $popover));

        //职位福利
        $welfareLabelList = self::handleUrl($parr, 'welfareLabelId', WelfareLabel::getWelfareLabelList());
        //学历要求
        $educationRequire = self::handleUrl($parr, 'educationType', Dictionary::getEducationList());
        //行业类别
        $tradeList = self::handleUrl($parr, 'industryId', Trade::getTradeList());
        //工作年限
        $experienceList = self::handleUrl($parr, 'experienceType', Dictionary::getExperienceList());
        //发布时间
        $releaseTimeList = self::handleUrl($parr, 'releaseTimeType', Dictionary::getReleaseTimeList());
        //薪资范围
        $wageRangeList = self::handleUrl($parr, 'wageId', Dictionary::getWageRangeList());
        //单位规模
        $companyScaleList = self::handleUrl($parr, 'companyScaleType', Dictionary::getCompanyScaleList());
        //职位性质
        $jobNatureList = self::handleUrl($parr, 'natureType', Dictionary::getNatureList());
        //岗位类型
        $jobTypeList = Dictionary::getJobTypeList();
        //职称
        $titleTypeList = self::handleUrl($parr, 'titleType', Dictionary::getTitleList());

        // 学科分类一二级回显
        $majorAllList = Major::getAllListByLevel2();

        //这里去职位列表热门搜索广告位广告
        $jobNumber        = 'zhiweiliebiao_remensousuo';
        $jobHotId         = BaseHomePosition::findOneVal(['number' => $jobNumber], 'id');
        $jobHotSearchList = BaseShowcase::getByPositionConfig($jobHotId, $jobNumber);
        foreach ($jobHotSearchList as $k => $value) {
            if (strlen($value['real_target_link']) < 1) {
                $jobHotSearchList[$k]['url'] = "/job?page=1&keyword=" . $value['title'];
            }
        }

        //设置默认值
        $resumeCompletePercent = 0;
        $resumeAttachmentList  = [];
        $resumeStatus          = Resume::STATUS_UN_COMPLETE_BASE_INFO;
        $userStatus            = Member::USER_STATUS_UN_LOGIN;
        //判断是否已经登录，获取用户信息
        $memberId = Yii::$app->user->id;
        //获取默认的简历id
        $defaultResumeToken = ResumeAttachment::getDefaultResumeToken($memberId);

        if (!empty($memberId)) {
            //获取用户附件简历列表
            $resumeAttachmentList = ResumeAttachment::getList($memberId);

            //判断用户当前的状态（是否完成简历前三步）
            $userStatus = Member::getUserResumeStatus($memberId);
            if ($userStatus == Member::USER_STATUS_COMPLETE_RESUME) {
                //如果简历已经完成了，获取简历完成度
                $resumeCompletePercent = BaseResume::getComplete($memberId);
                //已经完成，这时候把简历状态改成已完成
                $resumeStatus = Resume::STATUS_COMPLETE_BASE_INFO;
            }
        } else {
            $memberId = 0;
        }

        $getInfo = Yii::$app->request->get();

        // 限制一下
        if ($getInfo['pageSize'] > 100) {
            $getInfo['pageSize'] = 100;
        }
        $userEmail = '';
        if (!empty(Yii::$app->user->id)) {
            $getInfo['memberId'] = Yii::$app->user->id;
            //获取用户邮箱
            $userEmail = Member::findOneVal(['id' => $getInfo['memberId']], 'email');
        }

        $categoryJobList = CategoryJob::getPersonCategoryJobList();

        try {
            $data = Job::searchForList($getInfo);
        } catch (\Exception $e) {
            $data = [];
        }

        $jobList  = $data['list'] ?: [];
        $pageSize = $data['pageSize'] ?: 0;
        $totalNum = $data['totalNum'] ?: 0;

        //拿一下职位类型
        $jobType    = array_filter(explode('_', $getInfo['jobType']));
        $jobTypeTxt = BaseCategoryJob::getMultipleJobCategory($jobType);

        if (!empty($jobList)) {
            foreach ($jobList as &$job) {
                $job['jobNameTitle'] = str_replace('"', "'", $job['jobName']);
            }
        }

        $seoConfig = Yii::$app->params['seo']['jobList'];

        $this->setSeo([
            'title'       => $seoConfig['title'],
            'keywords'    => $seoConfig['keywords'],
            'description' => $seoConfig['description'],
        ]);

        return $this->render('index.html', [
            'cityList'              => $cityList,
            'companyNatureList'     => $companyNatureList,
            'companyTypeList'       => $companyTypeList,
            'majorList'             => $majorList,
            'welfareLabelList'      => $welfareLabelList,
            'educationRequire'      => $educationRequire,
            'tradeList'             => $tradeList,
            'jobNatureList'         => $jobNatureList,
            'experienceList'        => $experienceList,
            'wageRangeList'         => $wageRangeList,
            'jobTypeList'           => $jobTypeList,
            'companyScaleList'      => $companyScaleList,
            'jobList'               => $jobList,
            'releaseTimeList'       => $releaseTimeList,
            'resumeAttachmentList'  => $resumeAttachmentList,
            'resumeCompletePercent' => $resumeCompletePercent,
            'userStatus'            => $userStatus,
            'resumeStatus'          => $resumeStatus,
            'fullUrl'               => $fullUrl,
            'getInfo'               => $getInfo,
            'pageSize'              => $pageSize,
            'totalNum'              => $totalNum,
            'defaultResumeToken'    => $defaultResumeToken,
            'companyList'           => $companyList,
            'parentMajorId'         => $parentMajorId,
            'majorId'               => $parr['majorId'],
            'titleTypeList'         => $titleTypeList,
            'userEmail'             => $userEmail,
            'categoryJobList'       => $categoryJobList,
            'showcaseList'          => $showcaseList,
            'popover'               => $popover,
            'majorAllList'          => $majorAllList,
            'jobHotSearchList'      => $jobHotSearchList,
            'jobTypeTxt'            => $jobTypeTxt,
        ]);
    }

    /**
     * 获取详情页面信息
     */
    public function actionDetail()
    {
        $id = Yii::$app->request->get('id');
        if (empty($id)) {
            $this->notFound();
        }
        $memberId = Yii::$app->user->id;
        $resumeId = BaseMember::getMainId($memberId);
        if (Yii::$app->request->get('update') == 1) {
            $info = Job::getJobDetail($id, $memberId, true);
        } else {
            $info = Job::getJobDetail($id, $memberId);
        }
        $info['applyStatus']   = BaseJobApplyRecord::checkJobApplyStatus($resumeId, $id);
        $info['collectStatus'] = BaseJobCollect::getCollectStatus($id, $memberId);
        $info['userEmail']     = BaseMember::findOneVal(['id' => $memberId], 'email');

        if (!$info['jobName']) {
            $this->notFound();
        }

        if ($info['status'] == BaseJob::STATUS_DELETE || $info['status'] == BaseJob::STATUS_WAIT || $info['is_show'] == BaseJob::IS_SHOW_NO) {
            $this->notFound();
        }
        $info['isEmailApply'] = StringHelper::strToBool($info['isEmailApply']);
        //获取默认的简历id
        $defaultResumeToken = ResumeAttachment::getDefaultResumeToken($memberId);
        //设置默认值
        $resumeAttachmentList  = [];
        $resumeCompletePercent = 0;
        $info['resumeStatus']  = Resume::STATUS_UN_COMPLETE_BASE_INFO;

        $info['userStatus'] = Member::USER_STATUS_UN_LOGIN;
        $info['percent']    = 0;
        if (!empty($memberId)) {
            $resumeAttachmentList = ResumeAttachment::getList($memberId);

            //判断用户当前的状态（是否完成简历前三步）
            $info['userStatus'] = Member::getUserResumeStatus($memberId);
            if ($info['userStatus'] == Member::USER_STATUS_COMPLETE_RESUME) {
                //如果简历已经完成了，获取简历完成度
                $resumeCompletePercent = BaseResume::getComplete($memberId);
                $info['resumeStatus']  = Resume::STATUS_COMPLETE_BASE_INFO;
            }
            //获取用户简历完成度
            $info['percent'] = BaseResume::getComplete($memberId);
        }

        //获取推荐职位
        //        $recommendJobList = Job::getRecommendList(['pageSize' => Yii::$app->params['recommendJobCount']]);
        //        $recommendJobUrl  = BaseJob::getRecommendJobUrl();

        // 获取推荐职位开始
        $recommendService    = new RecommendService();
        $recommendSearchData = [
            'memberId' => $memberId ?: '',
            'jobId'    => $id,
            'limit'    => 8,
        ];
        $recommendList       = $recommendService->setData($recommendSearchData)
            ->getRecommendList();
        $recommendList0      = array_slice($recommendList, 0, 4);
        $recommendList1      = array_slice($recommendList, 4, 4);

        $recommendHtml = $this->renderPartial('/job/recommend.html', [
            'recommendList0' => $recommendList0,
            'recommendList1' => $recommendList1,
        ]);
        // 获取推荐职位结束

        $memberId = Yii::$app->user->id ?? 0;
        BaseJobClickLog::create($id, $memberId);
        //查看公告是否有附件需要显示
        $info['fileList'] = [];
        if (!empty($info['file_ids'])) {
            $file_ids = $info['file_ids'];
        } else {
            $file_ids = $info['announcement_file_ids'];
        }
        if (!empty($file_ids)) {
            $fileIdsArr = explode(',', $file_ids);
            $file_data  = BaseFile::getIdsList($fileIdsArr);
            $fileArr    = [];
            foreach ($file_data as &$value) {
                if (!empty($value['path'])) {
                    $item['path']   = FileHelper::getFullUrl($value['path']);
                    $item['name']   = $value['name'];
                    $item['suffix'] = FileHelper::getFileSuffixClassName($value['suffix']);
                    $item['id']     = $value['id'];
                    array_push($fileArr, $item);
                }
            }
            $info['fileList'] = $fileArr;
        }
        $seoConfig = Yii::$app->params['seo']['jobDetail'];
        $title     = str_replace([
            '【职位名称】',
            '【单位名称】',
        ], [
            $info['jobName'],
            $info['companyName'],
        ], $seoConfig['title']);

        $keywords = str_replace([
            '【职位名称】',
            '【单位名称】',
        ], [
            $info['jobName'],
            $info['companyName'],
        ], $seoConfig['keywords']);

        $description = str_replace([
            '【职位名称】',
            '【单位名称】',
        ], [
            $info['jobName'],
            $info['companyName'],
        ], $seoConfig['description']);

        $this->setSeo([
            'title'       => $title,
            'keywords'    => $keywords,
            'description' => $description,
        ]);

        $template = 'detail.html';

        // 这里找一下公告的模板?
        if ($info['announcementId']) {
            $templateId = Announcement::findOneVal(['id' => $info['announcementId']], 'template_id');
            if ($templateId == Announcement::TEMPLATE_DOUBLE_MEETING_ACTIVITY) {
                $template = 'doubleMeetingActivityDetail.html';
                //$info['applyTypeText'] = '站内报名';
            }
        }

        BaiduTimeFactor::create($info['refresh_time']);
        ToutiaoTimeFactor::create($info['refresh_time']);

        // 当前职位是否已经查看过

        $hasRecord = ResumeJobReportRecord::checkReportRecord($resumeId, $id);
        // 获取职位匹配度
        $matchInfo['hasRecord'] = $hasRecord;
        $matchInfo['reportUrl'] = Job::getReportUrl($resumeId, $id);
        if ($hasRecord) {
            //$jobApplyTotal     = JobReport::getJobApplyTotal($id);
            $matchInfo['data'] = JobReport::getJopMatchInfo($id, $resumeId, MatchCompleteService::PLATFORM_WEB);
        } else {
            $matchInfo['data'] = [];
        }

        // 特殊逻辑
        $info = (new JobInformationService())->handelJobDetail($info);

        return $this->render($template, [
            'info'                  => $info,
            'resumeAttachmentList'  => $resumeAttachmentList,
            'resumeCompletePercent' => $resumeCompletePercent,
            'defaultResumeToken'    => $defaultResumeToken,
            //            'recommendJobUrl'       => $recommendJobUrl,
            'matchInfo'             => $matchInfo,
            'shareUrlCode'          => BaseJob::getDetailMiniCode($id),
            'recommendHtml'         => $recommendHtml,
        ]);
    }

    /*================== 新职位中心相关接口开始 =============================*/

    // 职位中心重构获取的几个接口(获取全部广告位)
    public function actionHomeShowcaseList()
    {
        // 头部
        // $showcaseList = HomePosition::getJobShowcase();
        $showcaseList2024 = HomePosition::getJobShowcase2024();
        $search           = HomePosition::getNewJobSearch();
        $aside            = HomePosition::getNewJobAside();

        return $this->success([
            // 之前这里是轮播的，后面改成了单个了（禅道需求736）
            // 'recommend' => $showcaseList,
            'head'   => $showcaseList2024,
            'search' => $search,
            'aside'  => $aside,
        ]);
    }

    /**
     * 获取参数(字典)
     * "jobTypeList": [],
     * "hotAreaList": [],
     * "areaList": [],
     * "companyTypeList": [],
     * "majorList": [],
     * "educationList": [],
     * "companyNatureList": [],
     * "industryList": [],
     * "releaseList": [],
     * "titleList": [],
     * "natureList": []
     */
    public function actionHomeParams()
    {
        // 这里加整体缓存
        $app = CommonSearchApplication::getInstance();

        return $this->success($app->getPcJobListParams());
    }

    public function actionHomeParamsText()
    {
        // 跟进传过来的参数获取对应的文案 jobType
        // areaId
        // majorId
        $getInfo = Yii::$app->request->get();
        $areaId  = explode(',', $getInfo['areaId']);
        $majorId = explode(',', $getInfo['majorId']);
        $jobType = explode(',', $getInfo['jobType']);

        return $this->success([
            'areaText'    => BaseArea::find()
                ->select('name')
                ->where(['id' => $areaId])
                ->asArray()
                ->column(),
            'majorText'   => Major::find()
                ->select('name')
                ->where(['id' => $majorId])
                ->asArray()
                ->column(),
            'jobTypeText' => BaseCategoryJob::find()
                ->select('name')
                ->where(['id' => $jobType])
                ->asArray()
                ->column(),
        ]);
    }

    public function actionHomeList()
    {
        $getInfo = Yii::$app->request->get();

        // 这里主要是做兼容新
        if ($getInfo['currentPage']) {
            $getInfo['p'] = $getInfo['currentPage'];
        }

        if ($getInfo['pageSize'] > 100) {
            $getInfo['pageSize'] = 100;
        }

        //        $userEmail = '';
        $memberId = 0;
        if (!empty(Yii::$app->user->id)) {
            $memberId            = Yii::$app->user->id;
            $getInfo['memberId'] = $memberId;
        }

        // 最多到100页
        if ($getInfo['p'] > 100) {
            $getInfo['p'] = 100;
        }
        // 这里需要对有编制的一些查询进行过滤,避免非会员使用了这个参数进去
        // 校验是否拥有编制查询权益
        $resumeId = Resume::findOneVal(['member_id' => $memberId], 'id');
        if (BaseResumeEquity::checkEquity($resumeId, BaseResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
            unset($getInfo['isEstablishment'], $getInfo['applyHeat']);
        }

        try {
            // 内存开到1g
            ini_set('memory_limit', '1024M');

            $app = CommonSearchApplication::getInstance();

            // 全新限制逻辑，未登录请夸下，getInfo = []
            // if (Yii::$app->user->isGuest) {
            //     $getInfo = [];
            // }

            $data = $app->pcJobListSearch($getInfo);

            // 又是一个兼容性问题,如果list没有,并且page是第一页,那么就是没有数据,totalNum就直接是0
            if (empty($data['list']) && ($getInfo['p'] == 1 || !$getInfo['p'])) {
                $data['totalNum'] = 0;
            }
            //获取职位列表广告插入位置
            if ($getInfo['p'] == 1 || !$getInfo['p']) {
                // $data['showcaseInfo'] = BaseJob::getListShowcaseInfo(count($data['list']), Yii::$app->user->id,
                //     BaseJob::VIP_SHOWCASE_POSITION_TYPE_PC_JOB_LIST);

                $data['showcaseInfo'] = BaseJob::getBoshihouBanner(count($data['list']), Yii::$app->user->id,
                    BaseJob::VIP_SHOWCASE_POSITION_TYPE_PC_JOB_LIST);
                //
            }
        } catch (\Exception $e) {
            $data = [];
        }

        // 这里再做一次字符的格式化,因为不太规范
        foreach ($data['list'] as $key => &$value) {
            // $value['jobName'] = str_replace(' ', '', $value['jobName']);

            foreach ($value as &$item) {
                if (is_int($item)) {
                    $item = (string)$item;
                }
                if ($item == 'true') {
                    $item = '1';
                }

                if ($item == 'false') {
                    $item = '2';
                }

                // 因为现在版本未完成,所以需要把官方标签去掉
                $value['isCooperation'] = '2';
            }
            //每一个职位里面塞入一个直聊按钮的显示
            $chatButtonInfo          = CommonRule::checkButtonAuth($value['jobId'], $memberId);
            $value['chatButtonType'] = $chatButtonInfo['buttonType'];
            $value['chatButtonName'] = $chatButtonInfo['buttonName'];
        }

        return $this->success($data);
    }

    public function actionHomeUserInfo()
    {
        // 这里是看是否登录(求职者)
        $isLogin            = false;
        $hasJobIntention    = false;
        $hasJobSubscription = false;
        // 是否vip
        $isVip = false;

        if (!empty(Yii::$app->user->id)) {
            $user = BaseMember::getLoginInfo();

            if ($user['type'] == BaseMember::TYPE_PERSON) {
                $isLogin = true;

                $resumeId = BaseResume::findOneVal(['member_id' => Yii::$app->user->id], 'id');
                // 找是否有求职意向
                if (BaseResumeIntention::find()
                    ->where([
                        'resume_id' => $resumeId,
                        'status'    => BaseResumeIntention::STATUS_ACTIVE,
                    ])
                    ->exists()) {
                    // 这里再判断这个用户是否已经40+的完善度了
                    if ($user['resumePercent'] >= 40) {
                        $hasJobIntention = true;
                    }
                }
                // 找是否有订阅
                if (BaseJobSubscribe::find()
                    ->where([
                        'resume_id' => $resumeId,
                        'status'    => BaseJobSubscribe::STATUS_ACTIVE,
                    ])
                    ->exists()) {
                    $hasJobSubscription = true;
                }
                // 是否vip
                if (BaseResume::find()
                    ->where([
                        'id'       => $resumeId,
                        'vip_type' => BaseResume::VIP_TYPE_ACTIVE,
                    ])
                    ->exists()) {
                    $isVip = true;
                }
            }

            //判断是否要显示绑定二维码
            $wxBindInfo = BaseResumeWxBind::findOne(['resume_id' => $resumeId]);
            if (empty($wxBindInfo['unionid'])) {
                try {
                    $bindCodeUrl = WxPublic::getInstance(BaseMember::TYPE_PERSON)
                                       ->createBindQrCode($resumeId)['url'];
                } catch (\Exception $e) {
                    $bindCodeUrl = '';
                }
            }
        }

        return $this->success([
            'bindCodeUrl'        => $bindCodeUrl ?: '',
            'isLogin'            => $isLogin,
            'hasJobIntention'    => $hasJobIntention,
            'hasJobSubscription' => $hasJobSubscription,
            'isVip'              => $isVip,
        ]);
    }

    /*================== 新职位中心相关接口结束 =============================*/

    /**
     * 职位报告详情页
     */
    public function actionReport()
    {
        $jobId            = Yii::$app->request->get('id');
        $memberId         = Yii::$app->user->id;
        $resumeId         = Resume::findOneVal(['member_id' => $memberId], 'id');
        $token            = Yii::$app->request->get('token', 0);
        $url              = Job::getDetailUrl($jobId);
        $permissionDenied = "<script>alert('您暂无权限访问该页面');window.location.href='$url';</script>";

        if (empty($resumeId) || empty($token)) {
            exit($permissionDenied);
        }

        $jobRow = Job::find()
            ->select('status,is_show')
            ->where([
                'id' => $jobId,
            ])
            ->asArray()
            ->one();

        if (empty($jobRow)) {
            exit($permissionDenied);
        }
        if ($jobRow['status'] == BaseJob::STATUS_DELETE) {
            exit($permissionDenied);
        }

        if ($jobRow['status'] == BaseJob::STATUS_WAIT) {
            exit($permissionDenied);
        }

        // 是否查看过
        $hasRecord = ResumeJobReportRecord::checkReportRecord($resumeId, $jobId, $token);
        if (!$hasRecord) {
            exit($permissionDenied);
        }

        return $this->render('reportLoading.html', [
            'jobId' => $jobId,
            'token' => $token,
        ]);
    }

    public function actionReportDetail()
    {
        $jobId            = Yii::$app->request->get('jobId');
        $token            = Yii::$app->request->get('token', 0);
        $memberId         = Yii::$app->user->id;
        $resumeId         = Resume::findOneVal(['member_id' => $memberId], 'id');
        $token            = Yii::$app->request->get('token', 0);
        $url              = Job::getDetailUrl($jobId);
        $permissionDenied = "<script>alert('您暂无权限访问该页面');window.location.href='$url';</script>";
        if (empty($resumeId) || empty($token)) {
            exit($permissionDenied);
        }

        $jobRow = Job::find()
            ->select('status,is_show')
            ->where([
                'id' => $jobId,
            ])
            ->asArray()
            ->one();

        if (empty($jobRow)) {
            exit($permissionDenied);
        }
        if ($jobRow['status'] == BaseJob::STATUS_DELETE) {
            exit($permissionDenied);
        }

        if ($jobRow['status'] == BaseJob::STATUS_WAIT) {
            exit($permissionDenied);
        }
        // exit('职位报告');
        $data = BaseJobReport::getReport($jobId, $resumeId, MatchCompleteService::PLATFORM_WEB);

        return $this->success(['html' => $this->renderPartial('report.html', ['data' => $data])]);
    }
}
