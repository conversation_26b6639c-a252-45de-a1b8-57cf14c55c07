<?php

namespace frontendPc\controllers;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseShowcase;
use common\helpers\IpHelper;
use common\libs\BaiduTimeFactor;
use common\libs\Cache;
use common\libs\ToutiaoTimeFactor;
use common\service\search\CommonSearchApplication;
use common\service\search\CompanyListService;
use common\service\specialNeedService\CompanyInformationService;
use frontendPc\models\Area;
use frontendPc\models\Company;
use frontendPc\models\Dictionary;
use frontendPc\models\HomePosition;
use frontendPc\models\Member;
use frontendPc\models\Trade;
use frontendPc\models\WelfareLabel;
use Yii;
use yii\console\Response;

class CompanyController extends BaseFrontendPcController
{

    private function handleUrl($parr, $key, $list)
    {
        if ($key == 'areaId' || $key == 'welfareLabelId' || $key == 'companyNature' || $key == 'companyType') {
            $urlCityId    = $parr[$key];
            $urlCityIdArr = explode('_', $urlCityId);
            foreach ($list as $item_key => &$item) {
                if (is_array($item)) {
                    $cureentCityIdArr = $urlCityIdArr;
                    // 拿到城市的参数
                    if (in_array($item['id'], $urlCityIdArr)) {
                        $arr           = $parr;
                        $item['class'] = 'active';
                        //如果重复点击，去除active标签
                        $activeKey = array_search($item['id'], $urlCityIdArr);
                        array_splice($cureentCityIdArr, $activeKey, 1);
                        $arr[$key] = implode('_', $cureentCityIdArr);
                        unset($arr['page']);
                        $newParams = http_build_query($arr);
                        // 把最左边的_去掉,
                        $newParams   = ltrim($newParams, '_');
                        $item['url'] = '/company?' . $newParams;
                    } else {
                        $arr       = $parr;
                        $arr[$key] = ltrim($arr[$key] . '_' . $item['id'], '_');
                        unset($arr['page']);
                        $newParams   = http_build_query($arr);
                        $item['url'] = '/company?' . $newParams;
                    }
                } else {
                    //companyNature+companyType
                    $arr         = [];
                    $arr['name'] = $item;
                    $arr['id']   = $item_key;
                    if (in_array($item_key, $urlCityIdArr)) {
                        $company_arr  = $parr;
                        $arr['class'] = 'active';
                        //如果重复点击，去除active标签
                        $activeKey  = array_search($item_key, $urlCityIdArr);
                        $split_parr = $urlCityIdArr;
                        array_splice($split_parr, $activeKey, 1);
                        $company_arr[$key] = implode('_', $split_parr);
                        unset($company_arr['page']);
                        $newParams = http_build_query($company_arr);
                        // 把最左边的_去掉,
                        $newParams  = ltrim($newParams, '_');
                        $arr['url'] = '/company?' . $newParams;
                    } else {
                        $company_arr       = $parr;
                        $company_arr[$key] = ltrim($company_arr[$key] . '_' . $item_key, '_');
                        unset($company_arr['page']);
                        $newParams  = http_build_query($company_arr);
                        $arr['url'] = '/company?' . $newParams;
                    }

                    $item = $arr;
                }
            }
        } else {
            foreach ($list as $k => &$item) {
                if (is_array($item)) {
                    $urlId = $parr[$key];
                    if ($item['id'] == $urlId) {
                        $arr           = $parr;
                        $item['class'] = 'active';
                        $arr[$key]     = '';
                        unset($arr['page']);
                        $newParams   = http_build_query($arr);
                        $item['url'] = '/company?' . $newParams;
                    } else {
                        $arr       = $parr;
                        $arr[$key] = $item['id'];
                        unset($arr['page']);
                        $newParams   = http_build_query($arr);
                        $item['url'] = '/company?' . $newParams;
                    }
                } else {
                    $arr         = [];
                    $arr['name'] = $item;
                    $arr['id']   = $k;
                    $urlId       = $parr[$key];
                    if ($k == $urlId) {
                        $newArr       = $parr;
                        $newArr[$key] = '';
                        $arr['class'] = 'active';
                        unset($newArr['page']);
                        $newParams  = http_build_query($newArr);
                        $arr['url'] = '/company?' . $newParams;
                    } else {
                        $newParr       = $parr;
                        $newParr[$key] = $k;
                        unset($newParr['page']);
                        $newParams  = http_build_query($newParr);
                        $arr['url'] = '/company?' . $newParams;
                    }
                    $item = $arr;
                }
            }
        }

        return $list;
    }

    public function actionOldIndex()
    {
        $ua = Yii::$app->request->getUserAgent();
        if ($ua === 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 6P Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2526.83 Mobile Safari/537.36; 360Spider') {
            Cache::lPush('TMP:HOME_UA', json_encode([
                'ua'     => $ua,
                'ip'     => IpHelper::getIp(),
                'userId' => Yii::$app->user->id,
                'time'   => CUR_DATETIME,
            ]));
            exit('系统维护中');
            // 返回一个空的页面
            // 入队
        }

        if (!IpHelper::isChina()) {
            // 有areaId 有companyType

            $areaId      = Yii::$app->request->get('areaId');
            $companyType = Yii::$app->request->get('companyType');

            $isLogin = !Yii::$app->user->isGuest;
            if ($areaId && $companyType && !$isLogin) {
                $url = '/member/person/login';
                exit("请先<a href='{$url}'>登录</a>");
            }
        }

        $ip = IpHelper::getIp();
        // if (in_array($ip, ['**************'])) {
        //     try {
        //         // UA包含这个,就直接ban
        //         if (strstr($ua,
        //             'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/11')) {
        //             exit('系统维护中');
        //         }
        //     } catch (\Exception $e) {
        //         // do nothing
        //     }
        // }
        $fullUrl = Yii::$app->request->getHostInfo() . Yii::$app->request->url;
        $params  = parse_url($fullUrl);
        if ($params['query']) {
            parse_str($params['query'], $parr);//获取URL中的参数，并赋值到$parr数组中
        }

        // 添加登录限制
        // if (Yii::$app->user->isGuest) {
        //     if ($params['query']) {
        //         // 跳转到登录页
        //         return $this->redirect('/member/person/login');
        //     }
        // }

        if ($parr['welfareLabelId']) {
            $this->notPermissions();
        }

        if (!empty($parr['industryId'])) {
            $parentIndustryId = Trade::getCurrentSearchParentId($parr['industryId']);
        } else {
            $parentIndustryId = 0;
        }
        //获取广告位的公司列表
        $showcaseList = HomePosition::getJobShowcase2024();
        //获取单位地点
        $cityList = $this->handleUrl($parr, 'areaId', Area::getSearchHotList());
        //获取单位性质
        $companyNatureList = $this->handleUrl($parr, 'companyNature', Dictionary::getCompanyNatureList());
        //获取单位类型
        $companyTypeList = $this->handleUrl($parr, 'companyType', Dictionary::getCompanyTypeList());
        //获取行业类别
        $industryList = $this->handleUrl($parr, 'industryId', Trade::getTradeList());
        //单位规模
        $companyScaleList = $this->handleUrl($parr, 'companyScaleType', Dictionary::getCompanyScaleList());
        //职位福利
        $welfareLabelList = $this->handleUrl($parr, 'welfareLabelId', WelfareLabel::getWelfareLabelList());

        //这里取单位和职位的热门搜索广告位广告
        $companyNumber        = 'danweiliebiao_remensousuo';
        $companyHotId         = BaseHomePosition::findOneVal(['number' => $companyNumber], 'id');
        $companyHotSearchList = BaseShowcase::getByPositionConfig($companyHotId, $companyNumber);
        foreach ($companyHotSearchList as $k => $value) {
            if (strlen($value['real_target_link']) < 1) {
                $companyHotSearchList[$k]['url'] = "/company?page=1&keyword=" . $value['title'];
            }
        }

        $getInfo = $parr;
        // 限制一下
        if ($getInfo['pageSize'] > 100) {
            $getInfo['pageSize'] = 100;
        }
        if (!empty(Yii::$app->user->id)) {
            $isLogin             = Member::IS_LOGIN_YES;
            $getInfo['memberId'] = Yii::$app->user->id;
        } else {
            $isLogin = Member::IS_LOGIN_NO;
        }
        try {
            if ($ip === '**************') {
                $data = Company::searchForList($getInfo);
            } else {
                // 暂时只要一个参数和分页进来，其他都不要？
                $params = [
                    'memberId'         => $getInfo['memberId'],
                    'page'             => $getInfo['page'],
                    'areaId'           => $getInfo['areaId'],
                    'companyType'      => $getInfo['companyType'],
                    'companyNature'    => $getInfo['companyNature'],
                    'companyScaleType' => $getInfo['companyScaleType'],
                    'industryId'       => $getInfo['industryId'],
                    'keyword'          => $getInfo['keyword'],
                    'sort'             => $getInfo['sort'],
                ];
                // 非登录全部去掉查询
                // if (Yii::$app->user->isGuest) {
                //     $params = [];
                // }
                $data = Company::searchForList($params);
            }
        } catch (\Exception $e) {
            $data = [];
        }

        $companyList = $data['list'] ?: [];
        $pageSize    = $data['pageSize'] ?: 0;
        $totalNum    = $data['totalNum'] ?: 0;

        $seoConfig = Yii::$app->params['seo']['companyList'];
        $this->setSeo([
            'title'       => $seoConfig['title'],
            'keywords'    => $seoConfig['keyword'],
            'description' => $seoConfig['description'],
        ]);

        return $this->render('list.html', [
            'cityList'             => $cityList,
            'companyNatureList'    => $companyNatureList,
            'companyTypeList'      => $companyTypeList,
            'industryList'         => $industryList,
            'industryId'           => $parr['industryId'],
            'parentIndustryId'     => $parentIndustryId,
            'companyScaleList'     => $companyScaleList,
            'welfareLabelList'     => $welfareLabelList,
            'fullUrl'              => $fullUrl,
            'getInfo'              => $getInfo,
            'companyList'          => $companyList,
            'pageSize'             => $pageSize,
            'totalNum'             => $totalNum,
            'isLogin'              => $isLogin,
            'showcaseList'         => $showcaseList,
            'companyHotSearchList' => $companyHotSearchList,
        ]);
    }

    public function actionIndex()
    {
        $seoConfig = Yii::$app->params['seo']['companyList'];
        $this->setSeo([
            'title'       => $seoConfig['title'],
            'keywords'    => $seoConfig['keyword'],
            'description' => $seoConfig['description'],
        ]);

        return $this->render('list2025.html');
    }

    /**
     * 获取单位详情页面数据
     * @throws \Exception
     */
    //    public function actionDetail()
    //    {
    //        $id       = Yii::$app->request->get('id');
    //        $memberId = Yii::$app->user->id;
    //
    //        $isCooperation = BaseCompany::checkIsCooperation($id);
    //        if (!$isCooperation) {
    //            return $this->actionUnCooperationDetail();
    //        }
    //        $info       = Company::getDetail($id, $memberId);
    //        $updateTime = Company::getDetailUpdateTime($id);
    //        if (!$info) {
    //            $this->notFound();
    //        }
    //
    //        // 设置好seo的信息
    //        $seoConfig   = Yii::$app->params['seo']['companyDetail'];
    //        $description = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
    //        $keywords    = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
    //        $this->setSeo([
    //            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
    //            'keywords'    => $keywords,
    //            'description' => $description,
    //        ]);
    //
    //        $welfareLabelViewAmount = Yii::$app->params['welfareLabelViewAmount'];
    //
    //        // 点击量
    //        Company::click($id);
    //
    //        BaiduTimeFactor::create($info['add_time'], $updateTime);
    //        ToutiaoTimeFactor::create($info['add_time'], $updateTime);
    //
    //        return $this->render('companyDetail.html', [
    //            'info'                   => $info,
    //            'welfareLabelViewAmount' => $welfareLabelViewAmount,
    //        ]);
    //    }

    public function actionDetail()
    {
        $id            = Yii::$app->request->get('id');
        $isCooperation = BaseCompany::checkIsCooperation($id);
        if ($isCooperation) {
            return $this->actionCooperationDetail($id);
        }

        return $this->actionUnCooperationDetail($id);
    }

    /**
     * 合作单位详情介绍页面
     * @throws \Exception
     */
    public function actionCooperationDetail($id)
    {
        $info                 = Company::getDetailIntroduceInfo($id);
        $info['activityList'] = BaseCompany::getActivityList(['companyId' => $id]);
        $updateTime           = Company::getDetailUpdateTime($id);
        if (!$info) {
            $this->notFound();
        }

        // 设置好seo的信息
        $seoConfig       = Yii::$app->params['seo']['cooperationCompanyDetail'];
        $descriptionName = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $descriptionType = str_replace('【单位类型】', $info['type'], $descriptionName);
        $description     = str_replace('【单位性质】', $info['nature'], $descriptionType);
        $keywords        = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);

        // 点击量
        Company::click($id);

        // https://lanhuapp.com/docs/#/d/d67d100a8cc57b0d878ce4532?from=project&type=independent
        //        BaiduTimeFactor::create($info['add_time'], $updateTime);
        //        ToutiaoTimeFactor::create($info['add_time'], $updateTime);
        BaseCompany::getCompanyDetailTimeJs($info['add_time'], $id);
        $detailJobUrl          = '/company/detail/' . $id . '_j.html';
        $detailAnnouncementUrl = '/company/detail/' . $id . '_a.html';

        $info = (new CompanyInformationService())->handelCompanyDetail($info, $id);

        return $this->render('companyDetail.html', [
            'info'                  => $info,
            'detailJobUrl'          => $detailJobUrl,
            'detailAnnouncementUrl' => $detailAnnouncementUrl,
        ]);
    }

    /**
     * 非合作单位详情
     * @return string
     * @throws \Exception
     */
    public function actionUnCooperationDetail($id)
    {
        $memberId   = Yii::$app->user->id;
        $info       = Company::getUnCooperationDetailInfo($id);
        $updateTime = Company::getDetailUpdateTime($id);
        if (!$info) {
            $this->notFound();
        }
        $info['isCollect'] = BaseCompanyCollect::getCollectStatus($id, $memberId);
        //查询投递状态
        if (!empty($memberId)) {
            //获取简历信息
            $resumeInfo = BaseResume::findOne(['member_id' => $memberId]);
            foreach ($info['jobList'] as &$item) {
                $item['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resumeInfo['id'], $item['jobId']);
            }
        }

        // 设置好seo的信息
        $seoConfig   = Yii::$app->params['seo']['companyDetail'];
        $description = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $keywords    = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);

        $welfareLabelViewAmount = Yii::$app->params['welfareLabelViewAmount'];

        // 点击量
        Company::click($id);

        // 修改时间因子：https://lanhuapp.com/docs/#/d/d67d100a8cc57b0d878ce4532?from=project&type=independent
        BaseCompany::getCompanyDetailTimeJs($info['add_time'], $id);
        //        BaiduTimeFactor::create($info['add_time'], $updateTime);
        //        ToutiaoTimeFactor::create($info['add_time'], $updateTime);

        return $this->render('unCooperationDetail.html', [
            'info'                   => $info,
            'welfareLabelViewAmount' => $welfareLabelViewAmount,
        ]);
    }

    public function actionDetailAnnouncementList()
    {
        $id                   = Yii::$app->request->get('id');
        $info                 = Company::getDetailAnnouncementListInfo($id);
        $updateTime           = Company::getDetailUpdateTime($id);
        $info['activityList'] = BaseCompany::getActivityList(['companyId' => $id]);
        if (!$info) {
            $this->notFound();
        }
        $isCooperation = BaseCompany::checkIsCooperation($id);
        if (!$isCooperation) {
            $this->notFound();
        }

        // 设置好seo的信息
        $seoConfig   = Yii::$app->params['seo']['companyDetailAnnouncement'];
        $description = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $keywords    = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);

        // 点击量
        Company::click($id);

        // BaiduTimeFactor::create($info['add_time'], $updateTime);
        // ToutiaoTimeFactor::create($info['add_time'], $updateTime);
        BaseCompany::getCompanyDetailTimeJs($info['add_time'], $id);

        $info = (new CompanyInformationService())->handelCompanyAnnouncementList($info, $id);

        return $this->render('companyDetailNoticeList.html', [
            'info' => $info,
        ]);
    }

    public function actionDetailJobList()
    {
        $id                   = Yii::$app->request->get('id');
        $memberId             = Yii::$app->user->id;
        $info                 = Company::getDetailJobListInfo($id);
        $info['activityList'] = BaseCompany::getActivityList(['companyId' => $id]);
        //获取简历信息
        $resumeId = BaseMember::getMainId($memberId);
        foreach ($info['jobList'] as &$item) {
            if ($item['is_cooperation'] == BaseCompany::COOPERATIVE_UNIT_NO) {
                $item['userEmail'] = BaseMember::findOneVal(['id' => $memberId], 'email');
            }
            $item['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resumeId, $item['jobId']);
        }
        $updateTime = Company::getDetailUpdateTime($id);
        if (!$info) {
            $this->notFound();
        }
        $isCooperation = BaseCompany::checkIsCooperation($id);
        if (!$isCooperation) {
            $this->notFound();
        }

        // 设置好seo的信息
        $seoConfig   = Yii::$app->params['seo']['companyDetailJob'];
        $description = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $keywords    = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);

        // 点击量
        Company::click($id);

        // BaiduTimeFactor::create($info['add_time'], $updateTime);
        // ToutiaoTimeFactor::create($info['add_time'], $updateTime);
        BaseCompany::getCompanyDetailTimeJs($info['add_time'], $id);

        $info = (new CompanyInformationService())->handelCompanyJobList($info, $id);

        return $this->render('companyDetailJobList.html', [
            'info' => $info,
        ]);
    }

    /**
     * 单位-引才活动
     */
    public function actionDetailActivityList()
    {
        $id   = Yii::$app->request->get('id');
        $info = BaseCompany::getDetailActivityListInfo($id);
        if (!$info['activityList']['page']['count']) {
            $this->redirect('/company/detail/' . $id . '.html', 301);
        }
        //获取简历信息
        $updateTime = Company::getDetailUpdateTime($id);
        if (!$info) {
            $this->notFound();
        }
        $isCooperation = BaseCompany::checkIsCooperation($id);
        if (!$isCooperation) {
            $this->notFound();
        }

        // 设置好seo的信息
        $seoConfig   = Yii::$app->params['seo']['companyDetailActivity'];
        $description = str_replace('【单位名称】', $info['companyName'], $seoConfig['description']);
        $keywords    = str_replace('【单位名称】', $info['companyName'], $seoConfig['keywords']);
        $this->setSeo([
            'title'       => str_replace('【单位名称】', $info['companyName'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);

        // 点击量
        Company::click($id);

        BaiduTimeFactor::create($info['add_time'], $updateTime);
        ToutiaoTimeFactor::create($info['add_time'], $updateTime);

        $info = (new CompanyInformationService())->handelCompanyJobList($info, $id);

        return $this->render('companyDetailActivityList.html', [
            'info' => $info,
        ]);
    }

    public function actionGetDetailMiniCode()
    {
        $id = Yii::$app->request->get('id');

        if (!$id) {
            return $this->fail('非法操作');
        }

        return $this->success(['url' => Company::getDetailMiniCode($id)]);
    }

    /**
     * 获取用户信息接口
     * @return Response|\yii\web\Response
     */
    public function actionHomeUserInfo()
    {
        try {
            $isLogin = !Yii::$app->user->isGuest;
            $isVip   = false;

            if ($isLogin) {
                // $loginInfo = BaseMember::getLoginInfo();
                // 这里可以根据实际业务逻辑判断VIP状态
                // $isVip = $this->checkVipStatus($loginInfo);
            }

            return $this->success([
                'isLogin' => $isLogin,
                'isVip'   => $isVip,
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取配置数据接口
     * @return Response|\yii\web\Response
     */
    public function actionConfig()
    {
        try {
            // 使用统一的配置参数服务，参考JobController的模式
            $app        = CommonSearchApplication::getInstance();
            $configData = $app->getPcCompanyListParams();

            return $this->success([
                'industryList'      => $configData['industryList'],
                'areaList'          => $configData['hotAreaList'],
                'companyNatureList' => $configData['companyNatureList'],
                'companyTypeList'   => $configData['companyTypeList'],
                'companyScaleList'  => $configData['companyScaleList'],
                'welfareList'       => $configData['welfareList'],
                'hotSearchList'     => $configData['hotSearchList'],
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取公司列表接口
     * @return Response|\yii\web\Response
     */
    public function actionList()
    {
        try {
            $request = Yii::$app->request->get();

            // 处理参数名映射
            // $this->processRequestParamMapping($request);

            // 添加用户ID用于收藏状态判断
            if (!Yii::$app->user->isGuest) {
                $request['memberId'] = Yii::$app->user->id;
            }

            if ($request['page'] > 40) {
                // 如果页数大于等于40，直接返回空数据
                return $this->success([]);
            }

            // 使用现有的公司列表服务
            $searchService = new CompanyListService();
            $data          = $searchService->run($request, 6); // TYPE_PC_COMPANY_LIST = 6

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取推荐数据接口
     * @return Response|\yii\web\Response
     */
    public function actionHomeShowcaseList()
    {
        try {
            // 获取推荐单位数据，使用BaseShowcase的静态方法
            $recommendData = HomePosition::getJobShowcase2024();

            return $this->success([
                'recommend' => $recommendData,
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}
