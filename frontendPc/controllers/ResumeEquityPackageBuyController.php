<?php

namespace frontendPc\controllers;

// 专门做了一个控制器,用于给求职者下单的
class ResumeEquityPackageBuyController extends BaseFrontendPcController
{
    public $layout = 'resume_equity_package_buy';
    public $params = [];
    public $key;
    public $config;

    const RESUME_VIP     = 'vip';
    const RESUME_INSIGHT = 'insight';

    // 配置文件,这里的key需要和静态项目里面的文件夹保持一致,方便查找
    const CONFIG = [
        self::RESUME_VIP     => [],
        self::RESUME_INSIGHT => [],
    ];

    public function actionIndex()
    {
        $key = \Yii::$app->request->get('key');
        if (!$key) {
            // 404
            $this->notFound();
        }

        $this->key    = $key;
        $this->config = self::CONFIG[$key];
        if (!isset($this->config)) {
            // 404
            $this->notFound();
        }

        // 这里就渲染页面了

        return $this->render('index.html');
    }

}
