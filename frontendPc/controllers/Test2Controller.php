<?php

namespace frontendPc\controllers;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseJob;
use common\base\models\BaseAnnouncementReport;
use common\base\models\BaseJobReport;
use common\base\models\BaseResume;

class Test2Controller extends BaseFrontendPcController
{
    public function actionAnnouncement()
    {
        $id    = \Yii::$app->request->get('id');
        if (empty($id)) {
            return $this->fail('公告id 不能为空');
        }
        if (empty(BaseAnnouncement::findOne($id))) {
            return $this->fail('公告 不存在');
        }
        return $this->success(BaseAnnouncementReport::getReport($id));
    }

    public function actionJob()
    {
        $id    = \Yii::$app->request->get('id');
        if (empty($id)) {
            return $this->fail('职位id 不能为空');
        }
        if (empty(BaseJob::findOne($id))) {
            return $this->fail('职位 不存在');
        }
        $memberId = \Yii::$app->user->id;
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        if (empty($resumeId)) {
            return $this->fail('请先登录求职者端');
        }
        return $this->success(BaseJobReport::getReport($id, $resumeId, 1));
    }
}
