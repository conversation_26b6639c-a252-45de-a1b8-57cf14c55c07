<?php

namespace frontendPc\controllers;

use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomeColumnDictionaryRelationship;
use common\base\models\BaseHomePosition;
use common\base\models\BaseMember;
use common\base\models\BaseShowcase;
use common\helpers\IpHelper;
use common\helpers\UrlHelper;
use common\libs\BaiduTimeFactor;
use common\libs\Cache;
use common\libs\HomeColumnCache;
use common\libs\ToutiaoTimeFactor;
use common\service\column\RecommendJobService;
use common\service\search\CommonSearchApplication;
use Exception;
use frontendPc\models\Announcement;
use frontendPc\models\DailyAnnouncementSummary;
use frontendPc\models\HomeColumn;
use frontendPc\models\HomePosition;
use frontendPc\models\News;
use frontendPc\models\Resume;
use frontendPc\models\ResumeEquity;
use frontendPc\models\ResumeEquitySetting;
use Yii;

class HomeController extends BaseFrontendPcController
{

    public function actionIndex()
    {
        $ua = Yii::$app->request->getUserAgent();
        // 这里ui从22年11月29日晚上开始攻击我们首页
        if ($ua == 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36)') {
            $this->layout = false;
            // 返回一个空的页面
            // 入队
            Cache::lPush('TMP:HOME_UA', json_encode([
                'ua'     => $ua,
                'ip'     => IpHelper::getIp(),
                'userId' => Yii::$app->user->id,
                'time'   => CUR_DATETIME,
            ]));

            return $this->render('bad.html', ['message' => '维护中']);
        }

        if ($ua == 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.0)') {
            $this->layout = false;
            // 返回一个空的页面
            // 入队
            Cache::lPush('TMP:HOME_UA', json_encode([
                'ua'     => $ua,
                'ip'     => IpHelper::getIp(),
                'userId' => Yii::$app->user->id,
                'time'   => CUR_DATETIME,
            ]));

            return $this->render('bad.html', ['message' => '维护中']);
        }

        $cacheKey = HomeColumnCache::getKey(HomeColumnCache::TYPE_PC_HOME);

        $this->layout = 'column_main';

        $isUpdateCache = false;
        if (Yii::$app->request->get(HomeColumnCache::CACHE_URL_KEY)) {
            $isUpdateCache = true;
        }

        $rs = Cache::get($cacheKey);
        if ($rs && !$isUpdateCache) {
            $data = json_decode($rs, true);
        } else {
            // 获取各页面数据
            // 这里加载各个页面
            $positionList = HomePosition::getAllShowcase();
            $allHead      = HomePosition::pcHomeHead();
            $daily        = HomePosition::pcHomeDaily();
            $rolling      = HomePosition::pcHomeRolling();
            $brandCompany = HomePosition::pcHomeBrandCompany();
            $focus        = HomePosition::pcHomeFocus();
            $recommend    = HomePosition::pcHomeRecommend();
            $top          = HomePosition::pcHomeTop();
            $hotTag       = HomePosition::getHotTag();
            $jobHot       = HomePosition::pcHomeJobHot();
            $hrDynamic    = HomePosition::pcHomeHrDynamic();
            $newest       = HomePosition::pcHomeNewest(count($top));
            $hotNews      = HomePosition::pcHomeHotNews();

            $remainHead = array_slice($allHead, 0, 5);
            $c4Head     = array_slice($allHead, 5, 27);

            $brandCompanyOne   = array_slice($brandCompany, 0, 10);
            $brandCompanyTwo   = array_slice($brandCompany, 10, 10);
            $brandCompanyThree = array_slice($brandCompany, 20, 10);
            $brandCompanyFore  = array_slice($brandCompany, 30, 10);

            $recommendOne   = array_slice($recommend, 0, 10);
            $recommendTwo   = array_slice($recommend, 10, 10);
            $recommendThree = array_slice($recommend, 20, 10);
            $recommendFour  = array_slice($recommend, 30, 10);
            $recommendFive  = array_slice($recommend, 40, 5);
            // 最新公告 & 简章
            $showcaseBrowseList = [];
            foreach ($positionList as $number => $item) {
                switch ($number) {
                    case 'C4':
                        // 有一个比较特殊的区域,C4,C4有部分是广告位,有部分不是广告位
                        $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html', [
                            'adList'    => $item,
                            'headList'  => $c4Head,
                            'dailyList' => $daily,
                            'jobHot'    => $jobHot,
                            'hrDynamic' => $hrDynamic,
                        ]);

                        break;
                    case 'B4':
                        // 这里是轮播,每7个一个位置,所以要变为7个一组的数组
                        $tmpB4      = [];
                        $tmpB4Key   = 0;
                        $tmpB4Index = 0;
                        foreach ($item as $key => $value) {
                            if ($tmpB4Key == 0) {
                                $tmpB4[$tmpB4Index] = [];
                            }
                            $tmpB4[$tmpB4Index][] = $value;
                            $tmpB4Key++;
                            if ($tmpB4Key == 7) {
                                $tmpB4Key = 0;
                                $tmpB4Index++;
                            }
                        }
                        $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html',
                            ['list' => $tmpB4]);
                        break;
                    case 'E1':
                        // C5其实是区分成左中右三个区域的,左中右各6个
                        $itemLeft                    = array_slice($item, 0, 6);
                        $itemMiddle                  = array_slice($item, 6, 6);
                        $itemRight                   = array_slice($item, 12, 6);
                        $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html', [
                            'leftList'   => $itemLeft,
                            'middleList' => $itemMiddle,
                            'rightList'  => $itemRight,
                        ]);
                        break;
                    case 'C2':
                        $C2_TOP    = array_slice($item, 0, 20);
                        $C2_BOTTOM = array_slice($item, 20);

                        $showcaseBrowseList['C2_TOP']    = $this->renderPartial('position/C2_TOP.html',
                            ['list' => $C2_TOP]);
                        $showcaseBrowseList['C2_BOTTOM'] = $this->renderPartial('position/C2_BOTTOM.html',
                            ['list' => $C2_BOTTOM]);
                        break;
                    case 'E2':
                    case 'E3':
                    case 'E4':
                        if ($number == 'E2') {
                            // E2其实是区分成左中右三个区域的,左中右各20个,但是顺序是按照1在第一组,2在第二组,3在第三组这样去做的
                            $itemLeft   = [];
                            $itemMiddle = [];
                            $itemRight  = [];
                            foreach ($item as $k => $eItem) {
                                if ($k % 3 == 0) {
                                    $itemLeft[] = $eItem;
                                } elseif ($k % 3 == 1) {
                                    $itemMiddle[] = $eItem;
                                } else {
                                    $itemRight[] = $eItem;
                                }
                            }
                            $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html', [
                                'leftList'   => $itemLeft,
                                'middleList' => $itemMiddle,
                                'rightList'  => $itemRight,
                            ]);
                        }
                        break;
                    case 'D1_1':
                    case 'D1_2':
                    case 'D1_3':
                    case 'D1_4':
                    case 'D1_5':
                        $list                        = array_chunk($item, 42);
                        $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html',
                            ['list' => $list]);
                        break;
                    default:
                        $showcaseBrowseList[$number] = $this->renderPartial('position/' . $number . '.html',
                            ['list' => $item]);
                        break;
                }
            }
            $data = array_merge([
                'showcaseGifLink'     => '/1.gif',
                'showcaseBrowseToken' => Yii::$app->params['showcaseBrowse']['token'],
                'nav'                 => HomeColumn::getNav(),
                'head'                => $remainHead,
                'rolling'             => $rolling,
                'brandCompanyOne'     => $brandCompanyOne,
                'brandCompanyTwo'     => $brandCompanyTwo,
                'brandCompanyThree'   => $brandCompanyThree,
                'brandCompanyFore'    => $brandCompanyFore,
                'focus'               => $focus,
                'recommendOne'        => $recommendOne,
                'recommendTwo'        => $recommendTwo,
                'recommendThree'      => $recommendThree,
                'recommendFour'       => $recommendFour,
                'recommendFive'       => $recommendFive,
                'top'                 => $top,
                'hotTag'              => $hotTag,
                'newest'              => $newest,
                'hotNews'             => $hotNews,
            ], $showcaseBrowseList);

            Cache::set($cacheKey, json_encode($data, true));
        }

        $this->setSeo(['title' => Yii::$app->params['seo']['home']['title']]);

        return $this->render('index.html', $data);
    }

    public function actionOldColumn()
    {
        $this->layout = 'column_main';
        $level1       = Yii::$app->request->get('level1');
        $level2       = Yii::$app->request->get('level2');

        // 这里会有一些特殊页面的输出
        if ($level1 == 'diqu' && !$level2) {
            // 301到地区栏目?
            return $this->redirect('/region.html', 301);
        }
        $path = $level2 ? $level1 . '/' . $level2 : $level1;
        $path = 'zhaopin/' . $path . '/';

        if ($path == 'zhaopin/sydw/zhengfuyincai/') {
            $v1Url   = Yii::$app->params['v1PcUrl'];
            $url     = $v1Url . '/' . $path;
            $content = file_get_contents($url);
            $content = iconv('GBK', 'UTF-8', $content);

            echo $content;
            exit;
        }
        // 这里是暂时做一个seo的优化实验

        if ($path == 'zhaopin/xuqiuxueke/') {
            return $this->redirect('/major.html', 301);
        }

        if ($path == 'zhaopin/total/') {
            return $this->redirect('/daily.html', 301);
        }

        if ($path == 'zhaopin/zhaopinhui/') {
            // 强制跳转
            return $this->redirect('/company/detail/965.html', 301);
        }

        $oldColumnConfig = Yii::$app->params['oldColumnToNewColumn'];
        if ($oldColumnConfig[$path]) {
            $homeColumn = HomeColumn::findOne($oldColumnConfig[$path]);

            $url = HomeColumn::getDetailUrl($homeColumn->id);

            return $this->redirect($url, 301);
        } else {
            // 这里还有旧栏目需要到旧栏目的
            $oldColumnToOldConfig = Yii::$app->params['oldColumnToOldColumn'];

            if ($oldColumnToOldConfig[$path]) {
                $url = $oldColumnToOldConfig[$path];

                return $this->redirect($url, 301);
            }

            $homeColumn = HomeColumn::findOne(['path' => $path]);
            if (!$homeColumn) {
                $this->notFound();
            }

            $url = HomeColumn::getDetailUrl($homeColumn->id);

            return $this->redirect($url, 301);
        }

        // 加个判断，如果状态不是1，就是404
        if ($homeColumn->status != 1) {
            $this->notFound();
        }

        return $this->distributeColumn($homeColumn);
    }

    public function actionOldNews()
    {
        $this->layout = 'column_main';
        $level1       = Yii::$app->request->get('level1');

        if (!$level1) {
            // 301到资讯首页
            return $this->redirect('/column/10.html', 301);
        }

        $url = Yii::$app->request->url;

        if (strpos($url, 'renshi') !== false) {
            // 强制跳转
            $basePath = 'renshi/';
        } else {
            $basePath = 'qiuzhi/';
        }

        // 找到对应的栏目id
        $path = $basePath . $level1 . '/';

        $homeColumn = HomeColumn::findOne(['path' => $path]);
        $url        = HomeColumn::getDetailUrl($homeColumn->id);

        return $this->redirect($url, 301);
    }

    public function actionOldTcolumn()
    {
        $this->notFound();
        $tid = Yii::$app->request->get('tid');
        if (!$tid) {
            $this->notFound();
        }

        $v1Url   = Yii::$app->params['v1PcUrl'];
        $url     = $v1Url . '/plus/list.php?tid=' . $tid;
        $content = file_get_contents($url);
        $content = iconv('GBK', 'UTF-8', $content);

        echo $content;
        exit;
    }

    public function actionOldArticle()
    {
        // 如果是mobile
        // 抓取html内容
        $level1 = Yii::$app->request->get('level1');
        $level2 = Yii::$app->request->get('level2');
        $level3 = Yii::$app->request->get('level3');
        $id     = Yii::$app->request->get('id');

        $pathInfo = $this->request->pathInfo;
        // 拿第一个/分割
        $firstPath = explode('/', $pathInfo)[0];

        $v1Url = Yii::$app->params['v1PcUrl'];
        if ($level3) {
            $url = "$v1Url/$firstPath/$level1/$level2/$level3/$id.html";
        } else {
            $url = "$v1Url/$firstPath/$level1/$level2/$id.html";
        }

        $content = file_get_contents($url);
        $content = iconv('GBK', 'UTF-8', $content);

        echo $content;
        exit;
    }

    public function actionOldCount()
    {
        // 随机100到500之间的数字
        $count = rand(100, 500);
        echo "document.write('$count');";
        exit;
        // 找到?后面的参数
        $url   = Yii::$app->request->getUrl();
        $v1Url = Yii::$app->params['v1PcUrl'] . $url;

        $content = file_get_contents($v1Url);
        $content = iconv('GBK', 'UTF-8', $content);

        echo $content;
        exit;
    }

    /**
     * @throws Exception
     */
    public function actionColumn()
    {
        $this->layout = 'column_main';
        //这里要接栏目ID，暂无链接过来
        $id = Yii::$app->request->get('id');
        if (!$id) {
            $this->notFound();
        }
        $model = HomeColumn::findOne($id);
        if (!$model) {
            $this->notFound();
        }

        // 内存加大一点到4g
        ini_set('memory_limit', '4096M');
        // 时间上开到5分钟
        set_time_limit(300);

        if ($model->status != 1) {
            $this->notFound();
        }

        return $this->distributeColumn($model);
    }

    /**
     * 地区
     * @return string
     */
    public function actionRegion()
    {
        $this->layout = 'column_main';
        $config       = Yii::$app->params['homeRegionColumn'];
        $list         = $config['list'];
        $hot          = $config['hot'];

        foreach ($hot as &$item) {
            $item['url'] = HomeColumn::getDetailUrl($item['id']);
        }

        foreach ($list as $k1 => $region) {
            foreach ($region['province'] as $k2 => $province) {
                $list[$k1]['province'][$k2]['url'] = HomeColumn::getDetailUrl($province['id']);
                foreach ($province['city'] as $k3 => $city) {
                    $list[$k1]['province'][$k2]['city'][$k3]['url'] = HomeColumn::getDetailUrl($city['id']);
                }
            }
        }

        $this->setSeo(['title' => Yii::$app->params['seo']['region']['title']]);

        return $this->render('region.html', [
            'hot'  => $hot,
            'list' => $list,
        ]);
    }

    /**
     * 学科
     * @return string
     */
    public function actionMajor()
    {
        $this->layout = 'column_main';

        $list = HomeColumn::getAllMajorColumn();
        $hot  = Yii::$app->params['hotMajorColumn'];

        foreach ($hot as &$item) {
            $item['url'] = HomeColumn::getDetailUrl($item['id']);
        }

        return $this->render('major.html', [
            'list' => $list,
            'hot'  => $hot,
        ]);
    }

    /**
     * 在这里做实际的分发
     * @throws Exception
     */
    private function distributeColumn(HomeColumn $homeColumn)
    {
        $id = $homeColumn->id;
        // 海外的直接跳过
        if (in_array($id, BaseHomeColumn::ABROAD_ID)) {
            // 重定向到高才海外首页
            $haiwaiHome = UrlHelper::getHaiwaiHome();

            return $this->redirect($haiwaiHome, 301);
        }

        // 旧海外的直接跳过
        if (in_array($id, BaseHomeColumn::OLD_ABROAD_ID)) {
            // 重定向到高才海外首页
            $haiwaiHome = UrlHelper::getHaiwaiHome();

            return $this->redirect($haiwaiHome, 301);
        }

        // 博士后重定向
        if (in_array($id, BaseHomeColumn::BOSHIHOU_COLUMN_ID)) {
            // 重定向到高才博士后首页

            return $this->redirect(UrlHelper::getBoShiHouHome(), 301);
        }

        $cacheKey = HomeColumnCache::getKey(HomeColumnCache::TYPE_PC_COLUMN, $id);
        // 设置好seo的信息
        // 在http://zentao.jugaocai.com/index.php?m=story&f=view&id=348有一个新的需求,对于某些特殊栏目,进行新的设置,所以这里需要做一下判断
        if (in_array($id, BaseHomeColumn::AREA_ID_LIST)) {
            // 地区栏目
            $seoConfig = Yii::$app->params['seo']['areaColumn'];
        } elseif (in_array($id, BaseHomeColumn::MAJOR_ID_LIST)) {
            // 学科栏目
            $seoConfig = Yii::$app->params['seo']['majorColumn'];
        } else {
            $seoConfig = Yii::$app->params['seo']['column'];
        }
        $seoTitle       = $homeColumn->seo_title ?: str_replace('【栏目名称】', $homeColumn->name, $seoConfig['title']);
        $seoKeywords    = $homeColumn->seo_keywords ?: str_replace('【栏目名称】', $homeColumn->name,
            $seoConfig['keywords']);
        $seoDescription = $homeColumn->seo_description ?: str_replace('【栏目名称】', $homeColumn->name,
            $seoConfig['description']);

        $this->setSeo([
            'title'       => $seoTitle,
            'keywords'    => $seoKeywords,
            'description' => $seoDescription,
        ]);

        $isUpdateCache = false;
        if (Yii::$app->request->get(HomeColumnCache::CACHE_URL_KEY)) {
            $isUpdateCache = true;
        }

        $data = Cache::get($cacheKey);


        if ($data && !$isUpdateCache) {
            $data = json_decode($data, true);
            switch ($homeColumn->template_type) {
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL1_A :
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL1_B :
                    // $name = '一级栏目通用模板A+B';
                    $showHtml = 'column.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL2   :
                    //二级栏目
                    $showHtml = 'columnSecond.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_SPECIAL_A:
                    //政府与事业单位
                    $showHtml = 'columnGovernment.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_AREA:
                    //省区栏目
                    $showHtml = 'columnProvince.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_POSTDOC  :
                    //博士后栏目

                    $showHtml = 'columnPostdoctor.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_DAILY:
                    // 每日汇总
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_NEWS:
                    // 资讯

                    if ($homeColumn->level == 1) {
                        $showHtml = 'newsLevel1.html';
                    } else {
                        $showHtml = 'newsLevel2.html';
                    }
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_SPECIAL_B:
                    //专题模版b
                    $showHtml = 'columnSpecial.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_POSTDOC_B:
                    //博士后模版b
                    $showHtml = 'columnPostdoctorB.html';
                    break;
            }
        } else {
            // BaseActiveRecord::openDb2();
            // 测试性能
            BaseActiveRecord::openDb2();

            // 把内存开到2g
            ini_set('memory_limit', '2048M');

            $keywords = Yii::$app->request->get();
            $name     = $homeColumn->name;

            $keywords = array_merge([
                'columnId'     => $id,
                'templateType' => $homeColumn->template_type,
                'level'        => $homeColumn->level,
            ], $keywords);

            //暂时不会在栏目页直接传高级筛选参数，但是二级栏目查询公告会和最新公告查询接口公用，这里直接过滤去除
            unset($keywords['isEstablishment'], $keywords['announcementHeat']);

            switch ($homeColumn->template_type) {
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL1_A :
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL1_B :
                    // $name = '一级栏目通用模板A+B';
                    $jobList          = Announcement::getColumnJob($id);
                    $announcementList = Announcement::columnAnnouncementList($keywords);

                    $columnList = [];
                    foreach ($announcementList as $number => $item) {
                        switch ($number) {
                            case 'headlines':
                                $itemTop    = array_slice($item, 0, 1);
                                $itemBottom = array_slice($item, 1, 3);

                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html', [
                                    'topList'    => $itemTop,
                                    'bottomList' => $itemBottom,
                                ]);
                                break;
                            case 'right_announcement_classification':
                                $item['guideTextList'] = Yii::$app->params['latestAnnouncementGuideBuyText'];
                                $columnList[$number]   = $this->renderPartial('column-module/' . $number . '.html',
                                    $item);
                                break;
                            case 'latest_announcement_tiled':
                                $item['guideTextList'] = Yii::$app->params['latestAnnouncementGuideBuyText'];
                                $columnList[$number]   = $this->renderPartial('column-module/' . $number . '.html', [
                                    'list'          => $item['list'],
                                    'tapList'       => $item['tapList'],
                                    'guideTextList' => $item['guideTextList'],
                                ]);
                                break;
                            default:
                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html',
                                    ['list' => $item]);
                                break;
                        }
                    }

                    $config = Yii::$app->params['firstLevelColumn']['job'];

                    foreach ($config as $number => $item) {
                        switch ($number) {
                            case 'right_job_classification':
                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html', [
                                    'list' => $jobList,
                                ]);
                                break;
                            default:
                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html',
                                    ['list' => $item]);
                                break;
                        }
                    }

                    $data = array_merge([
                        'name'                => '这里是栏目页:' . $name,
                        'columnId'            => $id,
                        'templateType'        => $homeColumn->template_type,
                        'templateTypeLevel1B' => BaseHomeColumn::TEMPLATE_TYPE_LEVEL1_B,
                    ], $columnList);

                    $showHtml = 'column.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_LEVEL2   :
                    //二级栏目
                    $announcementList       = Announcement::secondColumnAnnouncementList($keywords);
                    $columnList             = [];
                    $secondColumnSearchList = [];
                    foreach ($announcementList as $number => $item) {
                        switch ($number) {
                            case "latest_announcement":
                                $secondColumnSearchList                   = BaseAnnouncement::getSecondColumnSearchList($keywords);
                                $columnList[$number]                      = $this->renderPartial('column-module/' . $number . '.html',
                                    [
                                        'tapList'  => $secondColumnSearchList,
                                        'columnId' => $id,
                                    ]);
                                $columnList['latestAnnouncementPageSize'] = $item['pageSize'];
                                break;
                            default:
                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html',
                                    ['list' => $item]);
                                break;
                        }
                    }

                    // 二级栏目会有一些栏目跟字段的某些属性做了捆绑,所以会有一些栏目对于职位列表是有过滤的,暂时是会有学科和地区两者
                    $columnDictionaryData = BaseHomeColumnDictionaryRelationship::getAllTypeList($id);

                    if (count($columnDictionaryData['area']) == 1) {
                        $columnDictionaryInfoAreaId = $columnDictionaryData['area'][0]['id'];
                        $keywords['city_id']        = $columnDictionaryInfoAreaId;
                    }

                    if (count($columnDictionaryData['major']) == 1) {
                        $columnDictionaryInfoMajorId = $columnDictionaryData['major'][0]['id'];
                        $keywords['major_id']        = $columnDictionaryInfoMajorId;
                    }

                    $jobList = Announcement::getSecondColumnJob($keywords);

                    $secondColumnJobSearchList = BaseAnnouncement::getSecondColumnJobSearchList($keywords['columnId'],
                        $columnDictionaryData);

                    foreach ($jobList as $number => $jobItem) {
                        $columnList[$number]             = $this->renderPartial('column-module/' . $number . '.html', [
                            'tapList'  => $secondColumnJobSearchList,
                            'count'    => $jobItem['count'],
                            'columnId' => $id,
                        ]);
                        $columnList['jobAccount']        = $jobItem['count'];
                        $columnList['latestJobPageSize'] = $jobItem['pageSize'];
                    }

                    $data = array_merge([
                        'name'         => '这里是栏目页:' . $name,
                        'columnId'     => $id,
                        'templateType' => $homeColumn->template_type,
                        'majorId'      => $columnDictionaryInfoMajorId,
                        'areaId'       => $columnDictionaryInfoAreaId,
                    ], $columnList);

                    $showHtml = 'columnSecond.html';

                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_SPECIAL_A:
                    //政府与事业单位
                    $jobList          = Announcement::getGovernmentColumnJob();
                    $announcementList = Announcement::governmentColumnAnnouncementList($keywords);
                    $columnList       = [];
                    $sort             = 1;
                    foreach ($jobList as $number => $item) {
                        $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html',
                            ['list' => $item]);
                        foreach ($item as $v) {
                            $columnList['jobPageSize']         = $v['page']['limit'];
                            $columnList['jobAccount_' . $sort] = $v['page']['count'];
                            $sort++;
                        }
                    }
                    foreach ($announcementList as $number => $item) {
                        switch ($number) {
                            case 'headlines':
                                $itemTop    = array_slice($item, 0, 1);
                                $itemBottom = array_slice($item, 1, 3);

                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html', [
                                    'topList'    => $itemTop,
                                    'bottomList' => $itemBottom,
                                ]);
                                break;
                            case 'government_latest_announcement':
                                $item['guideTextList'] = Yii::$app->params['latestAnnouncementGuideBuyText'];
                                $columnList[$number]   = $this->renderPartial('column-module/' . $number . '.html',
                                    $item);
                                break;
                            default:
                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html',
                                    ['list' => $item]);
                                break;
                        }
                    }

                    $data = array_merge([
                        'name'         => '这里是栏目页:' . $name,
                        'columnId'     => $id,
                        'templateType' => $homeColumn->template_type,
                    ], $columnList);

                    $showHtml = 'columnGovernment.html';

                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_AREA:
                    //省区栏目
                    //                    $jobList          = Announcement::getProvinceColumnJob($id);
                    $jobList          = Announcement::getColumnJob($id);
                    $announcementList = Announcement::provinceColumnAnnouncementList($keywords);

                    $columnList = [];
                    foreach ($announcementList as $number => $item) {
                        switch ($number) {
                            case 'headlines':
                                $itemTop    = array_slice($item, 0, 1);
                                $itemBottom = array_slice($item, 1, 3);

                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html', [
                                    'topList'    => $itemTop,
                                    'bottomList' => $itemBottom,
                                ]);
                                break;
                            case 'right_announcement_classification':
                                $item['guideTextList'] = Yii::$app->params['latestAnnouncementGuideBuyText'];
                                $columnList[$number]   = $this->renderPartial('column-module/' . $number . '.html',
                                    $item);
                                break;
                            default:
                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html',
                                    ['list' => $item]);
                                break;
                        }
                    }
                    $config = Yii::$app->params['provinceColumn']['job'];

                    foreach ($config as $k => $item) {
                        switch ($k) {
                            case 'area_right_job_classification':
                                $columnList[$k] = $this->renderPartial('column-module/' . $k . '.html', [
                                    'list' => $jobList,
                                ]);
                                break;
                            default:
                                $columnList[$k] = $this->renderPartial('column-module/' . $k . '.html',
                                    ['list' => $item]);
                                break;
                        }
                    }

                    $data = array_merge([
                        'name'         => '这里是栏目页:' . $name,
                        'columnId'     => $id,
                        'templateType' => $homeColumn->template_type,
                    ], $columnList);

                    $showHtml = 'columnProvince.html';

                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_POSTDOC  :
                    //博士后栏目
                    $jobList = Announcement::getPostDoctorColumnJob($keywords);

                    $announcementList = Announcement::PostDoctorColumnAnnouncementList($keywords);

                    $columnList = [];
                    foreach ($announcementList as $number => $item) {
                        switch ($number) {
                            case 'headlines':
                                $itemTop    = array_slice($item, 0, 1);
                                $itemBottom = array_slice($item, 1, 3);

                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html', [
                                    'topList'    => $itemTop,
                                    'bottomList' => $itemBottom,
                                ]);
                                break;
                            case 'right_announcement_classification':
                                $item['guideTextList'] = Yii::$app->params['latestAnnouncementGuideBuyText'];
                                $columnList[$number]   = $this->renderPartial('column-module/' . $number . '.html',
                                    $item);
                                break;
                            default:
                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html',
                                    ['list' => $item]);
                                break;
                        }
                    }

                    foreach ($jobList as $number => $item) {
                        $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html', [
                            'recommendList'    => $item['recommend']['list'],
                            'recommendTapList' => $item['recommend']['tapList'],
                            'newestList'       => $item['newest']['list'],
                            'newestTapList'    => $item['newest']['tapList'],
                            'hotList'          => $item['hot']['list'],
                            'hotTapList'       => $item['hot']['tapList'],
                        ]);
                    }

                    $data = array_merge([
                        'name'         => '这里是栏目页:' . $name,
                        'columnId'     => $id,
                        'templateType' => $homeColumn->template_type,
                    ], $columnList);

                    $showHtml = 'columnPostdoctor.html';
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_DAILY:
                    // 每日汇总
                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_NEWS:
                    if ($homeColumn->level == 1) {
                        $showHtml = 'newsLevel1.html';

                        $data = $this->newsColumnLevel1($homeColumn);
                    } else {
                        $showHtml = 'newsLevel2.html';

                        $data = $this->newsColumnLevel2($homeColumn);
                    }

                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_SPECIAL_B:
                    //专场通用模板B

                    $jobList          = Announcement::getSpecialBColumnJob($keywords);
                    $announcementList = Announcement::specialBColumnAnnouncementList($keywords);

                    $columnList              = [];
                    $specialColumnSearchList = [];
                    foreach ($jobList as $number => $item) {
                        $specialColumnSearchList         = BaseAnnouncement::getSpecialBColumnSearchList($keywords);
                        $columnList[$number]             = $this->renderPartial('column-module/' . $number . '.html', [
                            'list'    => $item['list'],
                            'tapList' => $specialColumnSearchList,
                        ]);
                        $columnList['jobAccount']        = $item['count'];
                        $columnList['latestJobPageSize'] = 24;
                        break;
                    }

                    foreach ($announcementList as $number => $item) {
                        switch ($number) {
                            case 'headlines':
                                $itemTop    = array_slice($item['list'], 0, 1);
                                $itemBottom = array_slice($item['list'], 1, 3);

                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html', [
                                    'topList'    => $itemTop,
                                    'bottomList' => $itemBottom,
                                ]);
                                break;
                            case 'special_latest_announcement':
                                //$specialColumnSearchList                  = BaseAnnouncement::getSpecialBColumnSearchList($keywords);
                                $columnList[$number]                      = $this->renderPartial('column-module/' . $number . '.html',
                                    [
                                        'list'  => $item['list'],
                                        'count' => $item['count'],
                                    ]);
                                $columnList['tapList']                    = $specialColumnSearchList;
                                $columnList['announcement_account']       = $item['count'];
                                $columnList['latestAnnouncementPageSize'] = 12;
                                break;
                            default:
                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html',
                                    ['list' => $item['list']]);
                                break;
                        }
                    }

                    $data = array_merge([
                        'name'         => '这里是栏目页:' . $name,
                        'columnId'     => $id,
                        'templateType' => $homeColumn->template_type,
                    ], $columnList);

                    $showHtml = 'columnSpecial.html';

                    break;
                case BaseHomeColumn::TEMPLATE_TYPE_POSTDOC_B  :
                    //博士后栏目模版B
                    //                    $jobList          = Announcement::getPostDoctorColumnJob($keywords);
                    $jobList = Announcement::getColumnJob($id);

                    $announcementList = Announcement::PostDoctorColumnAnnouncementList($keywords);

                    $columnList = [];
                    foreach ($announcementList as $number => $item) {
                        switch ($number) {
                            case 'headlines':
                                $itemTop    = array_slice($item, 0, 1);
                                $itemBottom = array_slice($item, 1, 3);

                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html', [
                                    'topList'    => $itemTop,
                                    'bottomList' => $itemBottom,
                                ]);
                                break;
                            case 'right_announcement_classification':
                                $item['guideTextList'] = Yii::$app->params['latestAnnouncementGuideBuyText'];
                                $columnList[$number]   = $this->renderPartial('column-module/' . $number . '.html',
                                    $item);
                                break;
                            default:
                                $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html',
                                    ['list' => $item]);
                                break;
                        }
                    }
                    $config = Yii::$app->params['postDoctorColumn']['job'];

                    foreach ($config as $k => $item) {
                        switch ($k) {
                            case 'right_job_classification':
                                $columnList[$k] = $this->renderPartial('column-module/' . $k . '.html', [
                                    'list' => $jobList,
                                ]);
                                break;
                        }
                    }
                    //                    foreach ($jobList as $number => $item) {
                    //                        $columnList[$number] = $this->renderPartial('column-module/' . $number . '.html', [
                    //                            'recommendList'    => $item['recommend']['list'],
                    //                            'recommendTapList' => $item['recommend']['tapList'],
                    //                            'newestList'       => $item['newest']['list'],
                    //                            'newestTapList'    => $item['newest']['tapList'],
                    //                            'hotList'          => $item['hot']['list'],
                    //                            'hotTapList'       => $item['hot']['tapList'],
                    //                        ]);
                    //                    }

                    $data = array_merge([
                        'name'         => '这里是栏目页:' . $name,
                        'columnId'     => $id,
                        'templateType' => $homeColumn->template_type,
                    ], $columnList);

                    $showHtml = 'columnPostdoctorB.html';
                    break;
            }

            $refreshTime = BaseHomeColumn::getHomeColumnFactorTime($id);

            if ($refreshTime) {
                $data['timeFactorTime'] = $refreshTime;
            }

            Cache::set($cacheKey, json_encode($data));
        }
        if (!$showHtml) {
            $this->notFound();
        }

        // 点击量
        HomeColumn::click($id);

        // 如果之前缓存了时间因子
        if ($data['timeFactorTime']) {
            BaiduTimeFactor::create('', $data['timeFactorTime']);
            ToutiaoTimeFactor::create('', $data['timeFactorTime']);
        }


        return $this->render($showHtml, $data);
    }

    /**
     * 资讯栏目
     *
     * @param HomeColumn $homeColumn
     *
     */
    public function newsColumn(HomeColumn $homeColumn)
    {
        /**
         * 因为资讯栏目的一级和二级使用的模板有所不同,所以在这里再做一次分发
         */
        if ($homeColumn->level == 1) {
            return $this->newsColumnLevel1($homeColumn);
        } else {
            return $this->newsColumnLevel2($homeColumn);
        }
    }

    public function newsColumnLevel1(HomeColumn $homeColumn)
    {
        $newsList          = News::getLevel1NewsList($homeColumn->id);
        $recommendNewsList = News::getRecommendNewsList($homeColumn->id, 4);
        $hotList           = News::getHotList($homeColumn->id);
        $firstTagList      = News::getLevel1FirstTagList();
        $top               = array_slice($recommendNewsList, 0, 1)[0];
        $list              = array_slice($recommendNewsList, 1);
        $recommendNews     = $this->renderPartial('news/recommend_news.html', [
            'top'  => $top,
            'list' => $list,
        ]);

        $hotListHtml = $this->renderPartial('news/hot_list.html', [
            'list' => $hotList,
        ]);

        return [
            'newsList'      => $newsList,
            'firstTagList'  => $firstTagList,
            'recommendNews' => $recommendNews,
            'hotList'       => $hotListHtml,
            'columnId'      => $homeColumn->id,
        ];
    }

    public function newsColumnLevel2(HomeColumn $homeColumn)
    {
        $newsList          = News::getLevel2List($homeColumn->id);
        $recommendNewsList = News::getRecommendNewsList($homeColumn->id, 4);
        $hotList           = News::getHotList($homeColumn->id);
        $top               = array_slice($recommendNewsList, 0, 1)[0];
        $list              = array_slice($recommendNewsList, 1);
        $recommendNews     = $this->renderPartial('news/recommend_news.html', [
            'top'  => $top,
            'list' => $list,
        ]);

        $hotListHtml = $this->renderPartial('news/hot_list.html', [
            'list' => $hotList,
        ]);

        return [
            'name'          => $homeColumn->name,
            'newsList'      => $newsList['list'],
            'total'         => $newsList['total'],
            'recommendNews' => $recommendNews,
            'hotList'       => $hotListHtml,
            'columnId'      => $homeColumn->id,
        ];
    }

    public function actionGetNewsListHtml()
    {
        $columnId = Yii::$app->request->get('columnId');
        $page     = Yii::$app->request->get('page');
        $list     = News::getLevel2List($columnId, $page);
        $html     = $this->renderPartial('news/level2List.html', [
            'list' => $list['list'],
        ]);

        return $this->success([
            'html'  => $html,
            'total' => $list['total'],
        ]);
    }

    /**
     * 每日汇总栏目
     */
    public function actionDaily()
    {
        $this->layout = 'column_main';

        $page     = Yii::$app->request->get('page', 1);
        $pageSize = Yii::$app->request->get('pageSize', 20);

        // 为了避免有人恶意拿数据,pageSize必须要限制一下
        if ($pageSize > 100) {
            $pageSize = 100;
        }

        $data     = DailyAnnouncementSummary::getList($page, $pageSize);
        $newsList = News::top10();

        $this->setSeo(['title' => Yii::$app->params['seo']['daily']['title']]);

        // 去拿每日汇总的列表数据
        return $this->render('daily.html', [
            'newsList' => $newsList,
            'list'     => $data['list'],
            'pages'    => $data['pages'],
        ]);
    }

    public function getShowcaseListByHomePositionId($id): array
    {
        $list = BaseShowcase::find()
            ->alias('s')
            ->leftJoin('home_position as h', 'h.id=s.home_position_id')
            ->where([
                's.home_position_id' => $id,
                's.status'           => BaseShowcase::STATUS_ONLINE,
                's.is_show'          => 1,
            ])
            ->select([
                's.title',
                's.sub_title',
                's.second_title',
                's.image_url',
                's.image_link',
                's.image_alt',
                's.target_link',
                's.home_position_id',
                's.describe',
                's.id',
                'h.number',
                'h.platform_type',
                'h.name',
                'h.id as home_position_id',
            ])
            ->orderBy('s.sort desc,s.add_time desc')
            ->asArray()
            ->all();

        $platformList = BaseHomePosition::PLATFORM_TYPE_LIST;
        foreach ($list as $k => $item) {
            $platform = '';
            foreach ($platformList as $platformType => $v) {
                if ($platformType == $v['platform_type']) {
                    $platform = $v['name'];
                }
            }
            $temp = [
                'showcaseId'     => 'showcaseId=' . $item['id'],
                'positionNumber' => 'positionNumber=' . $item['number'],
                'showcaseTitle'  => 'showcaseTitle=' . $item['title'],
                'platform'       => 'platform=' . $platform,
                'platformType'   => 'platformType=' . $item['platform_type'],
                'position'       => 'position=' . $item['name'],
                'homePositionId' => 'homePositionId=' . $item['home_position_id'],
                'type'           => 'type=1',
            ];

            $dataStream             = implode('&', $temp);
            $list[$k]['dataStream'] = $dataStream;
        }

        return $list;
    }

    /**
     * 分页获取不到数据，获取默认页码的数据
     * @param $type
     * @param $keywords
     * @return array
     * @throws Exception
     */
    private function getNotListAnnouncement($type, $keywords)
    {
        //判断需不需要返回msg
        $msg = '';
        if ($keywords['page'] == 2) {
            //第二页没有数据了，也就是只有第一页有数据，提示消息
            $msg = '暂无更多内容，敬请期待';
        }

        //如果没有数据了，都是要返回第一页的数据
        $keywords['page'] = 1;
        $announcementList = [];
        switch ($type) {
            case 'column':
                $announcementList = Announcement::changeColumnAnnouncementList($keywords);
                break;
            case 'government':
                $announcementList = Announcement::changGovernmentColumnAnnouncementList($keywords);
                break;
            case 'province':
                $announcementList = Announcement::changeProvinceColumnAnnouncementList($keywords);
                break;
            case 'postDoctor':
                $announcementList = Announcement::changePostDoctorColumnAnnouncementList($keywords);
                break;
        }

        $list = $this->renderPartial('column-module/' . $keywords['key'] . '.html', ['list' => $announcementList]);

        return [
            'list' => $list,
            'msg'  => $msg,
            'page' => 2,
            //重置到第一页，默认返回下一页要请求到页码，即2
        ];
    }

    /**
     * 切换页面公告数据（一级）
     * @throws Exception
     */
    public function actionChangeAnnouncementList()
    {
        $this->layout = 'column_main';
        $id           = Yii::$app->request->get('columnId');
        if (!$id) {
            $this->notFound();
        }

        $keywords         = Yii::$app->request->get();
        $announcementList = Announcement::changeColumnAnnouncementList($keywords);
        if ($announcementList) {
            $list = $this->renderPartial('column-module/' . $keywords['key'] . '.html', ['list' => $announcementList]);

            return $this->success(['list' => $list]);
        } else {
            $data = $this->getNotListAnnouncement('column', $keywords);

            return $this->success($data);
        }
    }

    /**
     * 切换页面公告数据（政府与事业单位/专题栏目）
     * @throws Exception
     */
    public function actionChangeGovernmentAnnouncementList()
    {
        $this->layout = 'column_main';
        $id           = Yii::$app->request->get('columnId');
        if (!$id) {
            $this->notFound();
        }

        $keywords         = Yii::$app->request->get();
        $announcementList = Announcement::changGovernmentColumnAnnouncementList($keywords);

        if ($announcementList) {
            $list = $this->renderPartial('column-module/' . $keywords['key'] . '.html', ['list' => $announcementList]);

            return $this->success(['list' => $list]);
        } else {
            $data = $this->getNotListAnnouncement('government', $keywords);

            return $this->success($data);
        }
    }

    /**
     * 切换页面公告数据（省区栏目）
     * @throws Exception
     */
    public function actionChangeProvinceAnnouncementList()
    {
        $this->layout = 'column_main';
        $id           = Yii::$app->request->get('columnId');
        if (!$id) {
            $this->notFound();
        }

        $keywords         = Yii::$app->request->get();
        $announcementList = Announcement::changeProvinceColumnAnnouncementList($keywords);

        if ($announcementList) {
            $list = $this->renderPartial('column-module/' . $keywords['key'] . '.html', ['list' => $announcementList]);

            return $this->success(['list' => $list]);
        } else {
            $data = $this->getNotListAnnouncement('province', $keywords);

            return $this->success($data);
        }
    }

    /**
     * 切换页面公告数据（博士后栏目）
     * @throws Exception
     */
    public function actionChangePostDoctorAnnouncementList()
    {
        $this->layout = 'column_main';
        $id           = Yii::$app->request->get('columnId');
        if (!$id) {
            $this->notFound();
        }

        $keywords         = Yii::$app->request->get();
        $announcementList = Announcement::changePostDoctorColumnAnnouncementList($keywords);

        if ($announcementList) {
            $list = $this->renderPartial('column-module/' . $keywords['key'] . '.html', ['list' => $announcementList]);

            return $this->success(['list' => $list]);
        } else {
            $data = $this->getNotListAnnouncement('postDoctor', $keywords);

            return $this->success($data);
        }
    }

    /**
     *
     */
    public function actionSearch()
    {
        $this->layout = 'column_main';

        $params = Yii::$app->request->get();

        if ($params['q']) {
            $params['keyword'] = urldecode($params['q']);
        }

        $pathInfo = \Yii::$app->request->pathInfo;
        if ($pathInfo == 'plus/search.php') {
            // 这里的字符是有问题的,判断字符编码是否是gbk
            $params['keyword'] = iconv('gbk', 'utf-8', $params['keyword']);
        }

        try {
            // 在这里做一些基本限制,避免去搜索的是有效的数据
            $params['keyword'] = trim($params['keyword']);
            if ($params['keyword']) {
                $app  = new CommonSearchApplication();
                $data = $app->pcCommonSearch($params);
            }
            //            else {
            //                如果没有关键词，获取热门推荐公告/资讯列表
            //                $data['list'] = BaseHomePosition::getPcHotRecommendList($params['type'], 10);
            //            }
            $dailyList = DailyAnnouncementSummary::getSimpleList(7);

            $this->setSeo(['title' => Yii::$app->params['seo']['search']['title']]);
            $showCaseList = HomePosition::getSearchShowCase();

            //热门搜索关键词
            //这里取公告和资讯的热门搜索广告位广告
            $announcementNumber        = 'gonggao_remensousuo';
            $announcementHotId         = BaseHomePosition::findOneVal(['number' => $announcementNumber], 'id');
            $announcementHotSearchList = BaseShowcase::getByPositionConfig($announcementHotId, $announcementNumber);
            foreach ($announcementHotSearchList as $k => $value) {
                if (strlen($value['real_target_link']) < 1) {
                    $announcementHotSearchList[$k]['url'] = "/search?type=1&keyword=" . $value['title'];
                }
            }

            $newsNumber        = 'zixun_remensousuo';
            $newsHotId         = BaseHomePosition::findOneVal(['number' => $newsNumber], 'id');
            $newsHotSearchList = BaseShowcase::getByPositionConfig($newsHotId, $newsNumber);
            foreach ($newsHotSearchList as $k => $value) {
                if (strlen($value['real_target_link']) < 1) {
                    $newsHotSearchList[$k]['url'] = "/search?type=2&keyword=" . $value['title'];
                }
            }

            if (empty($data['list'])) {
                if ($params['type'] == 2) {
                    $searchHotRecommendNumber = 'zixunliebiao_rementuijian';
                } else {
                    $searchHotRecommendNumber = 'gonggaoliebiao_rementuijian';
                }
                $positionId             = BaseHomePosition::findOneVal(['number' => $searchHotRecommendNumber], 'id');
                $searchHotRecommendList = BaseShowcase::getByPositionConfig($positionId, $searchHotRecommendNumber);
                $searchHotRecommend     = $this->renderPartial('position/' . $searchHotRecommendNumber . '.html', [
                    'list' => $searchHotRecommendList,
                ]);
            }

            //推荐单位列表信息
            $homePositionList = HomePosition::getJobShowcase2024();

            // $showCaseT1     = [];
            // $showCaseT1Name = [];
            // foreach ($homePositionList as $value) {
            //     $temp  = BaseShowcase::getByPositionConfig($value['id'], $value['number']);
            //     $count = sizeof($temp);
            //     if ($count > 0) {
            //         $showCaseT1[]             = $temp;
            //         $showCaseT1Name[]['name'] = $value['chinese_name'];
            //     }
            // }
            //
            // // 合并一下showCaseT1和showCaseT1Name,让两者和jobList一样的顺序
            // $showcaseList = [];
            // foreach ($showCaseT1 as $k => $item) {
            //     $showcaseList[$k]['list'] = $item;
            //     $showcaseList[$k]['name'] = $showCaseT1Name[$k]['name'];
            // }

            return $this->render('search.html', [
                'dailyList'                 => $dailyList ?: [],
                'list'                      => $data['list'] ?: [],
                'pages'                     => $data['pages'] ?: '0',
                'keyword'                   => $params['keyword'] ?: '',
                'type'                      => $params['type'] ?: '1',
                'showCaseOne'               => $showCaseList[0]['list'][0],
                'showCaseTwo'               => $showCaseList[1]['list'][0],
                // 'showCaseT1'                => $showCaseT1,
                // 'showCaseT1Name'            => $showCaseT1Name,
                'announcementHotSearchList' => $announcementHotSearchList,
                'newsHotSearchList'         => $newsHotSearchList,
                'showcaseList'              => $homePositionList,
                'searchHotRecommend'        => $searchHotRecommend,
            ]);
        } catch (Exception $e) {
            $this->notFound();
        }
    }

    /**
     * 网站地图
     * @return string
     */
    public function actionSitemap()
    {
        $this->layout = 'column_main';
        $sitemap      = Yii::$app->params['sitemap'];
        foreach ($sitemap as &$parent) {
            if ($parent['id']) {
                $parent['url'] = HomeColumn::getDetailUrl($parent['id']);
            } elseif ($parent['url']) {
                $parent['url'] = UrlHelper::fix($parent['url']);
            } elseif ($parent['variable']) {
                $parent['url'] = $this->getVariableValue($parent['variable']);
            } elseif ($parent['action']) {
                $action        = $parent['action'];
                $parent['url'] = UrlHelper::$action();
            }

            foreach ($parent['list'] as &$item) {
                if ($item['id']) {
                    $item['url'] = HomeColumn::getDetailUrl($item['id']);
                } elseif ($item['url']) {
                    $item['url'] = UrlHelper::fix($item['url']);
                } elseif ($item['variable']) {
                    $item['url'] = $this->getVariableValue($item['variable']);
                } elseif ($item['action']) {
                    $itemAction  = $item['action'];
                    $item['url'] = UrlHelper::$itemAction();
                }
            }
        }

        return $this->render('sitemap.html', [
            'list' => $sitemap,
        ]);
    }

    public function getVariableValue($variable)
    {
        switch ($variable) {
            case 'haiHaiHome':
                return UrlHelper::getHaiwaiHome();
            case 'chuHaiUrl':
                return UrlHelper::getHaiwaiChuhai();
            case 'guiGuoUrl':
                return UrlHelper::getHaiwaiGuiguo();
            case 'qiuXianUrl':
                return UrlHelper::getHaiwaiQiuxian();
            case 'haiYouUrl':
                return UrlHelper::getHaiwaiYouqing();
            case 'danWeiUrl':
                return UrlHelper::getHaiwaiCompany();
            default:
                return '';
        }
    }

    /**
     * 切换最新公告数据（二级栏目）
     * @throws Exception
     */
    public function actionChangeLatestAnnouncement()
    {
        $keywords = Yii::$app->request->get();
        $memberId = Yii::$app->user->id;
        $keywords = [
            'columnId'         => $keywords['columnId'],
            'key'              => 'latest_announcement',
            'limit'            => 12,
            'major_id'         => $keywords['majorId'] ?: '',
            'education_type'   => $keywords['educationType'] ?: '',
            'city_id'          => $keywords['cityId'] ?: '',
            'job_category_id'  => $keywords['jobCategoryId'] ?: '',
            'page'             => $keywords['page'] ?: 1,
            'isEstablishment'  => $keywords['isEstablishment'] ?: 0,
            'announcementHeat' => $keywords['announcementHeat'] ?: 0,
        ];

        // 内存开到2g
        ini_set('memory_limit', '2048M');

        if (Yii::$app->user->isGuest) {
            if ($keywords['major_id'] || $keywords['education_type'] || $keywords['city_id'] || $keywords['job_category_id'] || $keywords['page'] != 1 || $keywords['isEstablishment'] || $keywords['announcementHeat']) {
                return $this->notLogin();
            }
        }
        //判断求职者权限
        $resumeId = BaseMember::getMainId($memberId);
        if (ResumeEquity::checkEquity($resumeId, ResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
            unset($keywords['isEstablishment'], $keywords['announcementHeat']);
        }

        $announcementList = BaseAnnouncement::changeSecondColumnAnnouncementList($keywords);
        $list             = $this->renderPartial('column-module/latest_announcement_list.html',
            ['list' => $announcementList]);

        return $this->success(['list' => $list]);
    }

    /**
     * 切换最新职位数据（二级栏目）
     * @throws Exception
     */
    public function actionChangeLatestJob()
    {
        $keywords = Yii::$app->request->get();
        $memberId = Yii::$app->user->id;
        $keywords = [
            'columnId'        => $keywords['columnId'],
            'key'             => 'latest_announcement',
            'limit'           => 12,
            'major_id'        => $keywords['majorId'] ?: '',
            'education_type'  => $keywords['educationType'] ?: '',
            'city_id'         => $keywords['cityId'] ?: '',
            'job_category_id' => $keywords['jobCategoryId'] ?: '',
            'page'            => $keywords['page'] ?: 1,
            'isEstablishment' => $keywords['isEstablishment'] ?: 0,
            'applyHeat'       => $keywords['applyHeat'] ?: 0,
        ];

        //判断求职者权限
        $resumeId = BaseMember::getMainId($memberId);
        if (ResumeEquity::checkEquity($resumeId, ResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
            unset($keywords['isEstablishment'], $keywords['applyHeat']);
        }

        $announcementList = BaseAnnouncement::changeSecondColumnJobList($keywords);
        $list             = $this->renderPartial('column-module/latest_job_classification_list.html', [
            'list' => $announcementList['list'],
        ]);

        return $this->success([
            'list'       => $list,
            'jobAccount' => $announcementList['count'],
        ]);
    }

    /**
     * 切换最新公告数据（二级栏目）统计
     * @throws Exception
     */
    public function actionChangeSecondColumnAnnouncementListAccount()
    {
        $keywords = Yii::$app->request->get();
        $memberId = Yii::$app->user->id;
        $keywords = [
            'columnId'         => $keywords['columnId'],
            'major_id'         => $keywords['majorId'] ?: '',
            'education_type'   => $keywords['educationType'] ?: '',
            'city_id'          => $keywords['cityId'] ?: '',
            'job_category_id'  => $keywords['jobCategoryId'] ?: '',
            'isEstablishment'  => $keywords['isEstablishment'] ?: '',
            'announcementHeat' => $keywords['announcementHeat'] ?: '',
        ];

        if (Yii::$app->user->isGuest) {
            if ($keywords['major_id'] || $keywords['education_type'] || $keywords['city_id'] || $keywords['job_category_id'] || $keywords['isEstablishment'] || $keywords['announcementHeat']) {
                return $this->notLogin();
            }
        }
        //判断求职者权限
        $resumeId = BaseMember::getMainId($memberId);
        if (ResumeEquity::checkEquity($resumeId, ResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
            unset($keywords['isEstablishment'], $keywords['announcementHeat']);
        }

        $count = BaseAnnouncement::changeSecondColumnAnnouncementListAccount($keywords);

        return $this->success(['count' => $count]);
    }

    /**
     * 切换职位数据（专题栏目B）
     * @throws Exception
     */
    public function actionChangeSpecialColumnJob()
    {
        $keywords = Yii::$app->request->get();
        $memberId = Yii::$app->user->id;
        $keywords = [
            'columnId'         => $keywords['columnId'],
            'key'              => 'latest_announcement',
            'limit'            => 24,
            'major_id'         => $keywords['majorId'] ?: '',
            'education_type'   => $keywords['educationType'] ?: '',
            'city_id'          => $keywords['cityId'] ?: '',
            'job_category_id'  => $keywords['jobCategoryId'] ?: '',
            'page'             => $keywords['page'] ?: 1,
            'isEstablishment'  => $keywords['isEstablishment'] ?: 0,
            'announcementHeat' => $keywords['announcementHeat'] ?: 0,
        ];

        //判断求职者权限
        $resumeId = BaseMember::getMainId($memberId);
        if (ResumeEquity::checkEquity($resumeId, ResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
            unset($keywords['isEstablishment'], $keywords['announcementHeat']);
        }

        $jobList = Announcement::changeSecondColumnJobList($keywords);
        $list    = $this->renderPartial('column-module/latest_job_classification_list.html', [
            'list' => $jobList['list'],
        ]);

        return $this->success([
            'list'       => $list,
            'jobAccount' => $jobList['count'],
        ]);
    }

    /**
     * 切换页面公告数据（专题栏目B）
     * @throws Exception
     */
    public function actionChangeSpecialBAnnouncement()
    {
        // 开大内存限制 开启到2g
        ini_set('memory_limit', '2048M');

        $this->layout = 'column_main';
        $memberId     = Yii::$app->user->id;
        $id           = Yii::$app->request->get('columnId');
        if (!$id) {
            $this->notFound();
        }

        $keywords = Yii::$app->request->get();
        $keywords = [
            'columnId'         => $keywords['columnId'],
            'key'              => 'special_latest_announcement',
            'limit'            => 12,
            'major_id'         => $keywords['majorId'] ?: '',
            'education_type'   => $keywords['educationType'] ?: '',
            'city_id'          => $keywords['cityId'] ?: '',
            'job_category_id'  => $keywords['jobCategoryId'] ?: '',
            'page'             => $keywords['page'] ?: 1,
            'isEstablishment'  => $keywords['isEstablishment'] ?: 0,
            'announcementHeat' => $keywords['announcementHeat'] ?: 0,
        ];

        if (Yii::$app->user->isGuest) {
            if ($keywords['major_id'] || $keywords['education_type'] || $keywords['city_id'] || $keywords['job_category_id'] || $keywords['page'] != 1 || $keywords['isEstablishment'] || $keywords['announcementHeat']) {
                return $this->notLogin();
            }
        }

        //判断求职者权限
        $resumeId = BaseMember::getMainId($memberId);
        if (ResumeEquity::checkEquity($resumeId, ResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
            unset($keywords['isEstablishment'], $keywords['announcementHeat']);
        }

        $allList = Announcement::changeSpecialBAnnouncementList($keywords);

        if ($allList['list']) {
            $list = $this->renderPartial('column-module/' . $keywords['key'] . '.html', ['list' => $allList['list']]);
        } else {
            $list = '';
        }

        return $this->success([
            'list'                 => $list,
            'announcement_account' => $allList['count'],
        ]);
    }

    //    /**
    //     * 切换页面职位数据（政府与事业单位/专题栏目）
    //     * @throws Exception
    //     */
    //    public function actionChangeGovernmentJob()
    //    {
    //        $this->layout = 'column_main';
    //        $id           = Yii::$app->request->get('columnId');
    //        if (!$id) {
    //            $this->notFound();
    //        }
    //
    //        $keywords = Yii::$app->request->get();
    //        $keywords = [
    //            'columnId' => $keywords['columnId'],
    //            'limit'    => 10,
    //            'page'     => $keywords['page'] ?: 1,
    //            'key'      => $keywords['key'],
    //        ];
    //
    //        if ($keywords['key'] == 'recommend') {
    //            $keywords = array_merge($keywords, [
    //                'type' => BaseArticleAttribute::COLUMN_RECOMMEND_ATTRIBUTE,
    //            ]);
    //        }
    //
    //        if ($keywords['key'] == 'hot') {
    //            $keywords = array_merge($keywords, [
    //                'sort_hot' => 1,
    //            ]);
    //        }
    //
    //        $allList = Announcement::getGovernmentColumnJobList($keywords);
    //
    //        if ($allList['list']) {
    //            $list = $this->renderPartial('column-module/government_job_list.html', ['list' => $allList['list']]);
    //        } else {
    //            $list = '';
    //        }
    //
    //        return $this->success([
    //            'list' => $list,
    //        ]);
    //    }

    //    /**
    //     * 切换页面职位数据（政府与事业单位/专题栏目）
    //     * @throws Exception
    //     */
    //    public function actionChangeGovernmentJob()
    //    {
    //        $this->layout = 'column_main';
    //        $id           = Yii::$app->request->get('columnId');
    //        if (!$id) {
    //            $this->notFound();
    //        }
    //
    //        $keywords = Yii::$app->request->get();
    //        $keywords = [
    //            'columnId' => $keywords['columnId'],
    //            'limit'    => 10,
    //            'page'     => $keywords['page'] ?: 1,
    //            'key'      => $keywords['key'],
    //        ];
    //
    //        if ($keywords['key'] == 'recommend') {
    //            $keywords = array_merge($keywords, [
    //                'type' => BaseArticleAttribute::COLUMN_RECOMMEND_ATTRIBUTE,
    //            ]);
    //        }
    //
    //        if ($keywords['key'] == 'hot') {
    //            $keywords = array_merge($keywords, [
    //                'sort_hot' => 1,
    //            ]);
    //        }
    //
    //        $allList = Announcement::getGovernmentColumnJobList($keywords);
    //
    //        if ($allList['list']) {
    //            $list = $this->renderPartial('column-module/government_job_list.html', ['list' => $allList['list']]);
    //        } else {
    //            $list = '';
    //        }
    //
    //        return $this->success([
    //            'list' => $list,
    //        ]);
    //    }

    /**
     * 切换页面职位数据（政府与事业单位/专题栏目）
     * @throws Exception
     */
    public function actionChangeGovernmentJob()
    {
        $this->layout = 'column_main';
        $id           = Yii::$app->request->get('columnId');
        if (!$id) {
            $this->notFound();
        }

        $keywords = Yii::$app->request->get();

        $recommendJobService = new RecommendJobService();
        if ($keywords['key'] == 'hot') {
            $jobList = $recommendJobService->getGovernmentHotJobList($keywords['page'])['list'];
        } else {
            $jobList = $recommendJobService->getGovernmentNewJobList($keywords['page'])['list'];
        }

        if ($jobList) {
            $list = $this->renderPartial('column-module/government_job_list.html', ['list' => $jobList]);
        } else {
            $list = null;
        }

        return $this->success([
            'list' => $list,
        ]);
    }

    /**
     * 切换页面职位数据（通用）
     * @throws Exception
     */
    public function actionChangeColumnJob()
    {
        $this->layout = 'column_main';
        $id           = Yii::$app->request->get('columnId');
        if (!$id) {
            $this->notFound();
        }

        $keywords  = Yii::$app->request->get();
        $className = "list2";

        $service = new RecommendJobService();
        $jobList = $service->getColumnPageJobList($keywords);
        if ($jobList) {
            $list = $this->renderPartial('column-module/right_job_classification_list.html', [
                'list'      => $jobList ?: '',
                'className' => $className,
            ]);

            return $this->success(['list' => $list]);
        } else {
            $msg = '';
            if ($keywords['page'] == 2) {
                $msg = '暂无更多内容，敬请期待';
            }
            $keywords['page'] = 1;
            $jobList          = $service->getColumnPageJobList($keywords);
            $list             = $this->renderPartial('column-module/right_job_classification_list.html', [
                'list'      => $jobList ?: '',
                'className' => $className,

            ]);

            return $this->success([
                'list' => $list,
                'page' => 2,
                'msg'  => $msg,
            ]);
        }
    }

    public function actionFooter()
    {
        return $this->renderPartial('/layouts/footer');
    }
}
