<?php

namespace frontendPc\controllers;

use common\base\models\BaseAgreementConfig;
use common\base\models\BaseSystemConfig;
use Yii;

class AgreementController extends BaseFrontendPcController
{
    public $layout = 'agreement_main';

    /**
     * 增值服务
     * @return string
     */
    public function actionValueAddedServices()
    {
        $type = Yii::$app->request->get('type', BaseAgreementConfig::TYPE_AGREEMENT_VIP);
        //当前显示版本
        $service_agreement = BaseAgreementConfig::getAgreementConfig($type);
        //历史版本
        $history_agreement = BaseAgreementConfig::getHistoryAgreementConfig($type);

        return $this->render('value-added-services.html', [
            'service_agreement' => $service_agreement,
            'history_agreement' => $history_agreement,
        ]);
    }

    /**
     * 增值服务历史
     * @return string
     */
    public function actionValueAddedServicesHistory()
    {
        $type              = Yii::$app->request->get('type', BaseAgreementConfig::TYPE_AGREEMENT_VIP);
        $version           = Yii::$app->request->get('version', 1.0);
        $service_agreement = BaseAgreementConfig::getAgreementConfigByVersion($type, $version);

        return $this->render('value-added-services-history.html', [
            'service_agreement' => $service_agreement,
        ]);
    }
}