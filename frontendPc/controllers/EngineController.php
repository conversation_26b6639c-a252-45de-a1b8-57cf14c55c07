<?php

namespace frontendPc\controllers;

use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeWxBind;
use common\libs\BaiduTimeFactor;
use common\libs\ToutiaoTimeFactor;
use common\libs\WxPublic;
use common\service\CommonService;
use common\service\engine\SearchService;
use Yii;

class EngineController extends BaseFrontendPcController
{
    /**
     * 搜索引擎-职位中心页面
     */
    public function actionIndex()
    {
        // 暂时返回状态码
        // return $this->badPage();
        //重定向 后缀带/ ? 会被去掉
        $url = urldecode(Yii::$app->request->url);
        $ip  = Yii::$app->request->userIP;

        // ban 掉 43.130.x.x 43.134.x.x 43.133.x.x  43.153.x.x  43.135.x.x 43.159.x.x  14.155.x.x 14.153.x.x
        if (strpos($ip, '43.130.') === 0 || strpos($ip, '43.134.') === 0 || strpos($ip, '43.133.') === 0 || strpos($ip,
                '43.153.') === 0 || strpos($ip, '43.135.') === 0 || strpos($ip, '43.159.') === 0 || strpos($ip,
                '14.155.') || strpos($ip, '14.153.')) {
            // 抛出个500
            return $this->badPage();
        }

        // 这里爬虫比较厉害，http://gaoxiaojob.dong/rczhaopin/p20，获取p的页码，如果大于20并且未登录，就去登录页
        $p = Yii::$app->request->get('p');
        // 去掉p,留下页码
        if ($p) {
            $page = str_replace('p', '', $p);
            if ($page > 20 && Yii::$app->user->isGuest) {
                return $this->redirect('/member/person/home', 301);
            }
        }

        //输出一下特殊的url日志
        //tj_综合服务? 含有tj_ 且含有?的url
        //        if (strpos($url, 'tj_') !== false && strpos($url, '?') !== false) {
        //            //写一条日志 将访问人的ip和url以及ua记录下来
        //            $log = [
        //                'ip'   => Yii::$app->request->userIP,
        //                'url'  => $url,
        //                'ua'   => Yii::$app->request->userAgent,
        //                'type' => 'PC',
        //            ];
        //            Yii::info($log, 'bPageUrl');
        //        }
        if (strpos($url, '?') !== false) {
            $url = strstr($url, '?', true);
            $url = rtrim($url, '/');
            $this->redirect($url, 301);
        }
        if ($url != rtrim($url, '/')) {
            //去掉？后面的参数
            $url = rtrim($url, '/');
            $this->redirect($url, 301);
        }
        //获取筛选条件
        $search_service_model = new SearchService();
        $data                 = $search_service_model->setPlatform(CommonService::PLATFORM_WEB)
            ->run();
        //设置当前页面的seo信息
        $this->setSeo([
            'title'       => $data['seo_data']['title'],
            'keywords'    => $data['seo_data']['keywords'],
            'description' => $data['seo_data']['description'],
        ]);

        BaiduTimeFactor::createRczhaopin();
        ToutiaoTimeFactor::createRczhaopin();

        $isLogin = !Yii::$app->user->isGuest;

        //判断是否要显示绑定二维码
        $isShowBindCode = true;
        if ($isLogin) {
            $resumeId   = BaseMember::getMainId(Yii::$app->user->id);
            $wxBindInfo = BaseResumeWxBind::findOne(['resume_id' => $resumeId]);
            if (!empty($wxBindInfo['unionid'])) {
                $isShowBindCode = false;
            }
        }
        // if ($isShowBindCode) {
        //     $bindCodeUrl = WxPublic::getInstance(BaseMember::TYPE_PERSON)
        //                        ->createBindQrCode($resumeId)['url'];
        // }

        return $this->render('index.html', [
            'data'        => $data,
            'isLogin'     => $isLogin,
            'bindCodeUrl' => $bindCodeUrl ?: '',
            'isVip'       => $isLogin && BaseResume::checkVip(Yii::$app->user->id),
        ]);
    }
}