<?php

namespace frontendPc\controllers;

use common\base\models\BaseCompanyMemberWxBind;
use common\base\models\BaseCompanyMemberWxBindCallbackLog;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResumeWxBind;
use common\base\models\BaseWxPublicCallbackLog;
use common\base\models\BaseResumeEquity;
use common\base\models\BaseResumeEquitySetting;
use common\helpers\DebugHelper;
use common\helpers\StringHelper;
use common\helpers\ValidateHelper;
use common\libs\Cache;
use common\libs\WxPublic;
use common\models\ResumeWxBind;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Messages\Text;
use Yii;
use yii\base\Exception;

class WxController extends BaseFrontendPcController
{

    public function actionCallback()
    {
        $config = \Yii::$app->params['wx']['personPublic'];

        $app = Factory::officialAccount($config);

        $app->server->push(function ($message) {
            return $this->handelMessage($message);
        });

        $response = $app->server->serve();

        $response->send();

        exit;
    }

    public function actionCompanyCallback()
    {
        $config = \Yii::$app->params['wx']['companyPublic'];

        $app = Factory::officialAccount($config);

        $app->server->push(function ($message) {
            // // 这里做一下限制
            $openid = $message['FromUserName'];

            return $this->CompanyHandelMessage($message);
        });

        $response = $app->server->serve();

        $response->send();

        exit;
    }

    public function actionRegisterBind()
    {
        // 这里承接了两个功能，一个是出页面,一个是校验,一个get一个是post
        $token = Yii::$app->request->get('token');

        // 判断来源referer
        $referer = Yii::$app->request->getReferrer();
        // 当前host
        $host = Yii::$app->request->hostInfo;

        try {
            $urlReferer = parse_url($referer)['host'];
            $urlHost    = parse_url($host)['host'];
            if ($urlReferer != $urlHost) {
                return $this->redirect('/');
            }
        } catch (\Exception $e) {
            // 回到首页
            return $this->redirect('/');
        }

        $this->layout = 'bind_main';
        try {
            if (!$token) {
                throw new \Exception('token不存在');
            }

            $loginForm = new BaseMemberLoginForm();
            $data      = $loginForm->getWxScanRegisterDataByToken($token);
            $status    = $data['status'];
            $openid    = $data['openid'];

            if ($status != 1) {
                // 有可能已经被绑定使用过了
                throw new Exception('验证码已失效');
            }

            // 找openid,看看这个用户是否已经绑定了,又或者这个openid不存在
            $resumeWxBind = BaseResumeWxBind::findOne(['openid' => $openid]);

            if (!$resumeWxBind) {
                throw new Exception('微信不存在');
            }
            if ($resumeWxBind['resume_id']) {
                throw new Exception('该微信已经绑定过手机号');
            }

            // 出页面
            return $this->render('/bind/mobile.html');
        } catch (\Exception $e) {
            return $this->redirect('/');
        }
    }

    /**
     * @param $key
     * @param $user BaseResumeWxBind
     */
    private function handelMessage($message)
    {
        BaseWxPublicCallbackLog::log($message);

        $msgType         = $message['MsgType'];
        $openid          = $message['FromUserName'];
        $ticket          = $message['Ticket'];
        $eventKey        = $message['EventKey'];
        $sendBindMessage = false;
        // 将它拿出来-去创建一个openid的数据关系
        $wx     = WxPublic::getInstance(WxPublic::TYPE_RESUME);
        $wxUser = $wx->getUserInfo($openid);
        if ($openid) {
            try {
                // 考虑到取消订阅还写数据会导致数据问题，所以取消订阅就不创建用户了
                if ($message['Event'] != 'unsubscribe') {
                    BaseResumeWxBind::create($openid, $wxUser);
                }
            } catch (Exception $e) {

            }
        }
        switch ($msgType) {
            case 'event':
                $event = $message['Event'];
                switch ($event) {
                    case 'subscribe':
                        $txt = '';
                        if (!in_array($eventKey, WxPublic::SCAN_SUBSCRIBE_NOT_SEND_KEY_LIST)) {
                            $txt = WxPublic::getSubscribeText();
                        }
                        $user = ResumeWxBind::findOne(['openid' => $openid]);
                        if (!$user->resume_id) {
                            if (strpos($eventKey, WxPublic::QR_CODE_RESUME_BIND_KEY) == false) {
                                $sendBindMessage = true;
                            }
                        }

                        break;
                    case 'unsubscribe':
                        //
                        BaseResumeWxBind::unSubscribe($openid);
                        break;
                    case 'CLICK':
                        // 点击事件
                        switch ($eventKey) {
                            case 'IN_GROUP':
                                // 回复一个图片
                                return $wx->sendWxMaterial($eventKey);
                            default:
                                $txt = "你点击了其他";
                                break;
                        }

                        return $txt;
                    case 'SCAN':

                        break;
                    default:
                        $txt = '收到事件消息';
                        break;
                }
                break;
            case 'text':
                /**
                 * 【其他回复】
                 * 已收到您的消息，若对高校人才网有任何疑问或意见，欢迎留言，我们将尽快回复~
                 *
                 * 【注册】【登录】
                 * 扫码登录请回到网页继续操作；或直接点击链接【http://m.gaoxiaojob.com/code-login】进行注册登录
                 *
                 * 【招聘】
                 * 高校人才网最新招聘信息【点击查看】http://m.gaoxiaojob.com
                 *
                 * 更多分类：
                 * - 高校招聘 请点击>>http://m.gaoxiaojob.com/column/1.html
                 * - 中小学招聘 请点击>>http://m.gaoxiaojob.com/column/2.html
                 * - 医学类招聘 请点击>>http://m.gaoxiaojob.com/column/5.html
                 * - 企业招聘 请点击>>http://m.gaoxiaojob.com/column/6.html
                 * - 科研机构招聘 请点击>>http://m.gaoxiaojob.com/column/3.html
                 * - 博士后招聘 请点击>>http://m.gaoxiaojob.com/column/7.html
                 * - 海归招聘 请点击>>http://m.gaoxiaojob.com/column/8.html
                 */

                if (strpos($message['Content'], '注册') !== false || strpos($message['Content'], '登录') !== false) {
                    $txt = "扫码登录请回到网页继续操作；或直接点击链接【http://m.gaoxiaojob.com/code-login】进行注册登录";
                    break;
                }

                if (strpos($message['Content'], '招聘') !== false) {
                    $txt = "高校人才网最新招聘信息<a href='http://m.gaoxiaojob.com'>【点击查看】</a>" . PHP_EOL;
                    $txt .= "更多分类：" . PHP_EOL;
                    $txt .= "- 高校招聘 <a href='https://m.gaoxiaojob.com/column/1.html'>请点击>></a>" . PHP_EOL;
                    $txt .= "- 中小学招聘 <a href='https://m.gaoxiaojob.com/column/2.html'>请点击>></a>" . PHP_EOL;
                    $txt .= "- 医学类招聘 <a href='https://m.gaoxiaojob.com/column/5.html'>请点击>></a>" . PHP_EOL;
                    $txt .= "- 企业招聘 <a href='https://m.gaoxiaojob.com/column/6.html'>请点击>></a>" . PHP_EOL;
                    $txt .= "- 科研机构招聘 <a href='https://m.gaoxiaojob.com/column/3.html'>请点击>></a>" . PHP_EOL;
                    $txt .= "- 博士后招聘 <a href='https://m.gaoxiaojob.com/column/7.html'>请点击>></a>" . PHP_EOL;
                    $txt .= "- 海归招聘 <a href='https://m.gaoxiaojob.com/column/8.html'>请点击>></a>" . PHP_EOL;
                    break;
                }

                // 处理获取求职资源包
                if (trim($message['Content']) == '求职') {
                    // 当前用户对应的简历id
                    $resumeId = BaseResumeWxBind::findOneVal(['openid' => $openid], 'resume_id');
                    // 校验权益(过期会员也可以用)
                    if (BaseResumeEquity::checkEquity($resumeId, BaseResumeEquitySetting::ID_JOB_RESOURCES,
                            null) !== false) {
                        $txt = "您好！【高才VIP专属求职资源包】领取方式如下：" . PHP_EOL;
                        $txt .= "链接：<a href='https://www.aliyundrive.com/s/DNYikCHkiMR'>https://www.aliyundrive.com/s/DNYikCHkiMR</a>" . PHP_EOL;
                        $txt .= "提取码：yk46" . PHP_EOL;
                    } else {
                        $txt = "抱歉，您尚未开通高才VIP会员哦～点击 👉🏻<a href='https://m.gaoxiaojob.com/vip.html'>开通高才VIP</a>，享受11大求职权益，即可领取专属超豪华求职资源包！";
                    }

                    break;
                }

                // 处理“会员课程”口令
                if (trim($message['Content']) == '会员课程') {
                    $txt = "【高才优课】课程领取方式：" . PHP_EOL;
                    $txt .= "1、请回复您购买的“钻石VIP”或“黄金VIP*180天”的订单号、手机号;" . PHP_EOL;
                    $txt .= "2、我们核实订单信息后，将在服务号发送课程领取地址，请耐心等待！" . PHP_EOL;

                    break;
                }

                // 处理“会员课程”口令
                if (trim($message['Content']) == '人才报告') {
                    $txt = "您已成功解锁《高校人才网_2023硕博人才就业趋势与发展报告》！领取方式如下↓" . PHP_EOL;
                    $txt .= "链接：https://www.aliyundrive.com/s/LK2k8JnQqQx" . PHP_EOL;
                    $txt .= "提取码: by36" . PHP_EOL;

                    break;
                }

                // $txt = "已收到您的消息，若对高校人才网有任何疑问或意见，欢迎留言，我们将尽快回复~" . PHP_EOL;
                // $txt .= "【注册】【登录】" . PHP_EOL;
                // $txt .= "扫码登录请回到网页继续操作；或直接点击链接【http://m.gaoxiaojob.com/code-login】进行注册登录" . PHP_EOL;
                // $txt .= "【招聘】" . PHP_EOL;

                // $txt = "高校人才网最新招聘信息<a href='http://m.gaoxiaojob.com'>【点击查看】</a>" . PHP_EOL;
                // $txt .= "更多分类：" . PHP_EOL;
                // $txt .= "- 高校招聘 请点击>>http://m.gaoxiaojob.com/column/1.html" . PHP_EOL;
                // $txt .= "- 中小学招聘 请点击>>http://m.gaoxiaojob.com/column/2.html" . PHP_EOL;
                // $txt .= "- 医学类招聘 请点击>>http://m.gaoxiaojob.com/column/5.html" . PHP_EOL;
                // $txt .= "- 企业招聘 请点击>>http://m.gaoxiaojob.com/column/6.html" . PHP_EOL;
                // $txt .= "- 科研机构招聘 请点击>>http://m.gaoxiaojob.com/column/3.html" . PHP_EOL;
                // $txt .= "- 博士后招聘 请点击>>http://m.gaoxiaojob.com/column/7.html" . PHP_EOL;
                // $txt .= "- 海归招聘 请点击>>http://m.gaoxiaojob.com/column/8.html" . PHP_EOL;

                // $txt = '已收到您的消息，若对高校人才网有任何疑问或意见，欢迎留言，我们将尽快回复~' . PHP_EOL;
                // $txt .= " 您或许想知道——" . PHP_EOL;
                // $txt .= " （1）每日招聘更新 请点击>>http://m.gaoxiaojob.com/daily.html" . PHP_EOL;
                // $txt .= " （2）查看投递记录 请点击>>http://m.gaoxiaojob.com/person:" . PHP_EOL;
                // $txt .= " （3）查看近期热文 请点击>>https://mp.weixin.qq.com/mp/homepage?__biz=Mzg4MzgzNDgzNw==&hid=1&sn=37c92f046967aa911169a405110c9bb9" . PHP_EOL;
                // $txt .= " （4）求职功能介绍 请点击>>https://mp.weixin.qq.com/mp/appmsgalbum?__biz=Mzg4MzgzNDgzNw==&action=getalbum&album_id=2751257578231382017#wechat_redirect" . PHP_EOL;

                $txt = '已收到您发来的消息~欢迎留言，我们将尽快回复！如遇紧急问题或咨询合作事宜，可联系：15920573323（微信同号）' . PHP_EOL;
                $txt .= PHP_EOL;
                $txt .= " 您或许想知道——" . PHP_EOL;
                $txt .= " 1.近期热门招聘信息 <a href='https://mp.weixin.qq.com/mp/appmsgalbum?__biz=Mzg4MzgzNDgzNw==&action=getalbum&album_id=2939572988453126147#wechat_redirect'>请点击</a>" . PHP_EOL;
                $txt .= " 2.高才VIP使用指南 <a href='https://mp.weixin.qq.com/mp/appmsgalbum?__biz=Mzg4MzgzNDgzNw==&action=getalbum&album_id=3522302526928224258#wechat_redirect'>请点击</a>" . PHP_EOL;
                $txt .= " 3.高校求职经验分享 <a href='https://mp.weixin.qq.com/mp/appmsgalbum?__biz=Mzg4MzgzNDgzNw==&action=getalbum&album_id=2939566505384542210#wechat_redirect'>请点击</a>" . PHP_EOL;
                $txt .= " 4.硕博就业交流 <a href='https://h5.clewm.net/?url=qr61.cn%2FoaZcT1%2Fqac5BaF&hasredirect=1'>请点击</a>" . PHP_EOL;

                break;
            default :
                $txt = '';
                break;
        }

        if ($eventKey) {
            try {
                $wx->parseQrKey($eventKey, $openid, $ticket, $message['Event']);
            } catch (\Exception $e) {
            }
        }

        if ($sendBindMessage) {
            $config            = \Yii::$app->params['wx']['personPublic'];
            $bindWxText        = WxPublic::getBindWxText();
            $bindWxTextContent = new Text($bindWxText);
            $app               = Factory::officialAccount($config);
            $app->customer_service->message($bindWxTextContent)
                ->to($openid)
                ->send();
        }

        return $txt;
        // // 暂时只处理订阅,取消订阅,扫码先
        // switch ($msgType) {
        //     case 'event':
        //         $event = $message['Event'];
        //         switch ($event) {
        //             case 'subscribe':
        //
        //                 return "英才你好，欢迎来到高校人才网~关注我，拿offer，你懂的！🎉";
        //             case 'unsubscribe':
        //                 //
        //                 BaseResumeWxBind::unSubscribe($openid);
        //                 break;
        //             case 'SCAN':
        //                 break;
        //             default:
        //                 return '收到事件消息';
        //         }
        //         break;
        //     default :
        //         return '收到其它消息';
        // }
    }

    /**
     * 单位公众号处理回调
     * @param $message
     * @return \EasyWeChat\Kernel\Messages\Image|mixed|string
     */
    private function companyHandelMessage($message)
    {
        BaseCompanyMemberWxBindCallbackLog::log($message);

        $msgType  = $message['MsgType'];
        $openid   = $message['FromUserName'];
        $ticket   = $message['Ticket'];
        $eventKey = $message['EventKey'];
        $txt      = '';
        // 将它拿出来-去创建一个openid的数据关系-已经去掉parseQrKey方法中的写入逻辑
        $wx = WxPublic::getInstance(WxPublic::TYPE_COMPANY);
        if ($openid) {
            try {
                $wxUser = $wx->getUserInfo($openid);
                BaseCompanyMemberWxBind::create($openid, $wxUser);
            } catch (Exception $e) {

            }
        }
        switch ($msgType) {
            case 'event':
                $event = $message['Event'];
                switch ($event) {
                    case 'subscribe':
                        $txt = '您好，欢迎来到高校人才网~';
                        break;
                    case 'unsubscribe':
                        //取消关注
                        BaseCompanyMemberWxBind::unSubscribe($openid);
                        break;
                    case 'CLICK':
                        //                        // 点击事件
                        //                        switch ($eventKey) {
                        //                            case 'IN_GROUP':
                        //                                // 回复一个图片
                        //                                return $wx->sendWxMaterial($eventKey);
                        //                            default:
                        //                                $txt = "你点击了其他";
                        //                                break;
                        //                        }

                        //                        return $txt;
                        break;
                    case 'SCAN':
                        break;
                    default:
                        //                        $txt = '收到事件消息';
                        break;
                }
                break;
            //            case 'text':
            //                break;
            default :
                $txt = '收到其他信息';
                break;
        }

        if ($eventKey) {
            try {
                $txt = $wx->parseQrKey($eventKey, $openid, $ticket);
            } catch (\Exception $e) {
            }
        }

        return $txt;
    }
}
