<?php

namespace frontendPc\controllers;

use common\base\models\BaseActivity;
use common\base\models\BaseActivityForm;
use common\base\models\BaseActivityFormRegistrationForm;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseDictionary;
use common\base\models\BaseMajor;
use common\base\models\BaseResume;
use common\helpers\ArrayHelper;
use Yii;
use yii\base\Exception;

class ActivityFormController extends BaseFrontendPcController
{
    /**
     * 获取详情页面信息
     * @throws Exception
     * @throws \Exception
     */
    public function actionGetDetail()
    {
        $memberId = $this->isResumeLogin() ?: 0;

        $this->layout = 'column_main';

        $model = BaseActivity::findOne(['token' => Yii::$app->request->get('token')]);
        if (!$model) {
            $this->fail('token错误');
        }
        $info                    = BaseActivityForm::getActivityFormInfo($model->id, $memberId);
        $info['showMessageList'] = BaseActivityForm::sortShowMessageList($info['showMessageList']);

        //简历前三步信息
        $activityFormResumeInfo = BaseResume::getActivityFormResumeInfo($memberId, $info['id']);
        $data                   = array_merge($info, [
            'resumeStep'    => $activityFormResumeInfo['resumeStep'],
            'baseInfo'      => $activityFormResumeInfo['baseInfo'],
            'intentionList' => $activityFormResumeInfo['intentionList'],
            'educationList' => $activityFormResumeInfo['educationList'],
            'parameter'     => [
                'allCityAreaList'         => BaseArea::getNativeAreaList(),
                'politicalList'           => ArrayHelper::obj2Arr(BaseDictionary::getPoliticalStatusList()),
                'titleList'               => BaseDictionary::getTitleList(),
                'educationList'           => ArrayHelper::obj2Arr(BaseDictionary::getEducationList()),
                'majorList'               => BaseMajor::getHierarchyMajorList(),
                'jobStatusList'           => ArrayHelper::obj2Arr(BaseDictionary::getJobStatusList()),
                'categoryJobList'         => BaseCategoryJob::getAllCategoryJobList(),
                'natureList'              => ArrayHelper::obj2Arr(BaseDictionary::getNatureList()),
                'areaList'                => BaseArea::getHierarchyCityList(),
                'wageList'                => ArrayHelper::obj2Arr(BaseDictionary::getWageRangeList()),
                'unitTypeList'            => ArrayHelper::obj2Arr(BaseActivityFormRegistrationForm::UNIT_TYPE_LIST),
                'channelList'             => ArrayHelper::obj2Arr(BaseActivityFormRegistrationForm::CHANNEL_LIST),
                'resumeFileList'          => BaseResume::getActivityResumeFileList($memberId),
                'arriveDateList'          => ArrayHelper::obj2Arr(BaseDictionary::getArriveDateList()),
                'overseasWorkingTimeList' => ArrayHelper::obj2Arr(BaseActivityFormRegistrationForm::OVERSEAS_WORKING_TIME_LIST),
                'employmentStatusList'    => ArrayHelper::obj2Arr(BaseActivityFormRegistrationForm::EMPLOYMENT_STATUS_LIST),
            ],
        ]);

        if (!$info) {
            $this->notFound();
        }

        if (!$memberId) {
            $data['isLogin'] = '2';
        } else {
            $data['isLogin'] = '1';
        }

        // 针对未登录用户做一些假数据

        return $this->success([
            'data' => $data,
        ]);
    }
}

