<?php

namespace frontendPc\controllers;

use common\base\models\BaseBuriedPointLog;
use common\base\models\BaseEmailLog;
use common\base\models\BasePayTransformBuriedPointLog;
use common\base\models\BaseResumeEquity;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\helpers\FormatConverter;
use common\libs\Cache;
use frontendPc\models\ShowcaseBrowseLog;
use Yii;
use yii\db\Exception;
use yii\web\Response;

class ShowcaseBrowseLogController extends BaseFrontendPcController
{
    /**
     * 新增广告统计数据
     */
    public function actionAddShowcaseBrowseLog()
    {
        $request = Yii::$app->request->get();
        unset($request['/1.gif']);

        $token = $request['token'];

        if (!$token) {
            $this->response();
        }

        if (Yii::$app->params['showcaseBrowse']['token'] != $token) {
            $this->response();
        }

        $useragent = Yii::$app->request->headers['user-agent'] ?: '';
        // 这里过滤一下,价格token
        try {
            ShowcaseBrowseLog::addShowcaseBrowseLog(FormatConverter::convertHump($request), $useragent);

            $this->response();
        } catch (\Exception $e) {
            $this->response();
        }
    }

    /**
     * 新增用户埋点数据日志
     * @return void
     */
    public function actionAddBuriedPointLog()
    {
        $request = Yii::$app->request->get();
        unset($request['/2.gif']);

        try {
            BaseBuriedPointLog::create($request);

            $this->response();
        } catch (Exception $e) {
            $this->response();
        }
    }

    /**
     * 更新VIP页面浏览Log
     * @return void
     */
    public function actionUpdateVipViewPointLog()
    {
        $uuid = Yii::$app->request->get('uuid');
        if ($uuid <= 0) {
            $this->success();
        }
        (new BasePayTransformBuriedPointLog)->setActionId(BasePayTransformBuriedPointLog::ACTION_ID_PC_VIP_VIEW)
            ->updateLog($uuid);
        $this->success();
    }

    /**
     * 添加VIP页面tab切换log
     * @return void
     */
    public function actionVipTabPointLog()
    {
        $uuid    = Yii::$app->request->get('uuid');
        $type    = Yii::$app->request->get('type');
        $tabName = $type == 1 ? BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP_TEXT : BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP_TEXT;
        if ($uuid <= 0) {
            $this->success();
        }
        (new BasePayTransformBuriedPointLog)->setActionId(BasePayTransformBuriedPointLog::ACTION_ID_PC_VIP_TAB_CHANGE)
            ->setPlatform(BasePayTransformBuriedPointLog::PLATFORM_TYPE_PC)
            ->setActionType(BasePayTransformBuriedPointLog::ACTION_TYPE_CLICK)
            ->setUuid($uuid)
            ->setEventParams([
                'tabName' => $tabName,
            ])
            ->createLog();
        $this->success();
    }

    /**
     * 更新洞察页面浏览Log
     * @return void
     */
    public function actionUpdateCompetitivePowerViewPointLog()
    {
        $uuid = Yii::$app->request->get('uuid');
        if ($uuid <= 0) {
            $this->success();
        }
        (new BasePayTransformBuriedPointLog)->setActionId(BasePayTransformBuriedPointLog::ACTION_ID_PC_INSIGHT_VIEW)
            ->updateLog($uuid);
        $this->success();
    }

    /**
     * 更新求职快页面浏览Log
     * @return void
     */
    public function actionUpdateJobFastViewPointLog()
    {
        $uuid = Yii::$app->request->get('uuid');
        if ($uuid <= 0) {
            $this->success();
        }
        (new BasePayTransformBuriedPointLog)->setActionId(BasePayTransformBuriedPointLog::ACTION_ID_PC_HUNT_JOB_VIEW)
            ->updateLog($uuid);
        $this->success();
    }

    public function actionAddEmailReadedLog()
    {
        $request = Yii::$app->request->get();
        unset($request['/e.gif']);

        try {
            // 先用一个key保存下来
            $key   = Cache::ALL_EMAIL_READED_KEY;
            $time  = CUR_TIMESTAMP;
            $value = json_encode($request);
            // 写集合
            Cache::zadd($key, $time, $value);
            $emailId = $request['emailId'];
            BaseEmailLog::read(['emailId' => $emailId]);

            $this->response();
        } catch (\Exception $e) {
            $this->response();
        }

        // // 先用一个key保存下来
        // $key   = Cache::ALL_EMAIL_READED_KEY;
        // $time  = CUR_TIMESTAMP;
        // $value = json_encode($request);
        // // 写集合
        // Cache::zadd($key, $time, $value);
    }

    private function response()
    {
        if (Yii::$app->has('response')) {
            $response          = Yii::$app->getResponse();
            $response->isSent  = false;
            $response->stream  = null;
            $response->data    = null;
            $response->content = null;
        } else {
            $response = new Response();
        }
        $response->statusCode = 200;
        $response->format     = Response::FORMAT_JSON;

        $response->send();
        exit;
    }
}