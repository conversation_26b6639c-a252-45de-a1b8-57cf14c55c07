<?php
/**
 * create user：shannon
 * create time：2024/3/1 10:42
 */
namespace frontendPc\controllers;

use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseResume;
use common\base\models\BaseSeoHotWordConfig;
use common\base\models\BaseSeoJobWiki;
use common\base\models\BaseShowcase;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\FormatConverter;
use common\helpers\UrlHelper;
use common\libs\BaiduTimeFactor;
use common\libs\Cache;
use common\libs\ToutiaoTimeFactor;
use common\service\search\PcJobListService;
use common\service\seo\SeoJobWikiService;
use Yii;

class SeoJobWikiController extends BaseFrontendPcController
{
    public function actionIndex()
    {
        try {
            $url = urldecode(Yii::$app->request->url);

            if ($url != rtrim($url, '/')) {
                //去掉？后面的参数
                $url = rtrim($url, '/');
                $this->redirect($url, 301);
            }
            $code      = Yii::$app->request->get('code');
            $isRefresh = Yii::$app->request->get('isRefresh', 0);
            $data      = (new SeoJobWikiService())->setPlatform(PLATFORM)
                ->getJobWikiDetailByCache($code, $isRefresh);
            $data      = FormatConverter::convertLine($data);

            $keyword            = $data['detail']['keyword'];
            $title              = "{$keyword}是干什么的_{$keyword}招聘要求有哪些-高校人才网|高才网";
            $keywords           = "{$keyword}是干什么的,{$keyword}岗位职责,{$keyword}招聘要求";
            $descriptionContent = "【高校人才网】{$keyword}职位百科专题页，为您提供{$keyword}是干什么的、{$keyword}岗位职责、{$keyword}招聘要求、{$keyword}相关招聘信息等内容，为您选择{$keyword}工作提供有价值的参考。";
            $this->setSeo([
                'title'       => $title,
                'keywords'    => $keywords,
                'description' => $descriptionContent,
            ]);

            return $this->render('/seo-job-wiki/index.html', $data);
        } catch (\Exception $e) {
            $this->notFound();
        }
    }
}