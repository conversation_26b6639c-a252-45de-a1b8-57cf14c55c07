<?php

namespace frontendPc\controllers;

use common\base\models\BaseDailyAnnouncementSummary;
use common\helpers\TimeHelper;
use frontendPc\models\DailyAnnouncementSummary;
use frontendPc\models\HomePosition;
use Yii;

class DailyAnnouncementSummaryController extends BaseFrontendPcController
{
    public function actionDetail()
    {
        $this->layout = 'column_main';
        $id           = Yii::$app->request->get('id');
        if (!$id) {
            $this->notFound();
        }
        try {
            $detail = DailyAnnouncementSummary::getDetail($id);

            // 这里替换一下域名,如果存在旧的域名就换成新的
            $old = 'gcjob.new.gaoxiaojob.com';
            $new = Yii::$app->params['pcHost'];

            $detail['content'] = str_replace($old, $new, $detail['content']);

            // https换成http
            $detail['content'] = str_replace('https://', 'http://', $detail['content']);
            $seoTitle          = str_replace('【标题】', $detail['title'],
                Yii::$app->params['seo']['dailyDetail']['title']);
            $this->setSeo([
                'title' => $seoTitle,
            ]);

            if ($detail['status'] != DailyAnnouncementSummary::STATUS_ACTIVE) {
                $this->notFound();
            }

            $recommendList = HomePosition::getPcDailyRecommend();

            //获取当前每日汇总
            $upAndDownList = DailyAnnouncementSummary::getUpAndDownList($id);

            return $this->render('/daily/detail.html', [
                'content'       => $detail['content'],
                'title'         => $detail['title'],
                'author'        => $detail['author'],
                'date'          => TimeHelper::getChinese($detail['belong_date']),
                'recommendList' => $recommendList,
                'upAndDownList' => $upAndDownList,
            ]);
        } catch (\Exception $e) {
            $this->notFound();
        }
    }

}
