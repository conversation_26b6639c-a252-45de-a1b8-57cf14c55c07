<?php

namespace frontendPc\controllers;

use common\base\models\BaseDictionary;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseResume;
use common\libs\ResumeHandle;
use common\service\match\MatchCompleteService;
use yii\web\Controller;

class PreviewController extends Controller
{
    public function actionMatch()
    {
        if (\Yii::$app->params['environment'] == 'prod') {
            exit('禁止访问');
        }
        $job_id = \Yii::$app->request->get('jobId');
        //获取所有的投递的简历ID
        $resumeIds = BaseJobApplyRecord::find()
            ->select('resume_id')
            ->where(['job_id' => $job_id])
            ->column();
        echo '<hr/>';
        echo "<h4>投递的信息：</h4>";
        echo "投递总数：" . count($resumeIds);
        echo '<br/>';
        echo "<div style='width: 1800px;max-width: 1800px;border: 1px solid red;white-space: normal;word-break: break-word'>投递简历ID(不去重)：" . implode(',',
                $resumeIds) . "</div>";
        echo '<hr/>';
        echo '<hr/>';
        echo '<hr/>';
        echo "<h4>投递记录的匹配度：</h4>";
        $cate_y            = 0;
        $cate_n            = 0;
        $edc_y             = 0;
        $edc_n             = 0;
        $major_y           = 0;
        $major_y_resume_id = '';
        $major_n           = 0;
        $major_n_resume_id = '';
        $major_u           = 0;
        $major_u_resume_id = '';
        $ct_y              = 0;
        $ct_n              = 0;
        $p_y               = 0;
        $p_y_resume_id     = '';
        $p_n               = 0;
        $p_n_resume_id     = '';
        //循环输出对应的匹配度
        foreach ($resumeIds as $resumeId) {
            $service        = new MatchCompleteService();
            $service_result = $service->setRuleKey()
                ->setProject(MatchCompleteService::PROJECT_TYPE_2)
                ->setOparetion(1)
                ->init([
                    'job_id'    => $job_id,
                    'resume_id' => $resumeId,
                ])
                ->run();
            if ($service_result['is_match_job_category'] == 0) {
                $cate_n++;
            } else {
                $cate_y++;
            }
            if ($service_result['is_match_education'] == 0) {
                $edc_n++;
            } else {
                $edc_y++;
            }
            if ($service_result['is_match_major'] == 0) {
                $major_n_resume_id .= $resumeId . ',';
                $major_n++;
            } elseif ($service_result['is_match_major'] == 1) {
                $major_y_resume_id .= $resumeId . ',';
                $major_y++;
            } else {
                $major_u_resume_id .= $resumeId . ',';
                $major_u++;
            }
            if ($service_result['is_match_area_city'] == 0) {
                $ct_n++;
            } else {
                $ct_y++;
            }
            if ($service_result['is_match_area_province'] == 0) {
                $p_n_resume_id .= $resumeId . ',';
                $p_n++;
            } else {
                $p_y_resume_id .= $resumeId . ',';
                $p_y++;
            }
            echo "<div style='width: 1800px;white-space: normal;word-break: break-word'>简历ID为{$resumeId}:   " . json_encode($service_result) . "</div>";
            echo '<hr/>';
        }
        echo '<hr/>';
        echo '<hr/>';
        echo '<hr/>';
        echo "<h4>投递记录的匹配度统计：</h4>";
        echo "匹配职位类别：{$cate_y}个，不匹配职位类别：{$cate_n}个";
        echo '<br/>';
        echo "匹配学历：{$edc_y}个，不匹配学历：{$edc_n}个";
        echo '<br/>';
        echo "匹配专业：{$major_y}个，不匹配专业：{$major_n}个，未填写专业：{$major_u}个";
        echo '<br/>';
        echo "<div style='width: 1800px;white-space: normal;word-break: break-word'>匹配专业的简历ID：{$major_y_resume_id} </div>";
        echo '<br/>';
        echo "<div style='width: 1800px;white-space: normal;word-break: break-word'>不匹配专业的简历ID：{$major_n_resume_id} </div>";
        echo '<br/>';
        echo "<div style='width: 1800px;white-space: normal;word-break: break-word'>未填写专业的简历ID：{$major_u_resume_id} </div>";
        echo '<br/>';
        echo '<br/>';
        echo "匹配城市：{$ct_y}个，不匹配城市：{$ct_n}个";
        echo '<br/>';
        echo "匹配省份：{$p_y}个，不匹配省份：{$p_n}个";
        echo '<br/>';
        $p_y_resume_id = explode(',', $p_y_resume_id);
        sort($p_y_resume_id);
        $p_y_resume_id = implode(',', $p_y_resume_id);
        echo "<div style='width: 1800px;white-space: normal;word-break: break-word'>匹配省份的简历ID：{$p_y_resume_id} </div>";
        echo '<br/>';
        echo "<div style='width: 1800px;white-space: normal;word-break: break-word'>不匹配省份的简历ID：{$p_n_resume_id} </div>";
        die();
    }

    public function actionTitle()
    {
        if (\Yii::$app->params['environment'] == 'prod') {
            exit('禁止访问');
        }
        $job_id = \Yii::$app->request->get('jobId');
        //获取所有的投递的简历ID
        $resumeIds = BaseJobApplyRecord::find()
            ->select('resume_id')
            ->where(['job_id' => $job_id])
            ->column();
        echo "<h1>职称</h1>";
        echo '<hr/>';
        echo "<h2>投递的信息：</h2>";
        echo "投递总数：" . count($resumeIds);
        echo '<br/>';
        echo "<div style='width: 1800px;max-width: 1800px;border: 1px solid red;white-space: normal;word-break: break-word'>投递简历ID(不去重)：" . implode(',',
                $resumeIds) . "</div>";
        echo '<hr/>';
        echo '<hr/>';
        echo '<hr/>';
        echo "<h2>投递记录的职称：</h2>";
        $c  = 0;
        $z  = 0;
        $fg = 0;
        $zg = 0;

        //循环输出对应的匹配度
        foreach ($resumeIds as $resumeId) {
            echo '<div style="border: 1px dashed red"></div>';
            //获取简历信息
            $resume = BaseResume::findOne($resumeId);
            echo "<h4>简历ID为{$resumeId}</h4>";
            if ($resume->title_id) {
                $title_arr = explode(',', $resume->title_id);
                $temp      = [];
                //获取职称信息
                foreach ($title_arr as $title_id) {
                    $title        = BaseDictionary::findOne([
                        'type' => 13,
                        'code' => $title_id,
                    ]);
                    $parent_title = BaseDictionary::findOne([
                        'type' => 13,
                        'id'   => $title->parent_id,
                    ]);
                    if (in_array($parent_title->code, $temp)) { // 去重
                        continue;
                    } else {
                        $temp[] = $parent_title->code;
                    }
                    if ($parent_title->code == 1) {
                        $zg++;
                    } elseif ($parent_title->code == 2) {
                        $fg++;
                    } elseif ($parent_title->code == 3) {
                        $z++;
                    } elseif ($parent_title->code == 4) {
                        $c++;
                    }
                    echo "<div style='width: 1800px;white-space: normal;word-break: break-word'>一级职称ID为：{$parent_title->code}，二级职称ID为： {$title->code}</div>";
                    echo "<div style='width: 1800px;white-space: normal;word-break: break-word'>一级职称名称为：{$parent_title->name}，二级职称名称为： {$title->name}</div>";
                }
            }
        }
        echo '<hr/>';
        echo '<hr/>';
        echo '<hr/>';
        echo "<h4>投递记录的职称统计：</h4>";
        echo "职称为初级：{$c}个，职称为中级：{$z}个，职称为正高级：{$zg}个，职称为副高级：{$fg}个";
        echo '<br/><br/><br/><br/><br/><br/><br/><br/><br/>';
        die();
    }

    public function actionPreview1()
    {
        exit('暂未开放');
        $id    = \Yii::$app->request->get('id');
        $model = new ResumeHandle();
        $model->setView('CVIP_jlmb_00')
            ->setOperator(1, ResumeHandle::OPERATOR_TYPE_SELF)
            ->setData($id)
            ->preview();
    }

    public function actionPreviewOne()
    {
        exit('暂未开放');
        $id    = \Yii::$app->request->get('id');
        $model = new ResumeHandle();
        $model->setView('CVIP_jlmb_01')
            ->setOperator(1, ResumeHandle::OPERATOR_TYPE_SELF)
            ->setData($id)
            ->preview();
    }

    public function actionPreviewTwo()
    {
        exit('暂未开放');
        $id    = \Yii::$app->request->get('id');
        $model = new ResumeHandle();
        $model->setView('CVIP_jlmb_02')
            ->setOperator(1, ResumeHandle::OPERATOR_TYPE_SELF)
            ->setData($id)
            ->preview();
    }

    public function actionPreviewThree()
    {
        exit('暂未开放');
        $id    = \Yii::$app->request->get('id');
        $model = new ResumeHandle();
        $model->setView('CVIP_jlmb_03')
            ->setOperator(1, ResumeHandle::OPERATOR_TYPE_SELF)
            ->setData($id)
            ->preview();
    }

}