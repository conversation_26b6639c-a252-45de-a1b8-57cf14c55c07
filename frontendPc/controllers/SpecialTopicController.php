<?php

namespace frontendPc\controllers;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\libs\Cache;
use Faker\Provider\Base;
use UserAgentParser\Provider\WhichBrowser;
use Yii;

/**
 * 这里一整套页面是用于专题页嵌入到系统里面使用的,所以这个控制器里面会有各种逻辑甚至常量配置等等,其他控制器不建议采取这种方式
 */
class SpecialTopicController extends BaseFrontendPcController
{

    public $layout   = false;
    public $baseFile;
    public $webFile;
    public $params   = [];
    public $key;
    public $isUpdate = false;
    public $config;

    const YCA_SPRINGSTUDENT2023_KEY      = 'yca_springstudent2023';
    const YCA_SPRINGSTUDENT2023_FDY_KEY  = 'yca_springstudent2023_fdy';
    const YCA_SPRINGSTUDENT2023_ZRJS_KEY = 'yca_springstudent2023_zrjs';
    const YCA_SPRINGSTUDENT2023_YXS_KEY  = 'yca_springstudent2023_yxs';

    const CACHE_KEY = Cache::PC_HOME_ZT_KEY;

    // 配置文件,这里的key需要和静态项目里面的文件夹保持一致,方便查找
    const CONFIG = [
        self::YCA_SPRINGSTUDENT2023_KEY      => [
            'key'     => self::YCA_SPRINGSTUDENT2023_KEY,
            'isCache' => false,
        ],
        self::YCA_SPRINGSTUDENT2023_FDY_KEY  => [
            'key'     => self::YCA_SPRINGSTUDENT2023_FDY_KEY,
            'isCache' => false,
        ],
        self::YCA_SPRINGSTUDENT2023_ZRJS_KEY => [
            'key'     => self::YCA_SPRINGSTUDENT2023_ZRJS_KEY,
            'isCache' => false,
        ],
        self::YCA_SPRINGSTUDENT2023_YXS_KEY  => [
            'key'     => self::YCA_SPRINGSTUDENT2023_YXS_KEY,
            'isCache' => false,
        ],
    ];

    public function actionIndex()
    {
        $key      = \Yii::$app->request->get('key');
        $isUpdate = \Yii::$app->request->get('update');

        if (empty($key)) {
            $this->notFound();
        }
        $config = self::CONFIG[$key] ?? [];
        if (empty($config)) {
            $this->notFound();
        }

        $this->config = $config;

        $this->key = $key;

        if ($config['isCache'] && $isUpdate) {
            $this->isUpdate = true;
        }

        switch ($key) {
            case self::YCA_SPRINGSTUDENT2023_KEY:
                $this->ycaSpringstudent2023();
                break;
            case self::YCA_SPRINGSTUDENT2023_FDY_KEY:
                $this->ycaSpringstudent2023Fdy();
                break;
            case self::YCA_SPRINGSTUDENT2023_ZRJS_KEY:
                $this->ycaSpringstudent2023Zrjs();
                break;
            case self::YCA_SPRINGSTUDENT2023_YXS_KEY:
                $this->ycaSpringstudent2023Yxs();
                break;
            default:
                $this->notFound();
        }

        $this->webFile = '/zt/' . $this->key;

        $params = array_merge(['webFile' => $this->webFile], $this->params);

        return $this->render('@frontendPc/web/zt/' . $this->key . '/index.html', $params);
    }

    private function ycaSpringstudent2023()
    {
        $ua     = Yii::$app->request->headers['user-agent'];
        $result = (new WhichBrowser())->parse($ua);

        // 如果是移动端,就跳转
        if ($result->isMobile()) {
            $this->redirect('//m.gaoxiaojob.com/zhaopin/zt/yca_springstudent2023_m/index.html');
        }

        // 这里面就可以处理这个页面所需逻辑了

        // $view = $this->getViewFile();
        //
        // echo  $this->renderFile($view, ['webFile' => $this->webFile]);
        // exit;
        // 需要拿很多的公告,并且分四个大类,现在先拿随机拿几个吧

        // 这个页面如果不需要缓存,就直接拿数据
        $cacheKey = self::CACHE_KEY . ':' . $this->key;
        if ($this->config['isCache']) {
            // 需要缓存的情况下,还要判断这次请求是否是要更新缓存的
            if (!$this->isUpdate) {
                if ($data = Cache::get($cacheKey)) {
                    $this->params = json_decode($data, true);

                    return;
                }
            }
        }

        /**
         *2.公告名称 调用公告名称，2行展示，超字...
         * 3.职位类型 调用公告下在线职位的职位类型，默认排序，一行展示，超字...
         * 4.学历要求 调用公告下在线职位的学历要求，由高到低（博硕本专），一行展示，超字...
         * 5.需求专业 调用公告下在线职位的需求专业，默认排序，一行展示，超字...
         * （单位名称在最新的设计稿里会删掉，所以这里不做调用） @龚传栋
         */

        // 华东
        $list1 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'e.region'    => BaseArea::REGION_EAST,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_101,
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 华中华南
        $list2 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_101,
                'e.region'    => [
                    BaseArea::REGION_CENTRAL,
                    BaseArea::REGION_SOUTH,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 华北 东北
        $list3 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_101,
                'e.region'    => [
                    BaseArea::REGION_NORTH,
                    BaseArea::REGION_NORTH_EAST,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 西南 西北
        $list4 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_101,
                'e.region'    => [
                    BaseArea::REGION_SOUTH_WEST,
                    BaseArea::REGION_NORTH_WEST,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        foreach ($list1 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list1[$k]         = array_merge($item, $jobList);
            $list1[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list2 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list2[$k]         = array_merge($item, $jobList);
            $list2[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list3 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list3[$k]         = array_merge($item, $jobList);
            $list3[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list4 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list4[$k]         = array_merge($item, $jobList);
            $list4[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }

        // 找list5
        $list5 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_102,
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('100')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 递归找对应的
        foreach ($list5 as $k => $item) {
            $list5[$k]['city'] = BaseAnnouncement::getOnlineNewCityName($item['id']);
        }

        // $list5 分成10个数组,一个数组10个
        $list5 = array_chunk($list5, 10);

        $this->params = [
            'list' => [
                $list1,
                $list2,
                $list3,
                $list4,
                $list5,
            ],
        ];

        // 这里考虑做成缓存?

        Cache::set($cacheKey, json_encode($this->params), 3600);
    }

    private function ycaSpringstudent2023Fdy()
    {
        $ua     = Yii::$app->request->headers['user-agent'];
        $result = (new WhichBrowser())->parse($ua);

        // 如果是移动端,就跳转
        if ($result->isMobile()) {
            $this->redirect('//m.gaoxiaojob.com/zhaopin/zt/yca_springstudent2023_fdy_m/index.html');
        }

        // 这个页面如果不需要缓存,就直接拿数据
        $cacheKey = self::CACHE_KEY . ':' . $this->key;
        if ($this->config['isCache']) {
            // 需要缓存的情况下,还要判断这次请求是否是要更新缓存的
            if (!$this->isUpdate) {
                if ($data = Cache::get($cacheKey)) {
                    $this->params = json_decode($data, true);

                    return;
                }
            }
        }

        /**
         *2.公告名称 调用公告名称，2行展示，超字...
         * 3.职位类型 调用公告下在线职位的职位类型，默认排序，一行展示，超字...
         * 4.学历要求 调用公告下在线职位的学历要求，由高到低（博硕本专），一行展示，超字...
         * 5.需求专业 调用公告下在线职位的需求专业，默认排序，一行展示，超字...
         * （单位名称在最新的设计稿里会删掉，所以这里不做调用） @龚传栋
         */

        // 华东
        $list1 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'e.region'    => BaseArea::REGION_EAST,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_103,
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 华中华南
        $list2 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_103,
                'e.region'    => [
                    BaseArea::REGION_CENTRAL,
                    BaseArea::REGION_SOUTH,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 华北 东北
        $list3 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_103,
                'e.region'    => [
                    BaseArea::REGION_NORTH,
                    BaseArea::REGION_NORTH_EAST,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 西南 西北
        $list4 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_103,
                'e.region'    => [
                    BaseArea::REGION_SOUTH_WEST,
                    BaseArea::REGION_NORTH_WEST,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        foreach ($list1 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list1[$k]         = array_merge($item, $jobList);
            $list1[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list2 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list2[$k]         = array_merge($item, $jobList);
            $list2[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list3 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list3[$k]         = array_merge($item, $jobList);
            $list3[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list4 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list4[$k]         = array_merge($item, $jobList);
            $list4[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }

        // 找list5
        $list5 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_104,
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('100')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 递归找对应的
        foreach ($list5 as $k => $item) {
            $list5[$k]['city'] = BaseAnnouncement::getOnlineNewCityName($item['id']);
        }

        // $list5 分成10个数组,一个数组10个
        $list5 = array_chunk($list5, 10);

        $this->params = [
            'list' => [
                $list1,
                $list2,
                $list3,
                $list4,
                $list5,
            ],
        ];

        // 这里考虑做成缓存?

        Cache::set($cacheKey, json_encode($this->params), 3600);
    }

    private function ycaSpringstudent2023Zrjs()
    {
        $ua     = Yii::$app->request->headers['user-agent'];
        $result = (new WhichBrowser())->parse($ua);

        // 如果是移动端,就跳转
        if ($result->isMobile()) {
            $this->redirect('//m.gaoxiaojob.com/zhaopin/zt/yca_springstudent2023_zrjs_m/index.html');
        }

        // 这个页面如果不需要缓存,就直接拿数据
        $cacheKey = self::CACHE_KEY . ':' . $this->key;
        if ($this->config['isCache']) {
            // 需要缓存的情况下,还要判断这次请求是否是要更新缓存的
            if (!$this->isUpdate) {
                if ($data = Cache::get($cacheKey)) {
                    $this->params = json_decode($data, true);

                    return;
                }
            }
        }

        /**
         *2.公告名称 调用公告名称，2行展示，超字...
         * 3.职位类型 调用公告下在线职位的职位类型，默认排序，一行展示，超字...
         * 4.学历要求 调用公告下在线职位的学历要求，由高到低（博硕本专），一行展示，超字...
         * 5.需求专业 调用公告下在线职位的需求专业，默认排序，一行展示，超字...
         * （单位名称在最新的设计稿里会删掉，所以这里不做调用） @龚传栋
         */

        // 华东
        $list1 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'e.region'    => BaseArea::REGION_EAST,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_105,
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 华中华南
        $list2 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_105,
                'e.region'    => [
                    BaseArea::REGION_CENTRAL,
                    BaseArea::REGION_SOUTH,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 华北 东北
        $list3 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_105,
                'e.region'    => [
                    BaseArea::REGION_NORTH,
                    BaseArea::REGION_NORTH_EAST,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 西南 西北
        $list4 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_105,
                'e.region'    => [
                    BaseArea::REGION_SOUTH_WEST,
                    BaseArea::REGION_NORTH_WEST,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        foreach ($list1 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list1[$k]         = array_merge($item, $jobList);
            $list1[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list2 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list2[$k]         = array_merge($item, $jobList);
            $list2[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list3 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list3[$k]         = array_merge($item, $jobList);
            $list3[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list4 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list4[$k]         = array_merge($item, $jobList);
            $list4[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }

        // 找list5
        $list5 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_106,
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('100')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 递归找对应的
        foreach ($list5 as $k => $item) {
            $list5[$k]['city'] = BaseAnnouncement::getOnlineNewCityName($item['id']);
        }

        // $list5 分成10个数组,一个数组10个
        $list5 = array_chunk($list5, 10);

        $this->params = [
            'list' => [
                $list1,
                $list2,
                $list3,
                $list4,
                $list5,
            ],
        ];

        // 这里考虑做成缓存?

        Cache::set($cacheKey, json_encode($this->params), 3600);
    }


    private function ycaSpringstudent2023Yxs()
    {
        $ua     = Yii::$app->request->headers['user-agent'];
        $result = (new WhichBrowser())->parse($ua);

        // 如果是移动端,就跳转
        if ($result->isMobile()) {
            $this->redirect('//m.gaoxiaojob.com/zhaopin/zt/yca_springstudent2023_yxs_m/index.html');
        }

        // 这个页面如果不需要缓存,就直接拿数据
        $cacheKey = self::CACHE_KEY . ':' . $this->key;
        if ($this->config['isCache']) {
            // 需要缓存的情况下,还要判断这次请求是否是要更新缓存的
            if (!$this->isUpdate) {
                if ($data = Cache::get($cacheKey)) {
                    $this->params = json_decode($data, true);

                    return;
                }
            }
        }

        /**
         *2.公告名称 调用公告名称，2行展示，超字...
         * 3.职位类型 调用公告下在线职位的职位类型，默认排序，一行展示，超字...
         * 4.学历要求 调用公告下在线职位的学历要求，由高到低（博硕本专），一行展示，超字...
         * 5.需求专业 调用公告下在线职位的需求专业，默认排序，一行展示，超字...
         * （单位名称在最新的设计稿里会删掉，所以这里不做调用） @龚传栋
         */

        // 华东
        $list1 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'e.region'    => BaseArea::REGION_EAST,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_107,
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 华中华南
        $list2 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_107,
                'e.region'    => [
                    BaseArea::REGION_CENTRAL,
                    BaseArea::REGION_SOUTH,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 华北 东北
        $list3 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_107,
                'e.region'    => [
                    BaseArea::REGION_NORTH,
                    BaseArea::REGION_NORTH_EAST,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 西南 西北
        $list4 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['c' => BaseJob::tableName()], 'a.id=c.announcement_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->innerJoin(['e' => BaseArea::tableName()], 'c.city_id=e.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_107,
                'e.region'    => [
                    BaseArea::REGION_SOUTH_WEST,
                    BaseArea::REGION_NORTH_WEST,
                ],
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('1000')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        foreach ($list1 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list1[$k]         = array_merge($item, $jobList);
            $list1[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list2 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list2[$k]         = array_merge($item, $jobList);
            $list2[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list3 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list3[$k]         = array_merge($item, $jobList);
            $list3[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }
        foreach ($list4 as $k => $item) {
            $jobList           = BaseJob::getAnnouncementInfo($item['id']);
            $list4[$k]         = array_merge($item, $jobList);
            $list4[$k]['logo'] = BaseCompany::getLogo($item['company_id']);
        }

        // 找list5
        $list5 = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
            ->innerJoin(['d' => BaseArticleAttribute::tableName()], 'd.article_id=b.id')
            ->where([
                'b.status'    => BaseArticle::STATUS_ONLINE,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'd.type'      => BaseArticleAttribute::ATTRIBUTE_ZT_108,
            ])
            ->select('a.id,a.title,a.company_id')
            ->limit('100')
            ->orderBy('b.refresh_time desc')
            ->asArray()
            ->all();

        // 递归找对应的
        foreach ($list5 as $k => $item) {
            $list5[$k]['city'] = BaseAnnouncement::getOnlineNewCityName($item['id']);
        }

        // $list5 分成10个数组,一个数组10个
        $list5 = array_chunk($list5, 10);

        $this->params = [
            'list' => [
                $list1,
                $list2,
                $list3,
                $list4,
                $list5,
            ],
        ];

        // 这里考虑做成缓存?

        Cache::set($cacheKey, json_encode($this->params), 3600);
    }

    private function getViewFile()
    {
        $this->baseFile = Yii::getAlias('@frontendPc') . '/web/zt/' . $this->key;
        $this->webFile  = '/zt/' . $this->key;
        $file           = $this->baseFile . '/index.html';

        return $file;
    }
}
