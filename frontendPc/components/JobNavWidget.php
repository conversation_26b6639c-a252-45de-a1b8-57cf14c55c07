<?php
namespace frontendPc\components;

use common\base\models\BaseCategoryJob;
use common\libs\Cache;
use frontendPc\models\Job;
use yii\base\Widget;

class JobNavWidget extends BaseWidget
{
    private $list;

    public function init()
    {
        parent::init();

        $config = \Yii::$app->params['homeJobNav'];

        $list = [];
        // foreach ($config as $k=> $item) {
        //     $name    = $item['name'];
        //     if ($item['isMultiple']) {
        //         $list = $item['list'];
        //         foreach ($list as $k1 => &$value) {
        //             $tmp1List = [];
        //             foreach ($value['list'] as $item1) {
        //                 $tmp1List[] = [
        //                     'name' => $item1['name'],
        //                     'url'  => '/job?searchType=1&jobType=' . $item1['id'],
        //                 ];
        //             }
        //             $item['list'] = $tmp1List;
        //         }
        //         $list[] = [
        //             'name' => $name,
        //             'list' => $item,
        //         ];
        //     } else {
        //         $tmpList = [];
        //         foreach ($item['list'] as $r) {
        //             $tmpList[] = [
        //                 'name' => $r['name'],
        //                 'url'  => '/job?searchType=1&jobType=' . $r['id'],
        //             ];
        //         }
        //         $list[] = [
        //             'name' => $name,
        //             'list' => $tmpList,
        //         ];
        //     }
        //
        // }

        // 缓存一小段时间
        $key  = Cache::PC_HOME_JOB_NAV_WIDGET_KEY;
        $data = Cache::get($key);
        if ($data) {
            $config = json_decode($data, true);
        } else {
            foreach ($config as &$item) {
                if ($item['isMultiple']) {
                    foreach ($item['list'] as $k1 => &$item2) {
                        foreach ($item2['list'] as &$item3) {
                            $item3['url']  = '/job?searchType=1&jobType=' . $item3['id'];
                            $item3['name'] = BaseCategoryJob::findOneVal(['id' => $item3['id']], 'name');
                        }
                    }
                } else {
                    foreach ($item['list'] as &$item2) {
                        $item2['url']  = '/job?searchType=1&jobType=' . $item2['id'];
                        $item2['name'] = BaseCategoryJob::findOneVal(['id' => $item2['id']], 'name');
                    }
                }
            }

            Cache::set($key, json_encode($config), 3600 * 24);
        }

        $this->list = $config;
    }

    public function run()
    {
        return $this->render('job_nav.html', [
            'list' => $this->list,
        ]);
    }
}