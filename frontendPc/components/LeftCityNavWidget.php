<?php
namespace frontendPc\components;

use frontendPc\models\HomeColumn;
use frontendPc\models\Job;
use yii\base\Widget;

class LeftCityNavWidget extends BaseWidget
{
    private $list;

    public function init()
    {
        parent::init();

        $list = \Yii::$app->params['configure']['city_nav'];

        foreach ($list as $k => $v) {
            $list[$k]['link'] = HomeColumn::getDetailUrl($v['id']);
        }
        $this->list = $list;
    }

    public function run()
    {
        return $this->render('left_city_nav.html', [
            'list' => $this->list,
        ]);
    }
}