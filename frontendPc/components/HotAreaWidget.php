<?php
namespace frontendPc\components;

use frontendPc\models\HomeColumn;
use Yii;
use yii\base\Widget;

class HotAreaWidget extends BaseWidget
{
    public $list;
    public $columnId;

    public function init()
    {
        parent::init();

        $list = Yii::$app->params['configure']['hot_area'];

        foreach ($list as $k => $v) {
            $list[$k]['link'] = HomeColumn::getDetailUrl($v['id']);
        }
        $this->list = $list;
    }

    public function run(): string
    {
        return $this->render('hot_area.html', [
            'list' => $this->list,
        ]);
    }
}