<?php
namespace frontendPc\components;

use common\helpers\TimeHelper;
use frontendPc\models\Article;
use frontendPc\models\ArticleAttribute;
use frontendPc\models\HomePosition;
use yii\base\Widget;

class RecommendNewsDetailWidget extends BaseWidget
{
    private $list = [];

    public function init()
    {
        parent::init();

        $config = \Yii::$app->params['firstLevelColumn']['news']['recommend_news_detail'];
        $list = Article::find()
            ->alias('a')
            ->select('title,a.type,a.id,sort_time')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'a.id=b.article_id')
            ->where([
                'a.is_delete' => Article::IS_DELETE_NO,
                'b.type'      => ArticleAttribute::ATTRIBUTE_RECOMMEND,
                'status'      => Article::STATUS_ONLINE,
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.type'      => Article::TYPE_NEWS,
            ])
            ->orderBy('a.release_time desc')
            ->limit($config['count'])
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url'] = HomePosition::getDetailUrl($item['type'], $item['id']);
            $item['time'] = TimeHelper::short($item['sort_time']);
        }

        $this->list = $list;
    }

    public function run()
    {
        return $this->render('recommend_news_detail.html', [
            'list' => $this->list,
        ]);
    }
}