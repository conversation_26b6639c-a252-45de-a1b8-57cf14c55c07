<?php

namespace frontendPc\components;

use common\base\models\BaseJob;
use Yii;

/**
 * 全局通知挂件
 * 用于在特定页面显示博士后引流弹窗
 *
 * 显示条件：
 * 1. 职位详情页：职位类型为"博士后"或"特别研究助理"的职位详情页（不含已隐藏职位）
 * 2. 公告详情页："博士后"或"特别研究助理"类型职位（不含已隐藏职位）所属公告的公告详情页
 * 3. 单位详情页：包含"博士后"或"特别研究助理"类型职位的单位详情页及相关页面
 */
class GlobalNoticeWidget extends BaseWidget
{
    /** 博士后职位类型ID */
    const JOB_CATEGORY_POSTDOC = 29;

    /** 特别研究助理职位类型ID */
    const JOB_CATEGORY_RESEARCH_ASSISTANT = 263;

    /** 目标职位类型ID数组 */
    const TARGET_JOB_CATEGORIES = [
        self::JOB_CATEGORY_POSTDOC,
        self::JOB_CATEGORY_RESEARCH_ASSISTANT,
    ];

    /** 单位相关页面路由 */
    const COMPANY_ROUTES = [
        'company/detail',
        'company/detail-announcement-list',
        'company/detail-job-list',
        'company/detail-activity-list',
    ];

    /** 是否显示通知 */
    public $isShow = false;

    /**
     * 运行挂件
     * @return string
     */
    public function run()
    {
        $this->determineShowStatus();

        if ($this->isShow) {
            return $this->render('global_notice.html', $this->getNoticeConfig());
        }

        return '';
    }

    /**
     * 获取通知配置
     * @return array
     */
    private function getNoticeConfig()
    {
        return [
            'title'   => '「高才博士后」站点重磅上线！',
            'message' => $this->getDefaultMessage(),
        ];
    }

    /**
     * 获取默认消息内容
     * @return string
     */
    private function getDefaultMessage()
    {
        return '专为博士精英打造的博士后职位信息平台，全网极为丰富的博士后招收信息一站获取：<br/>' . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;· 日均更新500+条优质博士后岗位需求信息；<br/>' . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;· 遍布全国各个高校、科研院所及头部企业最新的博士后岗位；<br/>' . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;· 可根据学科、机构类型等关键字段精准筛选及智能匹配优质岗位。<br/>' . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2025，博士后已成为斩获高薪offer、直通编制岗位、积累项目资源的重要职业路径选择，' . '「高才博士后」伴您了解更多岗位信息，直通科研、学术生涯快车道！<br/>' . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;立即打开「高才博士后」抢占先机→' . '【<a href="https://boshihou.gaoxiaojob.com" target="_blank">https://boshihou.gaoxiaojob.com/</a>】';
    }

    /**
     * 确定是否显示通知
     */
    private function determineShowStatus()
    {
        $route = Yii::$app->controller->route;
        $id    = Yii::$app->request->get('id');

        if (!$id) {
            $this->isShow = false;

            return;
        }

        switch ($route) {
            case 'job/detail':
                $this->isShow = $this->shouldShowForJobDetail($id);
                break;

            case 'announcement/detail':
                $this->isShow = $this->shouldShowForAnnouncementDetail($id);
                break;
            default:
                if (in_array($route, self::COMPANY_ROUTES)) {
                    $this->isShow = $this->shouldShowForCompanyDetail($id);
                } else {
                    $this->isShow = false;
                }
                break;
        }
    }

    /**
     * 判断职位详情页是否应该显示通知
     * @param int $jobId 职位ID
     * @return bool
     */
    private function shouldShowForJobDetail($jobId)
    {
        $jobCategoryId = BaseJob::findOneVal(['id' => $jobId], 'job_category_id');

        return $this->isTargetJobCategory($jobCategoryId);
    }

    /**
     * 判断公告详情页是否应该显示通知
     * @param int $announcementId 公告ID
     * @return bool
     */
    private function shouldShowForAnnouncementDetail($announcementId)
    {
        $jobCategoryIds = BaseJob::find()
            ->where([
                'announcement_id' => $announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->select('job_category_id')
            ->column();

        return $this->hasTargetJobCategory($jobCategoryIds);
    }

    /**
     * 判断单位详情页是否应该显示通知
     * @param int $companyId 单位ID
     * @return bool
     */
    private function shouldShowForCompanyDetail($companyId)
    {
        return BaseJob::find()
            ->where([
                'company_id'      => $companyId,
                'is_show'         => BaseJob::IS_SHOW_YES,
                'status'          => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'job_category_id' => self::TARGET_JOB_CATEGORIES,
            ])
            ->exists();
    }

    /**
     * 判断单个职位类型是否为目标类型
     * @param int|null $jobCategoryId 职位类型ID
     * @return bool
     */
    private function isTargetJobCategory($jobCategoryId)
    {
        return in_array($jobCategoryId, self::TARGET_JOB_CATEGORIES);
    }

    /**
     * 判断职位类型数组中是否包含目标类型
     * @param array $jobCategoryIds 职位类型ID数组
     * @return bool
     */
    private function hasTargetJobCategory($jobCategoryIds)
    {
        if (empty($jobCategoryIds)) {
            return false;
        }

        return !empty(array_intersect($jobCategoryIds, self::TARGET_JOB_CATEGORIES));
    }
}
