<?php
namespace frontendPc\components;

use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

class NewsBannerWidget extends BaseWidget
{
    public $showcaseA1List;
    public $showcaseA2;
    public $showcaseA3;
    public $columnId;

    public function init()
    {
        parent::init();
        $columnId = $this->columnId;

        $config = Yii::$app->params['newsColumn'];
        $a1     = $config['zixun_A01'];
        $a2     = $config['zixun_A02'];
        $a3     = $config['zixun_A03'];

        $a1Position = BaseHomePosition::find()
            ->select([
                'id',
                'chinese_name',
            ])
            ->where([
                'number'        => 'zixun_A01',
                'status'        => BaseHomePosition::STATUS_ACTIVE,
                'platform_type' => BaseHomePosition::PLATFORM_PC_NEWS,
            ])
            ->asArray()
            ->one();

        $a2Position = BaseHomePosition::find()
            ->select([
                'id',
                'chinese_name',
            ])
            ->where([
                'number'        => 'zixun_A02',
                'status'        => BaseHomePosition::STATUS_ACTIVE,
                'platform_type' => BaseHomePosition::PLATFORM_PC_NEWS,
            ])
            ->asArray()
            ->one();
        $a3Position = BaseHomePosition::find()
            ->select([
                'id',
                'chinese_name',
            ])
            ->where([
                'number'        => 'zixun_A03',
                'status'        => BaseHomePosition::STATUS_ACTIVE,
                'platform_type' => BaseHomePosition::PLATFORM_PC_NEWS,
            ])
            ->asArray()
            ->one();

        $showcaseA1List = BaseShowcase::getByPositionConfig($a1Position['id'], 'zixun_A01', $a1['count']);
        $showcaseA2     = BaseShowcase::getByPositionConfig($a2Position['id'], 'zixun_A02', $a2['count'])[0];
        $showcaseA3     = BaseShowcase::getByPositionConfig($a3Position['id'], 'zixun_A03', $a3['count'])[0];

        $this->showcaseA1List = $showcaseA1List;
        $this->showcaseA2     = $showcaseA2;
        $this->showcaseA3     = $showcaseA3;
    }

    public function run()
    {
        return $this->render('news_banner.html', [
            'showcaseA1List' => $this->showcaseA1List,
            'showcaseA2'     => $this->showcaseA2,
            'showcaseA3'     => $this->showcaseA3,
        ]);
    }
}