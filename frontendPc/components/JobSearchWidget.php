<?php
namespace frontendPc\components;

use common\base\models\BaseResume;
use h5\models\Member;
use h5\models\Resume;

class JobSearchWidget extends BaseWidget
{

    public function run()
    {
        //<!-- 未登录/无生效中的VIP服务时，点击新页面打开【VIP介绍页】；有生效中的VIP服务时，点击新页面打开【职位中心】； -->
        if (\Yii::$app->user->isGuest) {
            $url = BaseResume::BUY_URL_VIP;
        } else {
            $memberId = \Yii::$app->user->id;
            $userInfo = Member::find()
                ->alias('m')
                ->leftJoin(['r' => Resume::tableName()], 'm.id = r.member_id')
                ->where(['m.id' => $memberId])
                ->select([
                    'r.vip_type',
                ])
                ->asArray()
                ->one();
            if ($userInfo['vip_type'] == BaseResume::VIP_TYPE_ACTIVE) {
                $url = '/job';
            } else {
                $url = BaseResume::BUY_URL_VIP;
            }
        }

        return $this->render('job_search.html', ['url' => $url]);
    }
}