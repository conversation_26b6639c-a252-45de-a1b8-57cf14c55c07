<aside class="aside">
    <div class="detail-basic">
        <div class="basic-data">
            <img src="<?=$info['logo']?>" alt="" class="logo">
            <div class="name">
                <h5><?=$info['companyName']?></h5>
                <h6><?=$info['englishName']?></h6>
            </div>
        </div>

        <div class="basic-info">
            <span class="category"><?=$info['industry']?></span>
            <span class="nature">
                <?php if ($info['type']): ?><i><?=$info['type']?></i><?php endif; ?>
                <?php if ($info['nature']): ?><i><?=$info['nature']?></i><?php endif; ?>
            </span>
            <span class="scale"><?=$info['scale']?></span>
            <span class="site"><a href="<?=$info['website']?>" target="_blank" rel="nofollow"><?=$info['website']?></a></span>
        </div>
    </div>

    <?php if($info['predicationTalentPreviewList']['count']>0):?>
    <div class="company-activity">
        <div class="title">单位近期引才活动预告</div>
        <div class="content">
            <?php foreach($info['predicationTalentPreviewList']['list'] as $k=>$activity):?>
            <div class="activity">
                <div class="activity-title">
                    <a href="<?=$activity['url'] ? $activity['url'] : 'javascript:;' ?>" target="<?=$activity['url'] ? '_blank' : '' ?>" rel="<?=$activity['rel']?>" title="<?=$activity['name']?>"><?=$activity['name']?></a>
                </div>
                <div class="activity-content">
                    <div class="activity-tag tag <?='tag'.$activity['seriesType'] ?>"><?=$activity['typeText']?></div>
                    <div class="activity-con"><?=$activity['time']?>&nbsp;丨&nbsp;<?=$activity['area']?></div>
                </div>
            </div>
            <?php endforeach?>
        </div>
        <?php if($info['predicationTalentPreviewList']['count']>2):?>
        <div class="view-all">
            <a href="<?=$info['companyActivityUrl']?>" target="_blank" rel="noopener noreferrer">查看全部活动&nbsp;&nbsp;></a>
        </div>
        <?php endif;?>
    </div>
    <?php endif;?>
    <?php if(!empty($info['styleAtlasList'])):?>
    <div class="company-style">
        <div class="title">单位风采</div>
        <div class="swiper company-style-swiper">
            <div class="swiper-wrapper">
                <?php foreach($info['styleAtlasList'] as $k=>$styleAtlas):?>
                <div class="swiper-slide">
                    <a href="javascript:;" class="company-style-trigger" data-index="<?=$styleAtlas['url']+1?>">
                        <img src="<?=$styleAtlas['url']?>" alt="">
                    </a>
                </div>
                <?php endforeach;?>
            </div>
            <div class="swiper-button swiper-button-next company-style-next"></div>
            <div class="swiper-button swiper-button-prev company-style-prev"></div>
            <div class="swiper-pagination company-style-pagination"></div>
        </div>
    </div>
    <?php endif;?>

    <div class="company-address">
        <div class="title">单位地址</div>
        <div class="address"><?=$info['address']?></div>
    </div>

    <!--                    //登录窗口-->
    <?php if(empty(Yii::$app->params['user'])):?>
    <?= frontendPc\components\RightLoginForm::widget() ?>
    <?php endif;?>

    <!-- 同类雇主职位推荐 -->
    <?php if($info['isCooperation']==2):?>
    <div class="same-wrapper">
        <div class="title">同类型雇主职位推荐</div>

        <div class="swiper same-swiper">
            <div class="swiper-wrapper">
                <?php foreach($info['recommendList'] as $item){ ?>
                <div class="swiper-slide">
                    <?php foreach($item as $v){ ?>
                    <div class="item">
                        <div class="top">
                            <a href="<?=$v['jobUrl']?>" class="name" target="_blank" title="<?=$v['name']?>"><?=$v['name']?></a>
                            <div class="salary"><?=$v['wage']?></div>
                        </div>
                        <!--<div class="middle">3-5年 | 博士研究生 | 招若干个</div>-->
                        <div class="middle"><?=implode(' | ', array_filter([$v['experienceTypeName'],$v['educationTypeName'],($v['amount']?'招'.$v['amount'].'人':'')]))?></div>
                        <div class="bottom">
                            <a href="<?=$v['companyUrl']?>" class="company" target="_blank" title="<?=$v['companyName']?>"><?=$v['companyName']?></a>
                            <div class="address"><?=$v['cityName']?></div>
                        </div>
                    </div>
                    <?php } ?>

                </div>
                <?php } ?>
            </div>
            <div class="swiper-pagination same-pagination"></div>
        </div>
    </div>
    <?php endif;?>

    <div class="to-miniprogram"></div>
</aside>
<script src="/static/js/companyStyle.js"></script>
<script>
    var swiper = new Swiper('.same-swiper', {
        loop: true,
        autoplay: {
            delay: 5000
        },
        pagination: {
            clickable: true,
            el: '.same-pagination'
        }
    })

    const companyStyleImages = [
        <?php foreach($info['styleAtlasList'] as $k => $styleAtlas): ?>
        {
            url: '<?= $styleAtlas['url'] ?>',
            alt: '单位风采图片<?= $k + 1 ?>',
        }<?= ($k < count($info['styleAtlasList']) - 1) ? ',' : '' ?>
        <?php endforeach; ?>
    ]

    CompanyStyleModal.create({
        images: companyStyleImages,
        autoPlay: true,
        autoPlayDelay: 4000,
        effect: 'slide',
        onOpen: () => {
        },
        onClose: () => {
        },
        onSlideChange: (index) => {
        }
    })


</script>
