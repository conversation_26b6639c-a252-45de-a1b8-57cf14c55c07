<!-- 弹窗登录 start -->
<link rel="stylesheet" href="/static/css/loginDialog.css?v=1.3.2">
<script type="text/javascript" src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>

<div id="loginDialogTemplate" class="login-dialog-template" v-cloak>
    <el-dialog v-model="dialogVisible" @close="clearQRCodeTimer" :close-on-click-modal="closeOnClickModal" :show-close="showClose">
        <div class="aside">
            <div class="introduce">
                <div class="name">高校人才网</div>
                百万硕博人的择业平台
            </div>
            <div class="introduce">
                <div class="name">高校直招</div>
                <span>5000+</span>单位官方入驻
            </div>
            <div class="introduce">
                <div class="name">简历直投</div>
                <span>30W+</span>职位一键投递
            </div>
        </div>

        <div class="login-form">
            <div class="login-switch">
                <div @click="handleSwitchToScan" v-if="loginSwitchType=='account'" class="account-tips">
                    <div class="login-tips">微信扫码1秒登录</div>
                </div>
                <div @click="handleSwitchToAccount" v-else class="scan-tips">
                    <div class="login-tips">短信/密码登录</div>
                </div>
            </div>

            <div v-if="loginSwitchType=='account'" class="account-login">
                <el-tabs v-model="loginType">
                    <el-tab-pane label="手机号登录/注册" name="mobile">
                        <el-form ref="mobileLoginRef" :model="mobileLoginForm" :rules="mobileLoginRules">
                            <el-form-item prop="mobile">
                                <el-input
                                    class="mobile"
                                    v-model="mobileLoginForm.mobile"
                                    oninput="value=value.replace(/[^0-9]/g, '')"
                                    name="gaoxiaojobMobile"
                                    autocomplete="on"
                                    placeholder="请输入常用手机号码"
                                    :maxlength="mobileMaxLength"
                                    clearable
                                >
                                    <!-- 暂时不要前置icon -->
                                    <!-- <template #prefix></template> -->

                                    <!-- 号段 -->
                                    <template #prefix>
                                        <el-select v-model="mobileLoginForm.mobileCode" popper-class="dialog-mobile-prefix-popper" class="mobile-prefix-select">
                                            <el-option-group v-for="{ type, list } in prefixOptions" :key="type" :label="type">
                                                <el-option v-for="{ country, code } in list" :key="code" :value="code">
                                                    <span style="float: left">{{ country }}</span>
                                                    <span style="float: right"> {{ code }} </span>
                                                </el-option>
                                            </el-option-group>
                                        </el-select>
                                    </template>
                                </el-input>
                            </el-form-item>

                            <el-form-item prop="code">
                                <el-input class="sms-code" v-model="mobileLoginForm.code" placeholder="请输入验证码"
                                    clearable>
                                    <!-- 暂时不要前置icon -->
                                    <!-- <template #prefix></template> -->

                                    <template #suffix>
                                        <el-button :disabled="codeDisabled" @click="handleSendCode"
                                            style="background: initial" v-html="codeText">
                                        </el-button>
                                    </template>
                                </el-input>
                            </el-form-item>

                            <el-button class="mobile-login-confirm" type="primary" :loading="mobileLogining"
                                @click="handleLogin">
                                求职者登录/注册
                            </el-button>
                        </el-form>
                    </el-tab-pane>

                    <el-tab-pane label="密码登录" name="account">
                        <el-form ref="accountLoginRef" :model="accountLoginForm" :rules="accountLoginRules">
                            <el-form-item prop="account">
                                <el-input v-model="accountLoginForm.account" name="gaoxiaojobAccount" autocomplete="on"
                                    placeholder="请输入手机号/用户名/邮箱"></el-input>
                            </el-form-item>

                            <el-form-item prop="password">
                                <el-input v-model="accountLoginForm.password" type="password" show-password
                                    placeholder="请输入密码">
                                </el-input>
                            </el-form-item>

                            <div class="forget-password"><a class="ft12 color-label" :href="resetPasswordUrl">忘记密码？</a>
                            </div>

                            <el-button class="account-login-confirm" type="primary" :loading="accountLogining"
                                @click="handleLogin">
                                求职者登录
                            </el-button>
                        </el-form>
                    </el-tab-pane>
                </el-tabs>

                <div v-show="loginType=='mobile'" class="entry-tips color-basic">
                    首次登录将自动注册，即代表同意<a class="color-primary" target="_blank" href="<?=$serviceAgreementUrl?>">用户协议</a>和<a
                        class="color-primary" target="_blank" href="<?=$privacyPolicyUrl?>">隐私条款</a>
                </div>

                <div class="signin-tips">
                    <a class="color-primary" :href="signinUrl">点击通过其他方式注册</a>
                </div>
            </div>

            <div v-else class="home-scan-login">
                <div class="type-title">微信扫码登录</div>
                <div class="qr-code" v-loading="qrCodeLoading">
                    <div v-if="!isScanSuccess" class="code" :style="`background-image:url(${qrCodeInfo.url})`"></div>

                    <div class="coderefresh" v-show="isQRcodePast" @click="getQRCode"></div>

                    <div v-if="isScanSuccess" class="scan-success">扫描成功</div>
                </div>
                <div class="scan-tips">
                    <template v-if="isQRcodePast"> 二维码失效 点击重试 </template>
                    <template v-else-if="isScanSuccess">
                        <div class="success">请在手机上确认登录</div>
                    </template>
                    <template v-else>打开微信【扫一扫】<br />扫描二维码快速登录</template>
                </div>
            </div>
        </div>
    </el-dialog>
</div>

<script>
    $(function () {
        const loginDialogOptions = {
            data() {
                function validMobile(mobile) {
                    const reg = /^1[3-9]\d{9}$/
                    return reg.test(mobile)
                }

                function validNumber(number) {
                    return /^\d+$/.test(number)
                }

                function validPassword(password) {
                    const reg = /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)[A-Za-z\d]{6,26}$/
                    return reg.test(password)
                }

                const validateMobile = (rule, value, callback) => {
                    const { mobileLoginForm: { mobileCode } } = this
                    if (mobileCode === '+86') {
                        if (validMobile(value)) {
                            callback()
                        } else {
                            callback('请输入正确的手机号码')
                        }
                    } else if (validNumber(value)) {
                        callback()
                    } else {
                        callback('请输入正确的手机号码')
                    }
                }

                const validatePassword = (rule, value, callback) => {
                    if (validPassword(value)) {
                        callback()
                    } else {
                        callback('6-26位大小写字母、数字组合密码')
                    }
                }

                return {
                    closeOnClickModal: true,
                    showClose: true,

                    dialogVisible: false,
                    loginType: 'mobile',

                    loginSwitchType: 'scan',
                    qrCodeTimer: null,
                    qrCodeInfo: {
                        url: '',
                        scene: '',
                        status: ''
                    },
                    qrCodeLoading: false,
                    redirectUrl: window.location.href,

                    captcha: null,
                    captchaAppId: '',

                    prefixOptions: [],

                    mobileLoginForm: {
                        mobileCode: '+86',
                        mobile: '',
                        code: ''
                    },

                    codeDisabled: false,
                    codeText: '获取验证码',
                    codeTime: 60,
                    codeTimer: null,

                    mobileLogining: false,

                    mobileLoginRules: {
                        mobile: [
                            { required: true, message: '请输入手机号码', trigger: 'blur' },
                            { validator: validateMobile, trigger: 'blur' }
                        ],
                        code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
                    },

                    accountLoginForm: {
                        account: '',
                        password: ''
                    },

                    accountLogining: false,

                    accountLoginRules: {
                        account: [{ required: true, message: '请输入手机号/用户名/邮箱', trigger: 'blur' }],
                        password: [
                            { required: true, message: '请输入密码', trigger: 'blur' },
                            { validator: validatePassword, trigger: 'blur' }
                        ]
                    }
                }
            },

            computed: {
                isMobileLogin() {
                    return this.loginType === 'mobile'
                },

                redirectQuery() {
                    return `?redirect=${window.location.href}`
                },

                signinUrl() {
                    return `/member/person/registry${this.redirectQuery}`
                },

                companySigninUrl() {
                    return `/member/company/login`
                },

                resetPasswordUrl() {
                    return `/member/person/reset${this.redirectQuery}`
                },
                isNeedStep() {
                    var search = window.location.search
                    var URLParams = new URLSearchParams(search)
                    return URLParams.get('isNeedStep') || ''
                },
                mobileMaxLength() {
                    return this.mobileLoginForm.mobileCode === '+86' ? 11 : 20
                },
                isQRcodePast() {
                    return this.qrCodeInfo.status === -1
                },
                isScanSuccess() {
                    return this.qrCodeInfo.status === 2
                }
            },

            methods: {
                hideAllClose() {
                    this.closeOnClickModal = false
                    this.showClose = false
                },

                showLoginDialog() {
                    this.dialogVisible = true
                    if (this.loginSwitchType == 'scan') {
                        this.getQRCode()
                    }
                },

                async getMobilePrefix() {
                    const data = await httpGet('/api/config/load-country-mobile-code')
                    this.prefixOptions = data
                },

                async getMobileCode(data) {
                    const { mobileLoginForm: { mobileCode, mobile } } = this
                    await httpPost('/api/member/send-mobile-login-code', { mobileCode, mobile, type: 1, ...data })
                },

                async getCaptchaConfig() {
                    const { captchaAppId: appId } = await httpGet('/api/member/get-captcha-config')
                    this.captchaAppId = appId
                },

                handleCaptcha(callback) {
                    try {
                        this.captcha = new TencentCaptcha(this.captchaAppId, (res) => {
                            const { ret, ticket, randstr } = res
                            if (ret === 0) {
                                callback && callback({ ticket, randstr })
                            }
                        })
                        this.captcha.show()
                    } catch (err) {
                        console.log(err)
                    }
                },

                countDown() {
                    this.codeDisabled = true
                    this.codeTimer = setInterval(() => {
                        if (this.codeTime === 1) {
                            clearInterval(this.codeTimer)
                            this.codeTime = 60
                            this.codeText = '重新发送'
                            this.codeDisabled = false
                        } else {
                            this.codeTime -= 1
                            this.codeText = `${this.codeTime}S<span>重新获取</span>`
                        }
                    }, 1000)
                },

                handleSendCode() {
                    let params = {
                        actionType: 3,
                        actionModule: 10
                    }
                    buriedPoint(params)
                    this.$refs.mobileLoginRef.validateField('mobile', (errMsg) => {
                        if (errMsg.length === 0) {
                            this.handleCaptcha((data) => {
                                this.getMobileCode(data)
                                this.countDown()
                            })
                        }
                    })

                },

                async handleSign(data) {
                    let params = {
                        actionType: 0,
                        actionModule: 0
                    }

                    const { isMobileLogin } = this
                    const loginApi = '/api/member/' + (isMobileLogin ? 'validate-mobile-login-code' : 'account-login')
                    const loading = isMobileLogin ? 'mobileLogining' : 'accountLogining'

                    this[loading] = true

                    let formData = { type: 1 }

                    if (isMobileLogin) {
                        params.actionType = 2
                        params.actionModule = 10
                        const { mobileLoginForm: { mobileCode, mobile, code } } = this
                        formData = { ...formData, mobileCode, mobile, code }
                    } else {
                        params.actionType = 2
                        params.actionModule = 11
                        formData = { ...formData, ...this.accountLoginForm }
                    }
                    buriedPoint(params)
                    try {
                        const { token, expireTime, redirectUrl } = await httpPost(loginApi, { ...formData, ...data, redirect: this.redirectUrl,isNeedStep: this.isNeedStep })
                        setToken(token, expireTime)
                        window.location.href = redirectUrl
                    } catch (err) {
                        this[loading] = false
                    }
                },

                handleLogin() {
                    const { isMobileLogin } = this
                    const formRef = this.$refs[isMobileLogin ? 'mobileLoginRef' : 'accountLoginRef']

                    formRef.validate((valid) => {
                        if (valid) {
                            if (isMobileLogin) {
                                this.handleSign()
                            } else {
                                this.handleCaptcha((data) => {
                                    this.handleSign(data)
                                })
                            }
                        }
                    })

                },

                clearQRCodeTimer() {
                    clearTimeout(this.qrCodeTimer)
                },

                checkQRCode() {
                    this.qrCodeTimer = setTimeout(async () => {
                        const { scene } = this.qrCodeInfo
                        const data = await httpPost('/api/member/check-mini-login-qrcode', {
                            scene,
                            redirect: this.redirectUrl,
                            isNeedStep: this.isNeedStep
                        })

                        const { status, userInfo } = data

                        // 1等待扫码、2已经扫码、3登录成功、-1错误
                        this.qrCodeInfo.status = status

                        switch (status) {
                            case 1:
                                if (this.loginSwitchType != 'scan') {
                                    this.clearQRCodeTimer()
                                    return
                                }
                                this.checkQRCode()
                                break
                            case 2:
                                this.checkQRCode()
                                break
                            case 3:
                                setToken(userInfo.token, userInfo.expireTime)
                                window.location.href = userInfo.redirectUrl
                                break
                            default:
                                this.clearQRCodeTimer()
                                break
                        }
                    }, 2000)
                },

                async getQRCode() {
                    this.qrCodeLoading = true
                    const data = await httpGet('/api/member/get-mini-login-qrcode')
                    this.qrCodeInfo = data
                    this.qrCodeLoading = false
                    this.clearQRCodeTimer()
                    this.checkQRCode()
                },

                handleSwitchToScan() {
                    this.loginSwitchType = 'scan'
                    this.qrCodeInfo.status = ''
                    this.getQRCode()
                },

                handleSwitchToAccount() {
                    this.loginSwitchType = 'account'
                    this.clearQRCodeTimer()
                },


            },

            mounted() {
                this.getMobilePrefix()
                this.getCaptchaConfig()
            },
            //----------监听用户切换登录方式，新增用户埋点日志开始--------------
            watch: {
                loginType(newValue,oldValue) {
                    let params = {
                        actionType: 0,
                        actionModule: 0
                    }
                    if(newValue === 'mobile'){
                        // 点击了手机号切换
                        params.actionType = 1
                        params.actionModule = 10
                    }else if(newValue === 'account'){
                        params.actionType = 1
                        params.actionModule = 11
                    }
                    buriedPoint(params)
                },
                loginSwitchType(newValue,oldValue){
                    let params = {
                        actionType: 0,
                        actionModule: 0
                    }
                    if(newValue === 'scan'){
                        params.actionType = 1
                        params.actionModule = 12
                    }else if(newValue === 'account'){
                        params.actionType = 1
                        params.actionModule = 11
                    }
                    buriedPoint(params)
                }
            },
            //----------监听用户切换登录方式，新增用户埋点日志结束--------------


        }
        const loginDialogComponent = Vue.createApp(loginDialogOptions).use(ElementPlus).mount('#loginDialogTemplate')

        window.globalComponents = { ...window.globalComponents, loginDialogComponent }
    });
</script>
<!-- 弹窗登录 end -->