<link rel="stylesheet" href="/static/css/checkGenerateReportDialog.css">

<div id="checkGenerateReportDialog" class="success-dialog-alert-template" v-cloak>
    <el-dialog v-model="dialogVisible" :close-on-click-modal="false">
        <div class="wechat-container" v-if="jumpType == 1">
            <div class="checktips">{{ title }}</div>
            <div class="tips-text">
                <div class="text-right">
                    <span>{{tips1}}</span>
                    <div class="text-second">{{tips2}}</div>
                </div>
            </div>
            <div class="el-message-box__btns">
                <button class="el-button el-button--default el-button--large change" type="button" @click="handelDialogClose">
                    <span>{{cancelBtnTxt}}</span>
                </button>
                <button class="el-button el-button--default el-button--large el-button--primary" type="button" @click="createReport(isConfirm)">
                    <span>{{confirmBtnTxt}}</span>
                </button>
            </div>
        </div>
        <div class="wechat-container" v-else-if="jumpType == 9">
            <div class="checktips">{{ title }}</div>
            <div class="tips-text">
                <div class="text-right">
                    <span>{{tips1}}</span>
                    <div class="text-second">{{tips2}}</div>
                </div>
            </div>
            <div class="el-message-box__btns">
                <button class="el-button el-button--default el-button--large change" type="button" @click="handelDialogClose">
                    <span>{{cancelBtnTxt}}</span>
                </button>
                <button class="el-button el-button--default el-button--large el-button--primary" type="button" @click="finishResume">
                    <span>{{confirmBtnTxt}}</span>
                </button>
            </div>
        </div>
        <div class="wechat-container" v-else>
            <div class="checktips">{{ title }}</div>
            <div class="tips-text">
                <div class="text-right">
                    <span>{{tips1}}</span>
                    <div class="text-second">{{tips2}}</div>
                </div>
            </div>
            <div class="el-message-box__btns">
                <button class="el-button el-button--default el-button--large change" type="button" @click="handelDialogClose">
                    <span>{{cancelBtnTxt}}</span>
                </button>
            </div>
        </div>
    </el-dialog>
</div>

<script>
    $(function () {
        const PromptDialogOptions = {
            data() {
                return {
                    dialogVisible: false,
                    title: '',
                    tips1: '',
                    tips2: '',
                    jumpUrl: '',
                    jumpType: '',
                    confirmBtnTxt: '',
                    cancelBtnTxt: '',
                    params: '',
                    isConfirm: ''
                }
            },

            methods: {
                pull(params, callback = () => {}) {
                    var _this = this
                    _this.params = params
                    httpGet(params.apiPull, params.param).then(function (data) {
                        const { title, jumpType, jumpUrl, confirmBtnTxt, cancelBtnTxt, tips1, tips2, isConfirm } = data
                        if (jumpType == 2) {
                            window.open(jumpUrl, '_blank')
                            return
                        }
                        _this.dialogVisible = true
                        _this.title = title
                        _this.tips1 = tips1
                        _this.tips2 = tips2
                        _this.jumpUrl = jumpUrl
                        _this.jumpType = jumpType
                        _this.confirmBtnTxt = confirmBtnTxt
                        _this.cancelBtnTxt = cancelBtnTxt
                        _this.isConfirm = isConfirm
                    })
                },
                createReport(id) {
                    var _this = this
                    var params = _this.params
                    var isConfirm = { isConfirm: id }
                    var param = { ...params.param, ...isConfirm }
                    if (id == '1') {
                        httpGet(params.apiPull, param).then(function (data) {
                            const { title, jumpType, jumpUrl, confirmBtnTxt, cancelBtnTxt, tips1, tips2, isConfirm } = data
                            _this.title = title
                            _this.tips1 = tips1
                            _this.tips2 = tips2
                            _this.jumpUrl = jumpUrl
                            _this.jumpType = jumpType
                            _this.confirmBtnTxt = confirmBtnTxt
                            _this.cancelBtnTxt = cancelBtnTxt
                            _this.isConfirm = isConfirm
                        })
                    } else {
                        httpGet(params.apiCreate, params.param).then(function (data) {
                            const { jumpUrl } = data
                            _this.handelDialogClose()
                            window.open(jumpUrl)
                        })
                    }
                },
                finishResume() {
                    window.location.href = this.jumpUrl
                },
                handelDialogClose() {
                    this.dialogVisible = false
                }
            },
            mounted() {}
        }

        const PromptDialogComponent = Vue.createApp(PromptDialogOptions).use(ElementPlus).mount('#checkGenerateReportDialog')

        window.globalComponents = { ...window.globalComponents, PromptDialogComponent }
    })
</script>