<link rel="stylesheet" href="/static/css/selectDialog.css" />
<link rel="stylesheet" href="/static/css/jobSearch.css" />
<script src="/static/lib/dialog/selectDialog.js"></script>

<div class="left-siderbar-box job-search-box" id="jobSearchComponent">
    <div class="common-title">
        <h2 class="job-search">
            <span>职位搜索</span>

            <!-- 未登录/无生效中的VIP服务时，点击新页面打开【VIP介绍页】；有生效中的VIP服务时，点击新页面打开【职位中心】； -->
            <a href="<?=$url?>" target="_blank"></a>
        </h2>
    </div>
    <div class="area">
        <div class="areatitle">地区选择</div>
        <div class="search flex jobs" id="area">
            <el-input class="search-keyword"
                      clearable
                      :class="areaText.length ? 'is-checked' : ''"
            >
                <template #prefix>
                    <span class="job-category" @click="handleDialogActive('areaList')">{{calcWordTypeText}}</span>
                    <i class="el-select__caret el-input__icon el-icon-circle-close" @click="handleDialogClear('areaList')"></i>
                    <i class="el-select__caret el-input__icon el-icon-arrow-down" @click="handleDialogActive('areaList')"></i>
                </template>
            </el-input>
        </div>
    </div>
    <div class="area">
        <div class="areatitle">学科分类</div>
        <div class="search flex jobs" id="discipline">
            <el-input class="search-keyword"
                      clearable
                      :class="majorId.length ? 'is-checked' : ''"
            >
                <template #prefix>
                    <span class="job-category" @click="handleDialogActive('majorList')">{{calcMajorText}}</span>
                    <i class="el-select__caret el-input__icon el-icon-circle-close" @click="handleDialogClear('majorList')"></i>
                    <i class="el-select__caret el-input__icon el-icon-arrow-down" @click="handleDialogActive('majorList')"></i>
                </template>
            </el-input>
        </div>
    </div>
    <div class="area">
        <div class="areatitle">学历要求</div>
        <div class="search flex jobs" id="educationa">
            <el-select
                    v-model="educationType"
                    :class="educationType ? 'is-checked' : ''"
                    placeholder="请选择"
                    clearable
                    @change="updateQueryValue('educationType', educationType)"
            >
                <el-option v-for="{ label, value } in educationList" :label="label" :value="value" />
            </el-select>
        </div>
    </div>
    <div class="area">
        <div class="areatitle">职位类型</div>
        <div class="search flex jobs" id="jobs">
            <el-input class="search-keyword"
                      clearable
                      :class="jobTypeText.length ? 'is-checked' : ''"
            >
                <template #prefix>
                    <span class="job-category" @click="handleDialogActive('jobTypeList')">{{calcJobTypeText}}</span>
                    <i class="el-select__caret el-input__icon el-icon-circle-close" @click="handleDialogClear('jobTypeList')"></i>
                    <i class="el-select__caret el-input__icon el-icon-arrow-down" @click="handleDialogActive('jobTypeList')"></i>
                </template>
            </el-input>
        </div>
    </div>
    <button style="width:100%" class="el-button el-button--primary" @click="handleSearch">职位搜索</button>
    <select-dialog
            ref="selectDialogRef"
            :title="calcDialogTitle"
            :search-placeholder="calcDialogPlaceholder"
            :list="calcDialogList"
            :name="calcDialogName"
            multiple
            :multiple-limit="5"
            v-model="calcDialogValue"
            @update="handleDialogChange"
    ></select-dialog>
</div>
<script>
    const jobSearchOptions = {
        data() {
            return {
                dialogActive: '',
                educationType:'',
                educationList: [],
                jobTypeList: [],
                WordTypeText:[],
                majorId:[],
                jobType: [],
                jobTypeText: [],
                areaId: [],
                areaText: [],
                majorText:[],
            }
        },
        methods: {
            handleSearch(forward) {
                let updateRoute = !!forward || true
                const search = decodeURIComponent(location.search)
                const data = this.getSearchData()
                const params = {}
                const checkboxKeys = ['isFast', 'isFresh', 'isCooperation']

                const query = Object.keys(data).reduce(function (previous, current) {
                    let value = data[current]

                    if (checkboxKeys.includes(current) && value === '2') {
                        value = ''
                    }

                    if (current === 'currentPage' && value === 1) {
                        value = ''
                    }

                    if (value) {
                        const prefix = `${previous}${previous === '' ? '?' : '&'}`

                        if (Array.isArray(value)) {
                            if (value.length) {
                                params[current] = value.join(',')
                                return `${prefix}${current}=${value.join('_')}`
                            } else {
                                return previous
                            }
                        } else {
                            params[current] = value
                            return `${prefix}${current}=${value}`
                        }
                    }

                    return previous
                }, '')
                window.open(`/job${query}`)
            },
            getQueryParams() {
                const query = `{"${location.search.replace('?', '')}"}`
                const params = query.replace(/&/g, '","').replace(/=/g, '": "')
                const arrayKeys = ['jobType', 'areaId', 'educationType', 'majorId']

                try {
                    const data = JSON.parse(params)

                    Object.keys(data).forEach(function (item) {
                        const value = decodeURIComponent(data[item])
                        if (item === 'currentPage') {
                            data[item] = value * 1
                        } else {
                            data[item] = arrayKeys.includes(item) ? value.split('_') : value
                        }
                    })
                    return data
                } catch {
                    return {}
                }
            },
            getParamsData() {
                const _this = this

                httpGet('/job/home-params')
                    .then(function (data) {
                        const {
                            jobTypeList,
                            hotAreaList,
                            areaList,
                            companyTypeList,
                            majorList,
                            educationList,
                            companyNatureList,
                            industryList,
                            releaseList,
                            titleList,
                            natureList,
                            selectButtonList
                        } = data
                        const areaItem = { label: '全国', value: '' }
                        const companyTypeItem = { label: '全部', value: '' }

                        hotAreaList.unshift(areaItem)
                        companyTypeList.unshift(companyTypeItem)

                        _this.jobTypeList = jobTypeList
                        _this.hotAreaList = hotAreaList
                        _this.areaList = areaList
                        _this.companyTypeList = companyTypeList
                        _this.majorList = majorList
                        _this.educationList = educationList
                        _this.companyNatureList = companyNatureList
                        _this.industryList = industryList
                        _this.releaseList = releaseList
                        _this.titleList = titleList
                        _this.natureList = natureList
                        _this.filterCheckbox = selectButtonList
                    })
                    .catch(function () {})
            },
            handleDialogClear(name) {
                const _this = this
                this.dialogActive = name

                this.$nextTick(function () {
                    const [valKey, textKey] = _this.calcDialogValueTextKey

                    _this[valKey] = []
                    _this[textKey] = []
                })
            },
            handleDialogActive(name) {
                this.dialogActive = name
                this.$refs.selectDialogRef.handleOpen()
            },
            updateQueryValue(key, value) {
                this[key] = value
            },
            updateQueryData(data) {
                const {
                    keyword,
                    jobType,
                    areaId,
                    companyType,
                    majorId,
                    educationType,
                    companyNature,
                    industryId,
                    releaseTimeType,
                    titleType,
                    natureType,
                    isFast,
                    isFresh,
                    isCooperation,
                    currentPage
                } = data

                this.keyword = keyword || ''
                this.jobType = jobType || []
                this.areaId = areaId || []
                this.companyType = companyType || []
                this.majorId = majorId || []
                this.educationType = educationType || ''
                this.companyNature = companyNature || ''
                this.industryId = industryId || ''
                this.releaseTimeType = releaseTimeType || ''
                this.titleType = titleType || ''
                this.natureType = natureType || ''
                this.isFast = isFast || '2'
                this.isFresh = isFresh || '2'
                this.isCooperation = isCooperation || '2'
                this.currentPage = currentPage || 1
            },
            handleInit(){
                this.updateQueryData(this.getQueryParams())
                this.getParamsData()
            },
            getSearchData() {
                const {
                    keyword,
                    jobType,
                    areaId,
                    companyType,
                    majorId,
                    educationType,
                    companyNature,
                    industryId,
                    releaseTimeType,
                    titleType,
                    natureType,
                    isFast,
                    isFresh,
                    isCooperation,
                    currentPage
                } = this

                return {
                    keyword,
                    jobType,
                    areaId,
                    companyType,
                    majorId,
                    educationType,
                    companyNature,
                    industryId,
                    releaseTimeType,
                    titleType,
                    natureType,
                    isFast,
                    isFresh,
                    isCooperation,
                    currentPage
                }
            },
            handleDialogChange(data) {
                const [valKey, textKey] = this.calcDialogValueTextKey
                const { label, value } = data

                if (textKey) {
                    this[textKey] = label
                }
            },

        },
        computed: {
            calcDialogList() {
                const { dialogActive } = this
                return this[dialogActive] || []
            },
            calcDialogValueTextKey() {
                const { dialogActive } = this
                const options = {
                    jobTypeList: ['jobType', 'jobTypeText'],
                    areaList: ['areaId', 'areaText'],
                    majorList: ['majorId', 'majorText']
                }
                const value = options[dialogActive]
                if (value) {
                    return value
                }
                return ['', '']
            },
            calcDialogName() {
                const { dialogActive } = this
                return 'area'
            },
            calcDialogValue: {
                get() {
                    const [valKey, textKey] = this.calcDialogValueTextKey

                    if (valKey) {
                        return this[valKey]
                    }
                    return []
                },
                set(val) {
                    const [valKey, textKey] = this.calcDialogValueTextKey

                    if (valKey) {
                        this[valKey] = val
                    }
                }
            },
            calcJobTypeText() {
                const { jobTypeText } = this

                if (jobTypeText.length) {
                    return jobTypeText.join(',')
                }
                return '请选择'
            },
            calcMajorText() {
                const { majorText } = this

                if (majorText.length) {
                    return majorText.join(',')
                }
                return '请选择'
            },
            calcWordTypeText() {
                const { areaText } = this

                if (areaText.length) {
                    return areaText.join(',')
                }

                return '请选择'
            },
            calcDialogTitle() {
                const { dialogActive } = this
                const options = {
                    jobTypeList: '请选择职位类型',
                    areaList: '请选择地点',
                    majorList: '请选择学科'
                }

                return options[dialogActive] || '请选择'
            },
            calcDialogPlaceholder() {
                const { dialogActive } = this
                const options = {
                    jobTypeList: '请输入职位类型关键词',
                    areaList: '搜索城市',
                    majorList: '请输入学科关键词'
                }

                return options[dialogActive] || '请输入关键词'
            },
        },
        mounted(){
            this.handleInit()
        }
    }

    Vue.createApp({ ...jobSearchOptions })
        .use(ElementPlus)
        .component('select-dialog', selectDialogComponent)
        .mount('#jobSearchComponent')
</script>