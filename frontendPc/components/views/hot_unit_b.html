<div class="site-tupian-forty flex w">
    <div class="fenlei-tab">
        <ul class="sort-list">
            <?php foreach($tapList as $k=>$v){?>
            <li class="<?php if($k==0){echo 'tab-mouseover';}?>"><?php echo $v['name'];?></li>
            <?php } ?>
        </ul>
    </div>
    <div class="tab-card-content">

        <?php foreach($list as $key=>$item){?>
        <?php if($key==0){?>
        <div class="pic" style="display: flex;">
            <?php }else{?>
            <div class="pic">
                <?php }?>
                <?php if($tapList[$key]['title']){?>
                <div class="list-title"><?php echo $tapList[$key]['title'];?></div>
                <?php }?>
                <div class="swiper sort-switch">
                    <div class="swiper-wrapper">
                        <?php foreach($item as $i=>$j){?>
                        <div class="swiper-slide">
                            <div class="focus-box">
                                <?php foreach($j as $o=>$c){?>
                                <a href="<?=$c['url'] ?>" target="_blank" class="list-link showcase-browse"
                                   data-showcase-number="<?php echo $c['number'] ?>"
                                   data-showcase-id="<?php echo $c['id'] ?>">
                                    <img src="<?php echo $c['image_link'];?>" alt="<?php echo $c['image_alt'];?>" class="school-pictures">
                                    <p class="title" title="<?=\common\helpers\StringHelper::changeQuotationMark($c['title'])?>"><?php echo $c['title'];?></p>
                                    <p class="subtitle"  title="<?=\common\helpers\StringHelper::changeQuotationMark($c['sub_title'])?>"><?php echo $c['sub_title'];?></p>
                                </a>
                                <?php }?>
                            </div>
                        </div>
                        <?php }?>
                    </div>
                    <div class="swiper-pagination sort-pagination"></div>
                </div>
            </div>
            <?php }?>
        </div>
    </div>