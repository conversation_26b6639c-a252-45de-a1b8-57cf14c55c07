<!-- 已登录 简历未完善 end -->
<?php if ($data['isStep']): ?>
<div class="banner-login-container">
    <a href="/member/person/resume" class="to-resume">
        <img class="avatar rich-img" src="<?=$data['avatar']?>" alt=""/>

        <?php if ($data['isVip']): ?>
        <div class="vip-logo"></div>
        <?php endif; ?>
        <div class="data">
            <h5 class="name"><?=$data['name']?></h5>
            <p class="tips">简历完整度<?=$data['complete']?>%</p>
        </div>
        <span class="mark">编辑简历</span>
    </a>

    <ul class="link-list">
        <li>
            <a href="/member/person/delivery">
                <span class="num"><?=$data['applyAmount']?>
                    <?php if ($data['isFeedback']): ?>
                    <sup class="badge is-special">反馈来了</sup>
                    <?php endif; ?>
                </span>
                <span class="txt">我的投递</span>
            </a>
        </li>
        <li>
            <a href="/member/person/invite">
                <span class="num"><?=$data['jobInviteAmount']?>
                    <?php if ($data['jobInviteRemindAmount']): ?>
                    <sup class="badge"><?=$data['jobInviteRemindAmount']?></sup>
                    <?php endif; ?>
                </span>
                <span class="txt">职位邀约</span>
            </a>
        </li>
        <li>
            <a href="/member/person/view">
                <span class="num"><?=$data['companyViewAmount']?>
                    <?php if ($data['companyViewRemindAmount']): ?>
                    <sup class="badge"><?=$data['companyViewRemindAmount']?></sup>
                    <?php endif; ?>
                </span>
                <span class="txt">谁看过我</span>
            </a>
        </li>
    </ul>

    <div class="operate-btn">
        <button id="refreshResumeBtn" class="el-button el-button--default el-button--mini">
            <span>刷新简历</span>
        </button>
        <?php if($data['isResumeTop']){ ?>
            <a href="/member/person/home">
                <button class="el-button el-button--primary el-button--mini">
                    <span>专享推荐</span>
                </button>
            </a>
        <?php }else{ ?>
            <a  target="_blank" href="/job-fast.html">
                <button class="el-button el-button--primary el-button--mini">
                    <span>置顶简历</span>
                </button>
            </a>
        <?php } ?>

    </div>
</div>
<?php else: ?>
<!-- 已登录 简历已完善  start-->
<div class="banner-login-container">
    <a href="/member/person/resume" class="to-resume">
        <img class="avatar rich-img" src="<?=$data['avatar']?>" alt=""/>

        <?php if ($data['isVip']): ?>
        <div class="vip-logo"></div>
        <?php endif; ?>
        <div class="data">
            <h5 class="name"><?=$data['name']?></h5>
            <p class="tips is-warning">在线简历待完善</p>
        </div>
    </a>

    <ul class="link-list">
        <li>
            <a href="/member/person/delivery">
                <span class="num"><?=$data['applyAmount']?>
                    <?php if ($data['isFeedback']): ?>
                    <sup class="badge is-special">反馈来了</sup>
                    <?php endif; ?>
                </span>
                <span class="txt">我的投递</span>
            </a>
        </li>
        <li>
            <a href="/member/person/invite">
                <span class="num"><?=$data['jobInviteAmount']?>
                    <?php if ($data['jobInviteRemindAmount']): ?>
                    <sup class="badge"><?=$data['jobInviteRemindAmount']?></sup>
                    <?php endif; ?>
                </span>
                <span class="txt">职位邀约</span>
            </a>
        </li>
        <li>
            <a href="/member/person/view">
                <span class="num"><?=$data['companyViewAmount']?>
                    <?php if ($data['companyViewRemindAmount']): ?>
                    <sup class="badge"><?=$data['companyViewRemindAmount']?></sup>
                    <?php endif; ?>
                </span>
                <span class="txt">谁看过我</span>
            </a>
        </li>
    </ul>

    <div class="operate-btn is-modify">
        <a href="/member/person/resume">
            <button class="el-button el-button--default el-button--mini">
                <span>完善简历</span>
                <sup class="badge"><?=$data['complete']?>%</sup>
            </button>
        </a>
        <?php if($data['isResumeTop']){ ?>
        <a href="/member/person/home">
            <button class="el-button el-button--primary el-button--mini">
                <span>专享推荐</span>
            </button>
        </a>
         <?php }else{ ?>
            <a target="_blank" href="/job-fast.html">
                <button class="el-button el-button--primary el-button--mini">
                    <span>置顶简历</span>
                </button>
            </a>
        <?php } ?>
    </div>
</div>
<?php endif; ?>

<script>
    $(function () {
        var $refreshResumeBtn = $('#refreshResumeBtn')

        function refreshMessageBox(title, message, options, callback = ()=>{}) {
            ElementPlus.ElMessageBox.alert(message, title, {
                ...options,
                buttonSize: 'large',
                callback
            })
        }

        function refreshToast(message) {
            ElementPlus.ElMessage.success(message)
        }

        // 修改简历显示状态
        function changeResumeStatus() {
            httpPost('/api/person/resume/change-show-status')
        }

        function resumeReFresh(){
            httpPost('/api/person/resume/refresh').then(function (data) {
                const {
                    type,
                    title,
                    message,
                    url
                } = data
                // 1第一次刷新，2博士研究生完善简历，3简历中心，4再次刷新，5开放简历再刷新一次，9其他
                if(type === 1 || type === 4){
                    refreshMessageBox(title, message, {dangerouslyUseHTMLString: true,confirmButtonText: '我知道了'})
                }

                if(type === 2){
                    refreshMessageBox(title, message,
                    {
                        dangerouslyUseHTMLString: true,
                        showCancelButton: true,
                        cancelButtonText: '取消',
                        confirmButtonText: '立即优化'
                    },
                    (action)=>{
                        if (action === 'confirm') {
                            window.location.href = url
                        }
                    })
                }

                if(type === 3){
                    refreshMessageBox(title, message,
                    {
                        dangerouslyUseHTMLString: true,
                        showCancelButton: true,
                        cancelButtonText: '取消',
                        confirmButtonText: '去完善'
                    },
                    (action)=>{
                        if (action === 'confirm') {
                            window.open(url,'_blank')
                        }
                    })
                }

                if(type === 5){
                    refreshMessageBox(title, message,
                    {
                        dangerouslyUseHTMLString: true,
                        showCancelButton: true,
                        cancelButtonText: '取消',
                        confirmButtonText: '确定'
                    },
                    async (action)=>{
                        if (action === 'confirm') {
                            await changeResumeStatus()
                            resumeReFresh()
                        }
                    })
                }

                if(type === 9){
                    refreshToast(message)
                     setTimeout(() => {
                        window.open('/job-fast.html','_blank')// 跳转到求职快介绍页
                      }, 3000)
                      return
                }
            })
        }

        $refreshResumeBtn.on('click', function () {
            resumeReFresh()
        })
    })

</script>



