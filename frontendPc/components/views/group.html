<div class="exchange">
    <div class="common-title exchange-title component">
        <h2>圈子</h2>
        <div class="inner">
            <ul class="flex">
                <li>QQ群</li>&nbsp;|&nbsp;
                <li class="is-active">微信群</li>
            </ul>
        </div>
    </div>

    <div class="dialogue">
        <div class="widget-content">
            <ul>
                <li>
                    <span class="dialogue-title  exchange-list4" title="高校人才网小程序">高校人才网小程序</span>
                    <span class="join"> 加入
                        <?php if ($showPosition=='right'): ?>

                        <div class="join-code">
                        <?php else: ?>
                            <div class="join-code is-join-code">

                        <?php endif; ?>
                            <img src="https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/201.png" alt="高校人才网小程序"
                                 class="public-code">
                            <div class="news-part">
                                <h4 class="scan-join">高才优聘</h4>
                                <span class="explain">高校人才网官方小程序</span>
                            </div>
                        </div>
                    </span>
                </li>
                <?php foreach ($qq as $v ): ?>
                <li>
                    <span class="dialogue-title $v['class']?>" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['name'])?>"><?=$v['name']?></span>
                    <?php if ($v['isFull']): ?>
                    <span class="join"><a target="_blank" href="<?=$v['url']?>">已满</a></span>
                    <?php else: ?>
                    <span class="join"><a target="_blank" href="<?=$v['url']?>">加入</a></span>
                    <?php endif; ?>
                </li>
                <?php endforeach ?>
            </ul>
        </div>
        <div class="widget-content" style="display: block;">
            <ul>
                <li>
                    <span class="dialogue-title  exchange-list4" title="高校人才网小程序">高校人才网小程序</span>
                    <span class="join"> 加入
                        <?php if ($showPosition=='right'): ?>

                        <div class="join-code">
                        <?php else: ?>
                            <div class="join-code is-join-code">

                        <?php endif; ?>
                            <img src="https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/201.png" alt="高校人才网小程序"
                                 class="public-code">
                            <div class="news-part">
                                <h4 class="scan-join">高才优聘</h4>
                                <span class="explain">高校人才网官方小程序</span>
                            </div>
                        </div>
                    </span>
                </li>
                <?php foreach ($wx as $v ): ?>
                <li>
                    <span class="dialogue-title <?=$v['class']?>" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['name'])?>"><?=$v['name']?></span>
                    <span class="join"> 加入
                        <?php if ($showPosition=='right'): ?>

                            <div class="join-code">
                        <?php else: ?>
                                <div class="join-code is-join-code">

                        <?php endif; ?>
                                <img src="<?=$v['img']?>" alt=""
                                     class="public-code">
                                <div class="news-part">
                                    <h4 class="scan-join">微信扫码立即进群</h4>
<!--                                    <span class="explain">请注明：所在城市-学历</span>-->
                                </div>
                            </div>
                        </span>
                </li>
                <?php endforeach ?>

            </ul>
        </div>
    </div>
</div>