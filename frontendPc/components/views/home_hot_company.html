<!--加载vue和jquery-->
<!--<script src="//img.gaoxiaojob.com/uploads/static/lib/jquery/jquery.min.js"></script>-->
<!--<script src="//img.gaoxiaojob.com/uploads/static/lib/vue/vue.min.js"></script>-->
<!--<script src="//img.gaoxiaojob.com/uploads/static/lib/element-plus/index.min.js?v=0.1"></script>-->
<!--<script src="/static/js/index.js?v=0.5"></script>-->
<div class="job-nav hot-college component w">
    <div class="common-title eliminate">
        <h2 class="subtitle">热门单位</h2>
    </div>
    <div class="list">
        <ul class="flex">
            <li class="region-type current">按地域查看</li>
            <li class="colleges-type">按单位类型查看</li>
        </ul>
    </div>
    <div class="more">
        <a href="/company" target="_blank">更多>></a>
    </div>
</div>

<div class="product-list">
    <div class="prodect-detail" style="display: block;">
        <div class="option college-change">
            <ul class="flex">
                <?php foreach ($areaList as $k => $v): ?>
                <li <?php if ($k==0): ?>class="play-1"<?php endif; ?>><?=$v['name']?></li>
                <?php endforeach ?>
            </ul>
        </div>

        <div class="tab-list">
            <?php foreach ($areaList as $k => $v): ?>
            <div class="college college-region" <?php if ($k==0): ?>style="display: block;"<?php endif; ?>>
                <ul>
                    <?php foreach ($v['list'] as $item ): ?>
                    <li>
                        <a href="<?=$item['url']?>" target="_blank" class="school-name flex showcase-browse" data-showcase-number="<?=$item['number'] ?>" data-showcase-id="<?=$item['id'] ?>" title="<?= \common\helpers\StringHelper::changeQuotationMark($item['title'])?>">
                            <div class="logo">
                                <img src="<?=$item['image_link'] ?>" alt="<?=$item['image_alt'] ?>" />
                            </div>
                            <div class="school-information">
                                <p><?=$item['title'] ?></p>
                                <span><?=$item['sub_title'] ?></span>
                            </div>
                        </a>
                    </li>
                    <?php endforeach ?>

                </ul>
            </div>
            <?php endforeach ?>
        </div>
    </div>

    <div class="prodect-detail" style="display: none;">
        <div class="option job-change">
            <ul class="flex">
                <?php foreach ($typeList as $k => $v): ?>
                <li <?php if ($k==0): ?>class="play-2"<?php endif; ?>><?=$v['name']?></li>
                <?php endforeach ?>
            </ul>
        </div>

        <div class="tab-list">
            <?php foreach ($typeList as $k => $v): ?>
            <div class="college college-job" <?php if ($k==0): ?>style="display: block;"<?php endif; ?>>
            <ul>
                <?php foreach ($v['list'] as $item ): ?>
                <li>
                    <a href="<?=$item['url']?>" target="_blank" class="school-name flex showcase-browse" data-showcase-number="<?=$item['number'] ?>" data-showcase-id="<?=$item['id'] ?>" title="<?= \common\helpers\StringHelper::changeQuotationMark($item['title'])?>">
                        <div class="logo">
                            <img src="<?=$item['image_link'] ?>" alt="<?=$item['image_alt'] ?>" />
                        </div>
                        <div class="school-information">
                            <p><?=$item['title'] ?></p>
                            <span><?=$item['sub_title'] ?></span>
                        </div>
                    </a>
                </li>
                <?php endforeach ?>

            </ul>
        </div>
        <?php endforeach ?>
        </div>
    </div>
</div>