<div class="left-content">

    <a href="/" target="_blank" class="logo"></a>
    <div class="switch-container" id="switch">
        <?php if ($showName): ?>
        <span class="countries"><?=$showName?></span>
        <span class="switch"> [ 切换 ]
        <div class="countries-city" style="display: none;">
            <!-- 全国站 -->
            <a href="/">全国</a>
            <?php foreach ($list as $v ): ?>
            <?php if ($v['active']): ?>
            <a href="<?=$v['url']?>" class="current-city" target="_blank"><?=$v['name']?></a>
            <?php else: ?>
            <a href="<?=$v['url']?>" target="_blank"><?=$v['name']?></a>
            <?php endif; ?>
            <?php endforeach ?>
        </div>
    </span>
        <?php endif; ?>
    </div>
    <?php if ($isHome): ?>
    &nbsp;&nbsp;
    <ul class="job-part">
        <li class="job-link">
            <a href="/job" target="_blank">职位大厅</a>
        </li>
        <li class="job-link vip">
            <a href="/vip.html" target="_blank">求职VIP<span class="arrow"></span>
                <span class="artifact">神器</span>
            </a>

            <div class="more">
                <a href="/job-fast.html" target="_blank">简历曝光</a>
                <a href="/vip.html" target="_blank">上岸直通车</a>
                <a href="/competitive-power.html" target="_blank">竞争力分析</a>
                <!--                <a href="http://t.jugaocai.com/SVWW" target="_blank">精选课程</a>-->
                <a href="/zhaopin/zhuanti/ycc_gaocaiyouke_v2/index.html" target="_blank">高校求职顾问</a>
                <a href="https://www.gaoxiaojob.com/zhaopin/zt/ycc_jianlifudao2025/index.html" target="_blank">简历诊断优化</a>
            </div>
        </li>
        <!-- 首页 -->
        <li class="job-link job-link__tool">
            <a href="https://zhaopinhui.gaoxiaojob.com" target="_blank">招聘会&引才活动</a>
            <!--            <a href="javascript:;">活动&服务</a>-->

            <ul class="job-link__tool-list">
                <?php foreach ($activityList as $k => $v): ?>
                <li class="job-link__tool-item">
                    <span class="tool-item__title"><?=$v['name']?></span>
                    <?php foreach ($v['list'] as $item): ?>
                    <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
                    <?php endforeach ?>
                    <?php endforeach ?>
            </ul>
        </li>
    </ul>
    <?php else: ?>
    <ul class="job-part">
        <li class="job-link header-notice-container">找公告
            <div class="notice-open-part">
                <div class="notice-content">
                    <div class="nav-list">
                        <div class="nav-title">
                            <p>栏目导航</p>
                        </div>
                        <ul class="is-nav-container nav-container">
                            <?php foreach ($hotColumn as  $item ): ?>
                            <li>
                                <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
                            </li>
                            <?php endforeach ?>
                        </ul>
                    </div>
                    <div class="nav-list">
                        <div class="nav-title">
                            <p>省区导航</p>
                        </div>
                        <ul class="nav-container">
                            <?php foreach ($provinceColumn as  $item ): ?>
                            <li>
                                <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
                            </li>
                            <?php endforeach ?>
                            <a class="more" href="/region.html" target="_blank">更多</a>
                        </ul>
                    </div>
                    <div class="nav-list">
                        <div class="nav-title">
                            <p>城市导航</p>
                        </div>
                        <div class="nav-container">
                            <ul class="nav-container">
                                <?php foreach ($cityColumn as  $item ): ?>
                                <li>
                                    <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
                                </li>
                                <?php endforeach ?>
                                <a href="/region.html" target="_blank" class="more">更多</a>
                            </ul>
                        </div>
                    </div>
                    <div class="nav-list">
                        <div class="nav-title">
                            <p>学科导航</p>
                        </div>
                        <ul class="is-nav-container nav-container">
                            <?php foreach ($majorColumn as  $item ): ?>
                            <li>
                                <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
                            </li>
                            <?php endforeach ?>
                            <a href="/major.html" target="_blank" class="more">更多</a>
                        </ul>
                    </div>
                </div>
            </div>
        </li>
        <li class="job-link">
            <a href="/job" target="_blank">找职位</a>
        </li>
        <li class="job-link">
            <a href="/company" target="_blank">找单位</a>
        </li>


        <!--        <li class="job-link">-->
        <!--            <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>-->
        <!--        </li>-->

        <li class="job-link vip">
            <a href="/vip.html" target="_blank">求职VIP<span class="arrow"></span>
                <span class="artifact">神器</span>
            </a>

            <div class="more">
                <a href="/job-fast.html" target="_blank">简历曝光</a>
                <a href="/vip.html" target="_blank">上岸直通车</a>
                <a href="/competitive-power.html" target="_blank">竞争力分析</a>
                <!--                <a href="http://t.jugaocai.com/SVWW" target="_blank">精选课程</a>-->
                <a href="/zhaopin/zhuanti/ycc_gaocaiyouke_v2/index.html" target="_blank">高校求职顾问</a>
                <a href="https://www.gaoxiaojob.com/zhaopin/zt/ycc_jianlifudao2025/index.html" target="_blank">简历诊断优化</a>
            </div>
        </li>

    </ul>
    <?php endif; ?>


</div>
<script src="/static/js/citySwitch.js"></script>