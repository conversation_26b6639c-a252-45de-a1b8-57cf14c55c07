<style>
    .footer-global-notice {
        overflow: visible;
        padding: 20px;
        border-radius: 10px;
        width: 350px;
    }

    .footer-global-notice.right {
        right: 45px;
    }

    .footer-global-notice .el-notification__title {
        text-align: center;
    }

    .footer-global-notice .el-notification__content {
        text-align: left;
        word-break: break-all;
    }

    .footer-global-notice .el-notification__content p {
        text-indent: 2em;
    }

    .footer-global-notice .el-notification__content a {
        color: #ffa000;
    }
</style>

<div id="globalNotice"></div>

<script>
    $(function () {
        const globalNoticeComponent = {
            mounted() {
                this.$notify({
                    title: "<?=$title?>",
                    customClass: "footer-global-notice",
                    duration: 0,
                    position: 'bottom-right',
                    dangerouslyUseHTMLString: true,
                    message: `<?=$message?>`
                });
            },

        }
        Vue.createApp(globalNoticeComponent).use(ElementPlus).mount('#globalNotice')
    });
</script>