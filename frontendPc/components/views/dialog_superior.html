<!-- 高级筛选 start -->
<link rel="stylesheet" href="/static/css/superiorDialog.css" />


<div id="superiorDialog" class="superior-dialog-template" v-cloak>
    <el-dialog v-model="visible" custom-class="superior-dialog" :close-on-click-modal="false">
        <template #title>
            <div class="title">高级筛选 <span class="tag">VIP会员专享筛选特权</span></div>
        </template>
        <div class="content">
            <div class="type-content">
                <div class="filter-type">
                    <div class="type-title">
                        <div class="label">
                            编制查询
                            <span class="ask">（单选）</span>
                            <el-tooltip placement="right">
                                <template #content>
                                    <div class="superior-popper-content" v-html="establishmentTips"></div>
                                </template>
                                <span class="answer"></span>
                            </el-tooltip>
                        </div>
                        <div class="desc">{{asideLabel.establishment}}</div>
                    </div>
                    <el-checkbox-group v-model="isEstablishment" @change="(e)=>handleChange('isEstablishment', e)">
                        <el-checkbox label="1"> 含编制 </el-checkbox>
                    </el-checkbox-group>
                </div>

                <div class="filter-type">
                    <div class="type-title">
                        <div class="label">
                            {{ hotLabel }}
                            <span class="ask">（单选）</span>
                            <el-tooltip placement="right">
                                <template #content>
                                    <div class="superior-popper-content" v-html="hotTips"></div>
                                </template>
                                <span class="answer"></span>
                            </el-tooltip>
                        </div>
                        <div class="desc">{{asideLabel.heat}}</div>
                    </div>
                    <el-checkbox-group v-model="heat" @change="(e)=>handleChange('heat', e)">
                        <el-checkbox label="1"> 一般 </el-checkbox>
                        <el-checkbox label="2"> 较热 </el-checkbox>
                        <el-checkbox label="3"> 火爆 </el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>

            <div class="filter-bottom">
                <el-button @click="handleReset">重置</el-button>
                <el-button @click="handleConfirm" type="primary">确定</el-button>
            </div>
        </div>
    </el-dialog>

    <el-dialog v-model="tipsVisible" custom-class="tips-dialog" :close-on-click-modal="false">
        <template #title>
            <div class="title">提示</div>
        </template>
        <div class="content">
            <div class="desc">开通VIP服务即可使用高级筛选功能， <br />精准捕捉目标职位&公告</div>
            <div class="tips">高级筛选功能包括：职位&公告 编制查询、热度情况筛选等</div>
            <div class="tips-bottom">
                <el-button @click="tipsVisible = false">取消</el-button>
                <el-button @click="handleUpgrade" type="primary">升级VIP</el-button>
            </div>
        </div>
    </el-dialog>
</div>

<script>
    $(function () {
        const superiorDialogOptions = {
            data() {
                return {
                    visible: false,
                    tipsVisible: false,

                    // 1 职位， 2 公告
                    type: 2,

                    isLogin: false,
                    isVip: false,

                    isEstablishment: [],
                    heat: [],

                    asideLabelOptions: {
                        job: {
                            establishment: '“上岸”快人一步',
                            heat: '追踪职位实时热度'
                        },
                        announcement: {
                            establishment: '捕捉每一次入编机会',
                            heat: '追踪公告实时热度'
                        }
                    },

                    callback: () => {}
                }
            },

            computed: {
                hotLabel() {
                    const { type } = this
                    return type === 1 ? '职位热度' : '公告热度'
                },

                asideLabel() {
                    const { type, asideLabelOptions } = this
                    return type === 1 ? asideLabelOptions['job'] : asideLabelOptions['announcement']
                },

                establishmentTips() {
                    const { type } = this
                    return type === 1 ? '可查询包含行政编制/事业编制/备案制等编制类型的优质职位' : '可查询包含编制职位的优质公告'
                },

                hotTips() {
                    const { type } = this
                    return type === 1
                        ? `根据职位的关注度、招录人数竞争比综合分析得出
                                        <br/>一般：表示关注人数较少，可重点关注；
                                        <br/>较热：表示该职位备受欢迎，可持续关注；
                                        <br/>火爆：表示该职位竞争激烈，可抓紧机会争取。`
                        : '根据公告的浏览、关注、投递情况等综合分析得出'
                }
            },

            methods: {
                show(type = 1, query = { isEstablishment: '', heat: '' }, callback = () => {}) {
                    this.type = type
                    const keys = Object.keys(query)
                    keys.forEach((key) => {
                        const value = query[key]
                        this[key] = value ? [value] : []
                    })
                    this.visible = true
                    this.fetchUserInfo()
                    this.callback = callback
                },

                async fetchUserInfo() {
                    const data = await httpGet('/api/person/member/get-vip-filter-info')
                    const { isLogin, isVip } = data
                    this.isLogin = isLogin
                    this.isVip = isVip
                },

                handleChange(type, value) {
                    const { isLogin, isVip } = this
                    if (!isLogin || !isVip) {
                        this[type] = []

                        if (!isLogin) {
                            window.globalComponents.loginDialogComponent.showLoginDialog()
                            return
                        }
                        if (!isVip) {
                            this.tipsVisible = true
                            return
                        }
                    }
                    const { length } = value
                    this[type] = length ? [value[length - 1]] : value
                },

                handleReset() {
                    this.isEstablishment = []
                    this.heat = []
                },

                handleConfirm() {
                    const { isEstablishment, heat, type } = this
                    const query = {
                        isEstablishment: isEstablishment.join(),
                        [type === 1 ? 'applyHeat' : 'announcementHeat']: heat.join()
                    }
                    this.callback(query)
                    this.visible = false
                },

                handleUpgrade() {
                    this.visible = false
                    this.tipsVisible = false
                    window.open('/vip.html', '_blank')
                }
            }
        }

        const SuperiorDialogComponent = Vue.createApp(superiorDialogOptions).use(ElementPlus).mount('#superiorDialog')

        window.globalComponents = {
            ...window.globalComponents,
            SuperiorDialogComponent
        }
    })
</script>
<!-- 高级筛选 end -->