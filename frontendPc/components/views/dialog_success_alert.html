<link rel="stylesheet" href="/static/css/dialogAlert.css?v=1">

<!--<div id="successDialogAlert" class="success-dialog-alert-template" v-cloak>-->
<!--    <el-dialog v-model="wechatVisible" @close="handelWxClose">-->
<!--        <div class="wechat-container" v-if="showWechatCode">-->
<!--            <div class="tips">-->
<!--                <strong>职位投递成功</strong>-->
<!--            </div>-->
<!--            <div class="qr-code" v-loading="loading">-->
<!--                <img :src="urCodeUrl" alt="" />-->
<!--                <img class="logo" src="https://img.gaoxiaojob.com/uploads/static/image/logo/logo_square.png" alt="" />-->
<!--            </div>-->
<!--            <div class="wechat-footer ft14 color-basic">-->
<!--                <div class="tips">-->
<!--                    <span>{{ applySuccessMsg }}</span>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="wechat-container" v-else>-->
<!--            <div class="tips">-->
<!--                <strong>职位投递成功</strong>-->
<!--            </div>-->
<!--            <div class="relogin-content">-->
<!--                <p class="write">{{ applySuccessMsg }}</p>-->
<!--                <el-button type="primary" @click="removeWechat"> 我知道了 </el-button>-->
<!--            </div>-->
<!--        </div>-->
<!--    </el-dialog>-->
<!--</div>-->

<div id="successDialogAlert" class="success-dialog-alert-template" v-cloak>
    <el-dialog v-model="wechatVisible" @close="handelWxClose">
        <div class="wechat-container" v-if="showWechatCode">
            <div class="tips">
                <strong>职位投递成功！</strong>
            </div>
            <div class="wechat-text">
                <div class="qr-code" v-loading="loading">
                    <img :src="urCodeUrl" alt="" />
                    <img class="logo" src="https://img.gaoxiaojob.com/uploads/static/image/logo/logo_square.png" alt="" />
                </div>
                <div class="text-right">
                    <span>微信扫码关注【高才-高校人才网服务号】</span>
                    <p>求职效率翻倍</p>
                    <div class="select">实时接收投递反馈</div>
                    <div class="select">精彩活动抢先知晓</div>
                </div>
            </div>

            <!-- <div class="wechat-footer ft14 color-basic">
                <div class="tips">
                    <span>{{ applySuccessMsg }}</span>
                </div>
            </div> -->
            <!-- <img class="recommend" src="assets/icon/recommend.png" alt="" /> -->
            <div class="recommend">为你推荐</div>
            <div class="relogin-content">
                <a href="/job-fast.html" class="analyse"  target="_blank" rel="求职快介绍">
                    <img src="/static/assets/icon/deliver-analyse.png" alt="" />
                    <div class="right-content">
                        <p>求职快</p>
                        <div class="text-analyse">
                            <span>想加快求职进程?</span>
                            <div class="see-analysis">去置顶投递</div>
                        </div>
                    </div>
                </a>
                <a href="/vip.html" target="_blank" rel="VIP介绍">
                    <div class="gaocai-vip">
                        <img src="/static/assets/icon/deliver-vip.png" alt="" />
                        <div class="right-content">
                            <p>高才VIP</p>
                            <div class="text-vip">
                                <span>11+项求职特权</span>
                                <span class="open">立即解锁</span>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
        <div class="wechat-container" v-else>
            <div class="tips">
                <strong>职位投递成功！</strong>
            </div>
            <div class="relogin-content">
                <a href="/job-fast.html" class="analyse"  target="_blank" rel="求职快介绍">
                    <img src="/static/assets/icon/deliver-analyse.png" alt="" />
                    <div class="right-content">
                        <p>求职快</p>
                        <div class="text-analyse">
                            <span>想加快求职进程吗?</span>
                            <div class="see-analysis">去置顶投递</div>
                        </div>
                    </div>
                </a>
                <a href="/vip.html" target="_blank" rel="VIP介绍">
                    <div class="gaocai-vip">
                        <img src="/static/assets/icon/deliver-vip.png" alt="" />
                        <div class="right-content">
                            <p>高才VIP</p>
                            <div class="text-vip">
                                <span>11+项求职特权</span>
                                <span class="open">立即解锁</span>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </el-dialog>
</div>

<script defer>
    readyRun(function () {
        const successDialogAlertOptions = {
                data() {
                    return {
                        successVisible: false,
                        wechatVisible: false,
                        showWechatCode: false,
                        applySuccessMsg: '',
                        params: {
                            apiPull: '/api/person/job/check-generate-report',
                            apiCreate: '/api/person/job/create-report',
                            param: { jobId: '' }
                        },
                        urCodeUrl: ''
                    }
                },

                methods: {
                    showSuccessDialogAlert(successData, jobId, callback = () => {}) {
                        const { wxBindQrCodeImageUrl, applySuccessMsg } = successData
                        this.params.param.jobId = jobId
                        this.applySuccessMsg = applySuccessMsg
                        if (wxBindQrCodeImageUrl) {
                            this.urCodeUrl = wxBindQrCodeImageUrl
                            this.showWechatCode = true
                        }
                        this.wechatVisible = true
                        callback()
                    },
                    removeWechat() {
                        this.wechatVisible = false
                    },
                    handelWxClose() {
                        this.wechatVisible = false
                    },
                    openAnalysis() {
                        window.globalComponents.PromptDialogComponent.pull(this.params)
                        this.wechatVisible = false
                    }
                }
            }

            const SuccessDialogAlertComponent = Vue.createApp(successDialogAlertOptions).use(ElementPlus).mount('#successDialogAlert')

            window.globalComponents = { ...window.globalComponents, SuccessDialogAlertComponent }

    })
</script>