<div id="sharing_widget" class="share-component" data-disabled="linkedin,douban,qzone,google"
     data-description="Share.js - 一键分享到微博，QQ空间，腾讯微博，人人，豆瓣" data-wechat-qrcode-helper="微信里点“发现”，扫码可在手机端随时查看，还可分享到朋友圈哦~"></div>


<link rel="stylesheet" href="/static/socializeShare/dist/css/share.min.css?v=0.1">
<script src="/static/socializeShare/dist/js/social-share.min.js"></script>
<script>
    // 加载完毕
    $(function () {
        // 分享组件
            var $img = $('#sharing_widget .wechat-qrcode .qrcode img')
            let src = '<?=$codeUrl?>';
            var $img2 = $('<img src="'+src+'" style="width: 150px;height: 150px" />')
            $('#sharing_widget .wechat-qrcode .qrcode').html($img2)
            // 找到 social-share-icon icon-wechat所在位置



    });
</script>