<!--<div class="nav">-->
<!--    <nav>-->
<!--        <?php foreach ($list as $key => $value): ?>-->
<!--        <a href="<?php echo $value['url'] ?>" target="_blank"><?php echo $value['name'] ?></a>-->
<!--        <?php endforeach ?>-->
<!--    </nav>-->
<!--</div>-->

<div class="nav">
    <nav>
        <ul>
            <li>
                <?php if ($isHome): ?>
                <a href="/" class="active">首页</a>
                <?php else: ?>
                <a href="/">首页</a>
                <?php endif; ?>
            </li>
            <?php foreach ($list as $key => $value): ?>
            <li>
                <?php if ($value['active']): ?>
                <a href="<?php echo $value['url'] ?>" target="_blank" class="active" title="<?php echo \common\helpers\StringHelper::changeQuotationMark($value['name'])?>"><?php echo $value['name'] ?>
                    <span></span></a>
                <?php else: ?>
                <a href="<?php echo $value['url'] ?>" target="_blank" class="<?php echo $value['class'] ?>" title="<?php echo \common\helpers\StringHelper::changeQuotationMark($value['name'])?>"><?php echo $value['name'] ?>
                    <span></span></a>
                <?php endif; ?>
                <div class="nav-text">
                    <div class="left-title">
                        <?php foreach ($value['children'] as $child): ?>
                        <a href="<?php echo $child['url'] ?>" target="_blank" title="<?php echo \common\helpers\StringHelper::changeQuotationMark($child['name'])?>"><?=$child['name']?></a>
                        <?php endforeach ?>
                    </div>
                    <div class="right-picture">
                        <a href="/column/277.html" target="_blank">
                            <img src="https://zt.gaoxiaojob.com/benke<?=date('Y')?>.jpg" alt=""/>
                        </a>
                        <a href="/column/273.html" target="_blank">
                            <img src="https://zt.gaoxiaojob.com/gaozhi<?=date('Y')?>.jpg" alt=""/>
                        </a>
                        <a href="/column/274.html" target="_blank">
                            <img src="https://zt.gaoxiaojob.com/dangxiao<?=date('Y')?>.jpg" alt=""/>
                        </a>
                        <a href="/column/272.html" target="_blank">
                            <img src="https://zt.gaoxiaojob.com/shuangyiliu<?=date('Y')?>.jpg" alt=""/>
                        </a>
                        <a href="/column/275.html" target="_blank">
                            <img src="https://zt.gaoxiaojob.com/boshi<?=date('Y')?>.jpg" alt=""/>
                        </a>
                    </div>
                </div>
            </li>
            <?php endforeach ?>
            <li>
                <a href="/region.html" target="_blank">地区<span></span></a>
                <div class="region-container">
                    <div class="region-information">
                        <!-- 省份为标题不跳转 -->
                        <a href="javascript:;" class="region-title region-link">省份:</a>
                        <?php foreach ($province as $item ) { ?>
                        <a href="<?=$item['url']?>" target="_blank" class="region-link"><?=$item['name']?></a>
                        <?php } ?>
                        <a href="/region.html" target="_blank" class="region-link more-region">更多</a>
                    </div>

                    <div class="region-information">
                        <!-- 城市为标题不跳转 -->
                        <a href="javascript:;" class="region-title region-link">城市:</a>
                        <?php foreach ($city as $item ) { ?>
                        <a href="<?=$item['url']?>" target="_blank" class="region-link"><?=$item['name']?></a>
                        <?php } ?>
                        <a href="/region.html" target="_blank" class="region-link more-region">更多</a>
                    </div>
                </div>
            </li>
        </ul>
    </nav>
</div>