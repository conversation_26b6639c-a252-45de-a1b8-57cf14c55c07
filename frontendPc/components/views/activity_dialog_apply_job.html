<link rel="stylesheet" href="/static/css/activityDialogApplyJob.css?v=1.0.0">
<div id="activityApplyJob" class="activity-apply-dialog-template" v-cloak>
    <el-dialog v-model="dialogVisible" title="立即报名" :center="true" @close="close">
        <el-form label-width="100px" ref="applyFormRef">
            <el-form-item label="选择报名场次" prop="jobId">
                <el-select v-model="jobId" placeholder="选择场次">
                    <el-option v-for="{id, name ,type } in announcementJobList" :key="id" :label="name" :value="id" :disabled="type">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="close">取消</el-button>
                <el-button type="primary" :loading="confirmLoading" @click="beforeApply(jobId)">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>


</div>

<div id="successDialog" class="success-dialog-template" v-cloak>
    <el-dialog v-model="successVisible" class="success-dialog" title="报名成功" :center="true">
        <div class="sussess-box">
            <div>{{successContentUp}}</div>
            <div>{{successContentDown}}</div>
            <img :src="qrcodeLink"></img>
            <el-button type="primary" @click="successVisible=false">
                我知道了
            </el-button>
        </div>
    </el-dialog>
</div>

<script>
    $(function () {

        const applyDialogOptions = {
            data() {
                return {
                    dialogVisible: false,
                    announcementApi: '/api/person/announcement/check-announcement-apply',
                    beforeApi: '/api/person/member/check-user-apply-status',
                    applyApi: '/api/person/job/apply',
                    announcementJobList: [],
                    jobId: '',
                }
            },


            methods: {
            async getJobList(announcementId) {
                    const { toastDialogData, jobList } = await httpPost(this.announcementApi, { announcementId })

                    if (Object.keys(toastDialogData).length !== 0) {
                        this.showTipsDialog(toastDialogData)
                        return
                    }

                    this.announcementJobList = jobList

                    const jobId = this.announcementJobList?.map((item) => item.id)[0]

                    this.announcementJobList?.forEach((item) => {
                        if (item.active) {
                            this.jobId = item.id
                        }
                    }) 

                    jobId ? (this.dialogVisible = true) : this.$message.error('暂无场次可报名，敬请期待')
                },


                showTipsDialog(data) {
                    const { title, content, confirmButtonText, cancelButtonText, link, isSendRequest } = data

                    this.$confirm(content, title, { confirmButtonText, cancelButtonText, buttonSize: 'large' })
                        .then(() => {
                            window.open(link)

                            if (isSendRequest) {
                                httpPost(this.applyApi, { jobId: this.jobId })
                                this.close()
                            }
                        })
                        .catch(() => { 
                            this.close()
                        })
                },

                async beforeApply(jobId, callback) {
                    this.jobId = jobId

                    const { toastDialogData } = await httpPost(this.beforeApi, { jobId })

                    if (Object.keys(toastDialogData).length !== 0) {
                        const { isAutoJump, isSendRequest, link } = toastDialogData

                        if (isAutoJump) {
                            if (isSendRequest) {
                                httpPost(this.applyApi, { jobId })
                            }
                            this.dialogVisible = false
                            window.open(link, '_blank')
                            return
                        }

                        this.showTipsDialog(toastDialogData)
                        return
                    }

                    this.callback = callback

                    this.handleDeliver()
                },


                handleDeliver() {
                    this.confirmLoading = true

                    httpPost(this.applyApi, { jobId: this.jobId })
                        .then((res)=> {
                            this.confirmLoading = false
                            window.globalComponents.successDialogApplyJob.showSuccessDialog(res, this.callback)

                        } )
                        .catch(error => {})
                        
                        this.confirmLoading = false
                        this.close()
                },

                close() {
                    this.$refs.applyFormRef.resetFields()
                    this.jobId = ''
                    this.dialogVisible = false
                }
            }
        }
        const activityDialogApplyJob = Vue.createApp(applyDialogOptions).use(ElementPlus).mount('#activityApplyJob')

        window.globalComponents = { ...window.globalComponents, activityDialogApplyJob }
    })





</script>

<script>
    $(function () {

        const successDialogOptions = {
            data() {
                return {
                    qrcodeLink: '',
                    successContentUp: '',
                    successContentDown: '',
                    successVisible: false,
                }
            },


            methods: {
                showSuccessDialog(successData, callback) {
                    const { qrcodeLink, successContentUp, successContentDown } = successData
                    this.qrcodeLink = qrcodeLink
                    this.successContentUp = successContentUp
                    this.successContentDown = successContentDown
                    this.successVisible = true
                    callback()
                }
            }
        }
        const successDialogApplyJob = Vue.createApp(successDialogOptions).use(ElementPlus).mount('#successDialog')

        window.globalComponents = { ...window.globalComponents, successDialogApplyJob }
    })

</script>
<!--<div id="applyActivityDialogTemplate" class="apply-dialog-template" v-cloak>-->
<!--    <el-dialog v-model="dialogVisible" title="立即报名" :center="true">-->
<!--        <el-form :model="resumeFormData" label-width="100px">-->
<!--            <div class="education-tips" v-if="checkTextOne">{{checkTextOne}}</div>-->
<!--            <el-form-item label="报名场次" v-if="isAnnouncement">-->
<!--                <el-select v-model="resumeFormData.jobId" placeholder="选择场次" @change="changeJob">-->
<!--                    <el-option v-for="{id, name} in announcementJobList" :key="id" :label="name" :value="id">-->
<!--                    </el-option>-->
<!--                </el-select>-->
<!--            </el-form-item>-->

<!--        </el-form>-->
<!--        <template #footer>-->
<!--            <div class="dialog-footer">-->
<!--                <el-button  @click="close">取消</el-button>-->
<!--                <el-button type="primary" :loading="confirmLoading" @click="handleDeliver" :disabled="!isDelivery">-->
<!--                    确定-->
<!--                </el-button>-->
<!--            </div>-->
<!--            <div class="dialog-tips" v-if="checkTextFive">-->
<!--                {{checkTextFive}}-->
<!--            </div>-->
<!--        </template>-->
<!--    </el-dialog>-->
<!--</div>-->

<!--<script defer>-->

<!--    readyRun(function () {-->
<!--            const applyDialogOptions = {-->
<!--                data() {-->
<!--                    return {-->
<!--                        isAnnouncement: false,-->
<!--                        dialogVisible: false,-->
<!--                        resumePercent: 0,-->
<!--                        limit: 5,-->
<!--                        jobListApi: '/api/person/announcement/job-list',-->
<!--                        beforeApi: '/api/person/member/check-user-apply-status',-->
<!--                        applyApi: '/api/person/job/apply',-->
<!--                        resumeUploadUrl: '/api/person/resume/upload',-->
<!--                        attachmentUploadUrl: '/api/upload/resume-attachment',-->
<!--                        resumeOptions: [],-->
<!--                        announcementJobList: [],-->
<!--                        resumeFormData: {-->
<!--                            jobId: '',-->
<!--                            token: '',-->
<!--                            isDefault: '2',-->
<!--                            stuffFileId: []-->
<!--                        },-->
<!--                        successCallback: null,-->
<!--                        toastType: null,-->
<!--                        checkTextOne: '',-->
<!--                        checkTextTwo: '',-->
<!--                        checkTextThree: '',-->
<!--                        checkTextFour: '',-->
<!--                        checkTextFive: '',-->
<!--                        applyButtonStatus: null,-->
<!--                        onLink: '',-->
<!--                        isDelivery: false-->
<!--                    }-->
<!--                },-->


<!--                methods: {-->
<!--                    async getJobList(announcementId) {-->
<!--                        const res = await httpGet(this.jobListApi, { announcementId })-->
<!--                        this.announcementJobList = res-->
<!--                        const jobId = this.announcementJobList?.map((item) => item.id)[0]-->
<!--                        if (!jobId) {-->
<!--                            this.$message.error('暂无职位可投递，敬请期待')-->
<!--                        } else {-->
<!--                            this.showApplyDialog(jobId)-->
<!--                        }-->
<!--                    },-->

<!--                    // 页面调用申请之前-->
<!--                    beforeApply(id, callback = () => { }) {-->
<!--                        this.isAnnouncement = false-->
<!--                        this.showApplyDialog(id, callback)-->
<!--                    },-->

<!--                    async showApplyDialog(id, callback = () => { }) {-->
<!--                        const { toastType, checkTextOne, checkTextTwo, checkTextThree, checkTextFour, checkTextFive, defaultResumeAttachment, applyButtonStatus, resumeAttachmentList, onLink,isDelivery } = await httpPost(this.beforeApi, { jobId: id })-->
<!--                        this.resumeOptions = resumeAttachmentList-->
<!--                        this.resumeFormData.jobId = id-->
<!--                        this.resumeFormData.token = defaultResumeAttachment-->
<!--                        this.toastType = toastType-->
<!--                        this.checkTextOne = checkTextOne-->
<!--                        this.checkTextTwo = checkTextTwo-->
<!--                        this.checkTextThree = checkTextThree-->
<!--                        this.checkTextFour = checkTextFour-->
<!--                        this.checkTextFive = checkTextFive-->
<!--                        this.isDelivery=isDelivery-->
<!--                        this.onLink = onLink-->
<!--                        this.successCallback = callback-->
<!--                        this.handleJobApply()-->
<!--                    },-->

<!--                    async announcementApply(id) {-->
<!--                        this.isAnnouncement = true-->
<!--                        this.getJobList(id)-->
<!--                    },-->

<!--                    handleJobApply() {-->
<!--                        const { resumeFormData, successCallback, toastType, checkTextOne, checkTextTwo, checkTextThree, checkTextFour, checkTextFive, onLink } = this-->
<!--                        if (this.isAnnouncement) {-->
<!--                            if (toastType === 2 && onLink) {-->
<!--                                this.checkTextFive = checkTextOne-->
<!--                                this.checkTextOne = ''-->
<!--                            }-->
<!--                            this.dialogVisible = true-->
<!--                        } else {-->
<!--                            if (toastType === 1) {-->
<!--                                this.dialogVisible = true-->
<!--                            } else {-->
<!--                                this.$confirm(checkTextOne, '简历投递', {-->
<!--                                    cancelButtonText: '取消',-->
<!--                                    confirmButtonText: '去投递',-->
<!--                                    buttonSize: 'large'-->
<!--                                }).then(async () => {-->
<!--                                    await httpPost(this.applyApi, resumeFormData)-->
<!--                                    successCallback()-->
<!--                                    if (onLink) {-->
<!--                                        window.open(onLink)-->
<!--                                    }-->
<!--                                }).catch(() => { })-->
<!--                            }-->
<!--                        }-->
<!--                    },-->

<!--                    handleDeliver() {-->
<!--                        if (this.isAnnouncement && this.onLink) {-->
<!--                            window.open(this.onLink)-->
<!--                            this.dialogVisible = false-->
<!--                            return-->
<!--                        }-->
<!--                        this.confirmLoading = true-->
<!--                        const { resumeFormData: { stuffFileId, ...data } } = this-->
<!--                        const postData = { ...data, stuffFileId: '' }-->

<!--                        if (stuffFileId.length) {-->
<!--                            const result = stuffFileId.reduce((previous, current) => {-->
<!--                                previous.push(current.response.data.id)-->
<!--                                return previous-->
<!--                            }, [])-->
<!--                            postData.stuffFileId = result.join()-->
<!--                        }-->

<!--                        httpPost(this.applyApi, postData).then(() => {-->
<!--                            this.resumeFormData.stuffFileId = []-->
<!--                            this.dialogVisible = false-->
<!--                            if (!this.isAnnouncement) {-->
<!--                                this.successCallback()-->
<!--                            }-->
<!--                            if (this.dialogTipsVisible) {-->
<!--                                this.$alert(`已自动帮您添加该职位至“投递”&#45;&#45;“站外应聘”菜单，请稍后查看。`, '职位投递成功', {-->
<!--                                    confirmButtonText: '我知道了',-->
<!--                                    buttonSize: 'large'-->
<!--                                })-->
<!--                            } else {-->
<!--                                this.$alert(``, '职位投递成功', {-->
<!--                                    confirmButtonText: '我知道了',-->
<!--                                    buttonSize: 'large'-->
<!--                                })-->
<!--                            }-->
<!--                        }).catch(error => {-->
<!--                            // this.$message.error(error)-->
<!--                        })-->
<!--                        this.confirmLoading = false-->
<!--                    },-->

<!--                    uploadResumeSuccess(response, file, fileList) {-->
<!--                        const { data, msg, result } = response-->
<!--                        if (result === 1) {-->
<!--                            const { resumeOptions } = this-->
<!--                            const { name, token } = data-->
<!--                            this.resumeOptions = [...resumeOptions, { label: name ? name : file.name, value: token }]-->
<!--                            this.resumeFormData.token = token-->
<!--                            this.resumeFormData.isDefault = '1'-->
<!--                        } else {-->
<!--                            this.$message.error(msg)-->
<!--                        }-->
<!--                    },-->
<!--                    changeJob(id) {-->
<!--                        this.showApplyDialog(id)-->
<!--                    },-->
<!--                    uploadAttachSuccess(response, file, fileList) {-->
<!--                        this.resumeFormData.stuffFileId = fileList-->
<!--                    },-->

<!--                    uploadAttachError(error, file, fileList) {-->
<!--                        const { msg } = JSON.parse(error.message)-->
<!--                        this.$message.error(msg)-->
<!--                    },-->

<!--                    uploadAttachRemove(file, fileList) {-->
<!--                        this.resumeFormData.stuffFileId = fileList-->
<!--                    },-->

<!--                    uploadAttachExceed(files, uploadFiles) {-->
<!--                        this.$message.warning(`目前只允许上传${this.limit}个应聘材料`)-->
<!--                    },-->

<!--                    beforeFileUpload(rawFile) {-->
<!--                        if (rawFile.size / 1024 / 1024 > 20) {-->
<!--                            this.$message.error('附件大小不能超过20M')-->
<!--                            return false-->
<!--                        }-->
<!--                    },-->

<!--                    close() {-->
<!--                        this.dialogVisible = false-->
<!--                    }-->
<!--                }-->
<!--            }-->
<!--            const activeApplyDialogComponent = Vue.createApp(applyDialogOptions).use(ElementPlus).mount('#applyActivityDialogTemplate')-->

<!--            window.globalComponents = { ...window.globalComponents, activeApplyDialogComponent }-->

<!--    })-->
<!--</script>-->