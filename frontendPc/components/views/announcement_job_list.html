<section>
    <div class="tips">
        声明：本站部分公告与职位内容由本站根据官方招聘公告进行整理编辑。由于用人单位需求专业、学历学位、资格条件、备注内容等内容情况复杂且有变化可能，是否符合招聘条件以用人单位公告为准或请联系用人单位确认。本站整理编辑的职位信息仅供求职者参考，如因此造成的损失本站不承担任何责任！
    </div>
    <?php if(!empty($jobFilter)){ ?>
    <div class="filter" id="filterTemplate">
        <el-select :class="{'is-select': jobType}" v-model="jobType" @change="(value) => handleFilter(value, 'jobType')" :clearable="true" placeholder="职位类型">
            <el-option v-for="item in jobCategoryList" :key="item.k" :label="item.v" :value="item.k"> </el-option>
        </el-select>

        <el-select :class="{'is-select': educationId}" v-model="educationId" @change="(value) => handleFilter(value, 'educationId')" :clearable="true" placeholder="学历">
            <el-option v-for="item in educationOptions" :key="item.k" :label="item.v" :value="item.k"> </el-option>
        </el-select>

        <el-select :class="{'is-select': majorId}" v-model="majorId" @change="(value) => handleFilter(value, 'majorId')" :clearable="true" placeholder="专业">
            <el-option v-for="item in majorOptions" :key="item.k" :label="item.v" :value="item.k"> </el-option>
        </el-select>
    </div>
    <?php }?>
    <?php if($jobCount>0){ ?>
    <div>
        <table class="el-table">
            <tr>
                <th class="el-col el-col-10">职位信息</th>
                <th class="el-col el-col-8">需求专业</th>
                <th class="el-col el-col-6">操作</th>
            </tr>
            <?php foreach($jobList as $k=>$item):?>
            <tr data-href="<?=$item['url']?>">
                <td class="data-name el-col el-col-10">
                    <a href="<?=$item['url']?>" target="_blank"><?=$item['jobName']?></a>
                    <p>
                        <strong class="salary"><?=$item['wage']?></strong>
                        <?php if($item['area']):?><span><?=$item['area']?></span><?php endif;?>
                        <?php if($item['education']):?><span><?= $item['education']?></span><?php endif;?>
                        <?php if($item['nature']):?><span><?=$item['nature']?></span><?php endif;?>
                    </p>
    
                </td>
    
                <td class="data-major el-col el-col-8">
                    <span><?=$item['major']?></span>
                </td>
    
                <td class="data-operate el-col el-col-6">
                    <?php if($item['collectStatus'] == 1): ?>
                    <button class="el-button el-button--collect job-collect-button collected"
                            data-id="<?=$item['jobId']?>">
                        <span>已收藏</span>
                    </button>
                    <?php else:?>
                    <button class="el-button el-button--collect job-collect-button"
                            data-id="<?=$item['jobId']?>">
                        <span>收藏</span>
                    </button>
                    <?php endif;?>
    
                    <?php if($item['status'] == 0): ?>
                    <button class="el-button el-button--primary is-disabled offline" disabled>
                        <span>已下线</span>
                    </button>
                    <?php else:?>
                    <?php if($item['applyStatus'] == 1):?>
                    <button class="el-button el-button--primary is-disabled" disabled>
                        <span>已申请</span>
                    </button>
                    <?php else:?>
                    <button class="el-button el-button--primary job-apply-button"
                            data-id="<?=$item['jobId']?>">
                        <span>申请职位</span>
                    </button>
                    <?php endif;?>
                    <?php endif;?>
                </td>
            </tr>
    
            <?php endforeach?>
        </table>
    
        <div id="paginationTemplate">
            <el-pagination background :layout="'total, sizes, prev, pager, next, jumper'"
                           :current-page="page" :page-size="pageSize"
                           :total="count"
                           :total="count" @size-change="(val) => handleFilter(val, 'pageSize')"
                           @current-change="(val) => handleFilter(val, 'page')">
            </el-pagination>
        </div>
    </div>
    <?php } else{?>
    <div class="no-data">暂无相关职位，请修改条件试试</div>
    <?php }?>
</section>

<script>
    $(function(){
        function updQuery(data) {
        const base = window.location.href
        const hasParams = base.indexOf('?') > -1
        const baseUrl = base + (hasParams ? '' : '?')
        const keys = Object.keys(data)

        const result = keys.reduce((previous, current) => {
            const value = data[current]
            const isValid = value === null ? false : value !== ''
            const isExist = new RegExp(`(${current}=[^&]*)`).test(previous)
            const keyValue = isExist ? RegExp.$1 : ''

            if (isValid) {
                if (isExist) {
                    previous = previous.replace(keyValue, `${current}=${value}`)
                } else {
                    previous += `&${current}=${encodeURIComponent(value)}`
                }
            } else {
                previous = previous.replace(new RegExp(`&?${keyValue}`), '')
            }

            return previous.replace(/\?&/, '?')
        }, baseUrl)

        return result.replace(/\?$/, '')
    }

    const paginationOptions = {
        data() {
            return {
                page: 1,
                pageSize: 20,
                count:  <?= $jobCount ?>
            }
        },
        mounted() {
            const search = window.location.search
            const [mark, query] = search.split('?')
            const getParams = (key, feature = '') => {
                if (new RegExp(`${key}=([^&]*)`).test(query)) {
                    const value = decodeURIComponent(RegExp.$1)
                    const toNumber = (val) => (/^-?\d+$/.test(val) ? val * 1 : val)
                    if (feature) {
                        this[key] = value.split(feature).map((item) => toNumber(item))
                    } else {
                        this[key] = toNumber(value)
                    }
                }
            }

            if (query) {
                getParams('page')
                getParams('pageSize')
            }
        },
        methods: {
            handleFilter(val, key) {
                const query = { [key]: Array.isArray(val) ? val.join('_') : val }
                if (key !== 'page') {
                    query.page = 1
                }
                window.location.href = updQuery(query)
            }
        }
    }

    Vue.createApp(paginationOptions)
        .use(ElementPlus, {
            locale: {
                name: 'zh-cn',
                el: {
                    pagination: {
                        goto: '前往',
                        pagesize: '条/页',
                        total: '共 {total} 条',
                        pageClassifier: '页',
                        deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                    }
                }
            }
        })
        .mount('#paginationTemplate')

    const filterOptions = {
        data() {
            return {
                jobType: '',
                majorId: '',
                educationId: '',
                jobCategoryList: <?=json_encode($jobFilter['jobCategoryFilter'])?>,
                majorOptions: <?=json_encode($jobFilter['jobMajorFilter'])?>,
                educationOptions: <?=json_encode($jobFilter['jobEducationTypeFilter'])?>
            }
        },
        mounted() {
            const search = window.location.search
            const [mark, query] = search.split('?')
            const getParams = (key, feature = '') => {
                if (new RegExp(`${key}=([^&]*)`).test(query)) {
                    const value = decodeURIComponent(RegExp.$1)
                    if (feature) {
                        this[key] = value.split(feature)
                    } else {
                        this[key] = value
                    }
                }
            }

            if (query) {
                getParams('jobType')
                getParams('majorId')
                getParams('educationId')
            }
        },
        methods: {
            handleFilter(val, key) {
                const query = { [key]: Array.isArray(val) ? val.join('_') : val }
                if (key !== 'page') {
                    query.page = 1
                }
                window.location.href = updQuery(query)
            }
        }
    }

    Vue.createApp(filterOptions)
        .use(ElementPlus)
        .mount('#filterTemplate')

    })
</script>