<div class="<?php if($count>0) {echo 'd1 cardtab'; } ?> w">
    <?php if($count>0) { ?>
    <div class="common-title">
        <h2 class="subtitle">热门单位</h2>
    </div>
    <div class="navbox tabnav">
        <ul class="flex">
            <?php foreach ($tapList as $k => $v) { ?>
            <!--            list-change-->
            <li class="<?php if($k==0){echo 'list-change';}?>">
                <?php echo $v['name'] ?>
            </li>
            <?php } ?>
        </ul>
    </div>
    <div class="cardcon">
        <?php foreach ($list as $key => $project) { ?>
        <div class="t-card card">
            <!-- <div class="card-position">华东地区：上海、江苏、浙江、安徽、福建、江西、山东等</div> -->
            <div class="swiper cardlist">
                <div class="swiper-wrapper">
                    <?php foreach ( $project as $i=>$value) {?>
                    <div class="swiper-slide">
                        <div class="content-tab">
                            <ul>
                                <?php foreach ($value as $k => $item) { ?>
                                <li>
                                    <a href="<?=$item['url'] ?>" target="_blank" class="showcase-browse"
                                       data-showcase-number="<?php echo $item['number'] ?>"
                                       data-showcase-id="<?php echo $item['id'] ?>">
                                        <img src="<?php echo $item['image_link'] ?>" alt="<?php echo $item['image_alt'];?>"/>
                                        <p title="<?=\common\helpers\StringHelper::changeQuotationMark($item['title'])?>">
                                            <?php echo $item['title'] ?>
                                        </p>
                                        <span title="<?=\common\helpers\StringHelper::changeQuotationMark($item['sub_title'])?>">
                                            <?php echo $item['sub_title'] ?>
                                        </span>
                                    </a>
                                </li>
                                <?php } ?>
                            </ul>
                        </div>
                    </div>
                    <?php }?>
                </div>
                <div class="swiper-pagination cardlist-pagination"></div>
            </div>
        </div>
        <?php } ?>
    </div>
    <?php } ?>
</div>
<style>
    .cardcon .t-card {
        display: none;
    }

    .cardcon .t-card:nth-child(1) {
        display: block;
    }
</style>