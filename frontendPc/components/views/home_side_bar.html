<?php if ($isShow): ?>
<div class="sidebar-container">
    <div class="guild-part">
        <a href="<?php if($isLogin):?>/member/person/home<?php else:?>javascript:;<?php endif?>" target="_blank" class="tools-link is-home">主页</a>

        <a href="<?php if($isLogin):?>/member/person/resume<?php else:?>javascript:;<?php endif?>" target="_blank" class="tools-link is-resume">简历</a>

        <a href="<?php if($isLogin):?>/member/person/invite<?php else:?>javascript:;<?php endif?>" target="_blank" class="tools-link is-invite">
            <span>邀约</span>
            <?php if ($jobInviteCount): ?>
                <sup class="badge"><?=$jobInviteCount?></sup>
            <?php endif; ?>
        </a>

        <a href="<?php if($isLogin):?>/member/person/delivery<?php else:?>javascript:;<?php endif?>" target="_blank" class="tools-link is-delivery">
            <span>投递</span>
            <?php if ($jobApplyAllCount): ?>
                <sup class="badge"><?=$jobApplyAllCount?></sup>
            <?php endif; ?>
        </a>

        <a href="<?php if($isLogin):?>/member/person/chat<?php else:?>javascript:;<?php endif?>" target="_blank" class="tools-link is-chat">
            <span>直聊</span>
            <?php if ($chatAmount): ?>
            <sup class="badge"><?=$chatAmount?></sup>
            <?php endif; ?>
        </a>

        <!-- <a href="/member/person/collection" target="_blank" class="tools-link is-collect">收藏</a> -->

        <!-- <a href="/member/person/message" target="_blank" class="tools-link is-news">消息</a> -->
    </div>

    <div class="status-part" id="statusTemplate">

        <a href="javascript:;" class="link-icon miniapp">
            <span>小程序</span>
            <span class="miniapp-hover">扫码进入小程序</span>
        </a>
        <a href="javascript:;" class="link-icon weixin">
            <span>公众号</span>
            <span class="weixin-hover">高才-高校人才网公众号</span>
        </a>

        <a href="javascript:;" class="link-icon mobile">
            <span>移动端</span>
            <span class="mobile-hover">查看高校人才网移动端</span>
        </a>

        <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
            <template #reference>
                <a href="javascript:;" class="link-icon feedback-link">
                                <span>
                                    咨询<br />
                                    反馈
                                </span>
                    <span class="mobile-hover">查看高校人才网移动端</span>
                </a>
            </template>

            <template #default>
                <div class="feedback-detail">
                    <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                        <h6>商务合作</h6>
                        <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                    </a>

                    <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                        <h6>意见反馈</h6>
                        <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                    </a>

                    <div class="customer-service">
                        <h6>联系客服</h6>
                        <div>
                            <p>平台功能体验等问题请联系：</p>
                            <p><strong>电话：</strong>020-85611139 ***********</p>
                            <p><strong>微信：</strong>***********</p>
                            <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                            <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                            <p>求职会员服务问题请咨询：</p>
                            <p><strong>微信：</strong>gzgxrcw14</p>
                        </div>
                    </div>
                </div>
            </template>
        </el-popover>
    </div>
</div>

<script>
    $(function () {
        $('.sidebar-container .guild-part').on('click', 'a', function (event) {
            var href = $(this).attr('href')

            if (href.indexOf(';') > -1) {
                event.preventDefault()
                window.globalComponents.loginDialogComponent.showLoginDialog()
            }
        })
    })
    Vue.createApp({}).use(ElementPlus).mount('#statusTemplate')
</script>

<?php endif; ?>