<link rel="stylesheet" href="/static/css/cascaderDialog.css">

<div id="cascaderDialogJobTypeTemplate" class="cascader-dialog-template" v-cloak>
    <el-dialog v-model="visible" :close-on-click-modal="false" custom-class="dialog-center"
        @close="handleClose">
        <template #title>
            <div class="cascader-header">
                <div class="title">{{ title }}</div>
                <div class="tips">
                    (最多选择<span>{{ multipleLimit }}</span>项)
                </div>
                <div class="search">
                    <el-select v-model="searchValue" :remote-method="handleSearch" filterable remote reserve-keyword :size="size"
                        placeholder="请输入职位关键词" @change="handleSearchChange">
                        <el-option v-for="item in searchOptions" :key="item.k" :label="item.v" :value="item.k" />
                    </el-select>
                </div>
            </div>
        </template>
        <div class="cascader-content">
            <div v-show="!!valueItems.length" class="select">
                <div class="select-label">已选：</div>
                <div class="select-value">
                    <el-tag v-for="item in valueItems" :key="item.k" @close="handleRemove(item)" class="tag"
                        closable>
                        <span class="el-tag__content">{{ item.v }}</span>
                    </el-tag>
                </div>
            </div>
            <div class="data-content">
                <div class="left-content">
                    <a @click="handleClickFirst(index)" v-for="(item, index) in options" :key="item.k"
                        href="JavaScript:;" class="list" :class="{
            active: selectFirstIndex === index,
            'has-select': handleFirstClass(item.k)
          }">{{ item.v }}</a>
                </div>
                <div class="right-content">
                    <div v-for="(first, first_index) in options" :key="first_index" class="second-content"
                        :class="{ show: selectFirstIndex == first_index }">
                        <div class="second-list-content column-4" >
                            <div v-for="second in first.children" :key="second.k" class="second-item only">
                                <label>
                                    <input @input="
                (e) => {
                    handleSelect(e, second)
                }
                " class="btn-select" name="value" :value="second.k" v-model="value" type="checkbox" />
                                    <span>{{ second.v }}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="multiple" class="cascader-footer">
                <el-button @click="submit" class="submit" type="primary" :size="size">确定</el-button>
            </div>
        </div>
    </el-dialog>
</div>

<script>
    $(function () {
        const cascaderDialogOptions = {
            data() {
                return {
                    multiple: true,
                    multipleLimit: 5,
                    title: '请选择职位类型',

                    size: 'small',

                    visible: false,

                    realValue: '',

                    // 选中的值
                    value: [],
                    valueItems: [],
                    valueText: '',

                    searchValue: '',

                    // 一级索引
                    selectFirstIndex: 0,

                    // 真实一级标识
                    firstMarkKey: [],

                    // 真实二级标识
                    secondMarkKey: [],

                    // 数据
                    options:[
                        <?php foreach($list as $k=>$v):?>
                        {
                            "k": '<?=$v["k"]?>',
                            "v": '<?=$v["v"]?>',
                            "children": [
                            <?php foreach($v['children'] as $kk=>$vv):?>
                            {
                                "k": '<?=$vv["k"]?>',
                                "v": '<?=$vv["v"]?>',
                                "topParentId": '<?=$v["k"]?>',
                            },
                            <?php endforeach; ?>
                        ]
                        },
                <?php endforeach; ?>
                    ],
                    // 最后子级，用于搜索
                    lastChildrenOptions: [],

                    // 搜索数据
                    searchLoading: false,
                    searchOptions: [],

                    successCallback: null
                }
            },

            computed: {},

            watch: {
                value:{
                    handler(newValue){
                        this.recursive()
                    },
                    deep: true

                }
            },

            mounted() {
                this.getLastChildren()
            },

            methods: {
                async open(value, callback = () => { }) {
                    let newValue
                    if (Array.isArray(value)) {
                        newValue = value
                    } else {
                        newValue = value ? String(value).split(',') : []
                    }
                    this.realValue = newValue
                    this.value = newValue
                    this.recursive('init')
                    this.successCallback = callback
                    this.visible = true
                },
                getValueText() {
                    this.valueText = this.valueItems.map((item) => item.v).join()
                },
                initOpen(firstActiveIndex, secondOpenkey) {
                    this.selectFirstIndex = firstActiveIndex
                    this.secondOpenkey = secondOpenkey
                },

                // 获取父级标识
                getParentMark(data){
                    const { parentId, topParentId } = data
                    this.secondMarkKey.push(parentId)
                    this.firstMarkKey.push(topParentId)
                },

                // 清除父级标识
                clearParentMark () {
                    this.firstMarkKey = []
                    this.secondMarkKey = []
                },

                handleSecondClass(k) {
                    const has = this.secondMarkKey.includes(k)
                    return has
                },

                handleFirstClass(k) {
                    const has = this.firstMarkKey.includes(k)
                    return has
                },
                  // 已选去重, 例如城市-热门城市包括城市北京，北京-北京
                getValueItems(arr) {
                    const newArr = []
                    const obj = {}
                    const { length } = arr
                    for (let i = 0; i < length; i += 1) {
                        const item = arr[i]
                        const { k } = item
                        if (!obj[k]) {
                        newArr.push(item)
                        obj[k] = true
                        }
                    }
                    this.valueItems = newArr
                },

                // 递归
                recursive(type) {
                    const { length: optionsLength } = this.options
                    if (!optionsLength) return

                    const valueItems = []
                    const { value, options } = this

                    this.clearParentMark()

                    let isFirst = false
                    options.map((data, index) => {
                        const arrayMap = (array, parent) => {
                            const { length } = array
                            for (let i = 0; i < length; i += 1) {
                                const item = array[i]
                                const { k, children } = item
                                if (value.includes(k)) {
                                    valueItems.push(item)
                                    if (!isFirst && type === 'init') {
                                        isFirst = true
                                        this.initOpen(index, parent.k)
                                    }
                                    this.getParentMark(item)
                                }
                                if (Array.isArray(children) && children.length) {
                                    arrayMap(children, item)
                                }
                            }
                        }
                        const { length: childrenLength = 0 } = data.children
                        if (childrenLength) {
                            arrayMap(data.children, data)
                        }
                    })

                    this.getValueItems(valueItems)
                    this.getValueText()
                },

                getLastChildren(){
                    const {
                        options,
                        options: {
                            length: optionsLength
                        }
                    } = this
                    if (!optionsLength) return

                    const lastChildren = []
                    const arrayMap = (array) => {
                        const { length } = array
                        for (let i = 0; i < length; i += 1) {
                        const item = array[i]
                        const { children } = item

                        if (Array.isArray(children) && children.length) {
                            arrayMap(children)
                        } else {
                            lastChildren.push(item)
                        }
                        }
                    }
                    arrayMap(options)
                    this.lastChildrenOptions = lastChildren
                },

                 // 点击一级
                handleClickFirst(index) {
                    this.selectFirstIndex = index
                },

                handleRemove(data) {
                    const { valueItems, value } = this
                    const { k } = data
                    this.valueItems = valueItems.filter((item) => item.k !== k)
                    this.value = value.filter((item) => item !== k)
                },

                handleChangeModelValue(value) {
                    const {multiple} = this
                        if(multiple) {
                            this.successCallback(value, this.valueItems)
                        }else{
                            const valueItems = this.lastChildrenOptions.filter(item => item.k === value)
                            this.successCallback(value, valueItems)
                        }
                    this.visible = false
                },

                // 处理筛选数据
                handleSearch(query) {
                    if (query) {
                        this.searchLoading = true
                        const { lastChildrenOptions } = this
                        setTimeout(() => {
                            const objMap = new Map()
                            this.searchOptions = lastChildrenOptions.filter((item) => {
                                const isIncludes = item.v.toLowerCase().includes(query.toLowerCase())
                                const { v } = item
                                const flag = isIncludes && !objMap.has(v)
                                if(flag) objMap.set(v, 1)
                                return flag
                            })
                            this.searchLoading = false
                        }, 200)
                    } else {
                        this.searchOptions = []
                    }
                },

                handleSearchChange(k) {
                    const { multiple } = this

                    this.searchOptions = []
                    this.searchValue = ''

                    if (!multiple) {
                        this.handleChangeModelValue(k)
                    } else {
                        const {
                        value,
                        value: { length }
                        } = this
                        const { multipleLimit } = this
                        if (length >= multipleLimit) {
                              this.$message.error(`最多选择${multipleLimit}项`)
                            return
                        }
                        const contain = value.includes(k)
                        if (!contain) {
                            this.value.push(k)
                        }
                    }
                },

                // 选中最后一级
                handleSelect (e) {
                    const {
                        target: { value, checked }
                    } = e
                    const { multiple, multipleLimit } = this

                    // 单选
                    if (!multiple) {
                        this.handleChangeModelValue(value)
                    } else {
                        const { length } = this.value
                        if (checked && multipleLimit <= length) {
                          this.$message.error(`最多选择${multipleLimit}项`)
                        this.value = this.value.filter((k) => k !== value)
                        }
                    }
                },

                submit(){
                    const { value } = this
                    this.handleChangeModelValue(value)
                },

                // 关闭弹窗，还原真实值
                handleClose() {
                    this.value = this.realValue
                },

                // 清空
                handleClear() {
                    const { multiple } = this.props
                    const val = multiple ? [] : ''
                    this.handleChangeModelValue(val)
                }
            }
        }
        const cascaderDialogJobType = Vue.createApp(cascaderDialogOptions).use(ElementPlus).mount('#cascaderDialogJobTypeTemplate')

        window.globalComponents = { ...window.globalComponents, cascaderDialogJobType }
    });
</script>