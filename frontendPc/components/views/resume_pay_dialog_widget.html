<link rel="stylesheet" href="/static/css/payAlert.css">

<div id="payDialogAlert" class="success-dialog-alert-template" v-cloak>
    <el-dialog v-model="wechatVisible" destroy-on-close @close="handelWxClose" :before-close="beforeClose" :close-on-click-modal="false" custom-class="pay-dialog">
        <div class="pay-container">
            <div class="tips">
                <strong>确认支付</strong>
            </div>
            <div class="pay-text" v-loading="orderLoading">
                <div class="title-vip">
                    <p>
                        {{packageConfig.name}}
                        <span v-if="isUpgrade" class="upgrades-tips">您可以优惠价升级为钻石VIP会员，享更多求职特权</span>
                    </p>
                    <div class="reminder">
                        购买即表示同意<span><a :href="packageConfig.serviceAgreementUrl" target="_blank">{{packageConfig.serviceAgreementTitle}}</a></span>
                    </div>
                </div>

                <div v-if="isUpgrade" class="upgrades-content">
                    当前黄金VIP套餐剩余<span class="color-primary">{{currentPackage.upgradeData?.goldRemainDays}}</span
                    >天，可抵¥{{createOrderCallback.convertPrice}}；最高抵扣金额不得高于所升级套餐金额。
                </div>

                <div class="price-content">
                    <div class="swiper packageSwiper">
                        <div class="swiper-wrapper">
                            <div
                                class="swiper-slide price"
                                :class="{active: selectPackageId === item.equityPackageId, 'col-4': isSpecial}"
                                v-for="(item, index) in orderList"
                                :key="item.equityPackageId"
                                @click="handleRecommendTabClick(item)"
                            >
                                <div class="prompt">
                                    <template v-if="isUpgrade">升级特惠</template>
                                    <template v-else>
                                        <!-- 黄金 -->
                                        <template v-if="type == 1">低至{{item.dailyAmount}}元/天</template>
                                        <!-- 钻石 -->
                                        <template v-if="type == 3">立省{{item.discountAmount}}元</template>
                                        <!-- 求职快 -->
                                        <template v-if="type == 4">低至{{item.dailyAmount}}元/天</template>
                                        <!-- 洞察 -->
                                        <template v-if="type == 2">单次低至￥{{item.timesAmount}}</template>
                                    </template>
                                </div>
                                <div class="price-title">
                                    <div class="price-title-first">{{item.name}}</div>
                                </div>
                                <div class="original-price"><span>¥</span>{{item.realAmount}}<span class="del-price">¥{{item.originalAmount}}</span></div>
                            </div>

                            <div
                                class="swiper-slide price append-slide"
                                :class="{active: selectPackageId == item.equityPackageId, 'col-4': isSpecial}"
                                v-for="(item, index) in appendList"
                                :key="item.equityPackageId"
                                @click="handleRecommendTabClick(item)"
                            >
                                <div class="prompt">享8+权益</div>
                                <div class="price-title">
                                    <div class="price-title-first">
                                        {{item.name}}

                                        <el-tooltip popper-class="append-popper" effect="light" placement="top">
                                            <span slot="default" class="icon"></span>
                                            <template #content>
                                                <div class="append-popper-content">
                                                    <div class="append-popper-list" v-for="pop in item.popTextList">{{pop}}</div>
                                                </div>
                                            </template>
                                        </el-tooltip>
                                    </div>
                                </div>
                                <div class="detail">
                                    <div class="real-price"><span>¥</span>{{item.realAmount}}</div>
                                    <span class="desc">{{item.desc}}</span>
                                </div>
                            </div>
                        </div>

                        <div v-if="isSpecial">
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="recommend">为你推荐</div> -->
            <div class="relogin-content">
                <div class="qr-code" v-loading="loading">
                    <img :src="urCodeUrl" alt="" />
                    <div class="coderefresh" v-show="isQRcodePast" @click="getQRCode">
                        <div class="mask-content">
                            <img class="img-refresh" src="/static/assets/login/new-refresh.png" alt="" />
                            点击刷新
                        </div>
                        <div class="mask"></div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="product-name">
                        产品名称：
                        <span>{{currentPackage['name']}}</span>
                    </div>
                    <div class="product-price">
                        订单金额：
                        <span>¥{{createOrderCallback.price}}</span>
                        <span v-if="isUpgrade" class="convert">已抵扣¥{{createOrderCallback.convertPrice}}</span>
                        <el-tooltip v-if="createOrderCallback.convertPrice != createOrderCallback.realOfferPrice" effect="dark" content="最高抵扣金额不得高于所购套餐金额" placement="top">
                            <span slot="default" class="what"></span>
                        </el-tooltip>
                    </div>
                    <div class="suppert">
                        <div class="select"></div>
                        <span>支持微信扫码支付</span>
                    </div>
                    <div class="finish" @click="initiativeCheckOrderPay">如果您已经完成操作，请点击<span>已完成付款</span></div>
                </div>
            </div>
        </div>
    </el-dialog>

    <el-dialog v-model="detainmentVisible" z-index="3000" :close-on-click-modal="false" :show-close="false" custom-class="detainment-dialog">
        <template #title>
            <div class="detainment-header">
                <div class="title" v-html="detainment.title"></div>
                <div class="sub-title" v-html="detainment.subTitle"></div>
            </div>
        </template>
        <div class="detainment-content">
            <template v-if="detainmentChart">
                <div class="chart-img">
                    <img :src="detainment.contentImage" alt="" />
                </div>
            </template>
            <template v-else>
                <div class="title">{{detainment.lineTxt}}</div>
                <div class="equity-content">
                    <div class="list" v-for="item in detainment.contentList">
                        <img :src="item.icon" alt="" />
                        <div class="info">
                            <div class="name">{{item.title}}</div>
                            <div class="desc">{{item.subTitle}}</div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <div class="detainment-footer">
            <button class="cancel" @click="detainmentVisible=false;wechatVisible=false">放弃优惠</button>
            <button class="confirm" @click="detainmentVisible=false">继续支付</button>
        </div>
    </el-dialog>

    <el-dialog v-model="paySuccessVisible" z-index="3500" :close-on-click-modal="false" custom-class="pay-success-dialog">
        <div class="pay-success-dialog">
            <div class="pay-success-title">{{noticeCard.title}}</div>
            <div class="pay-success-sub-title">{{noticeCard.subTitle}}</div>
            <div class="pay-success-wrapper-title">{{noticeCard.lineTxt}}</div>
            <div class="list-content">
                <a :href="item.url" target="_blank" class="package-list" v-for="item in noticeCard.contentList">
                    <div class="icon">
                        <img :src="item.icon" alt="" />
                    </div>
                    <div>
                        <div class="package-name">{{item.title}}</div>
                        <div class="package-desc">{{item.subTitle}}<span>{{item.linkLabel}}</span></div>
                    </div>
                </a>
            </div>
            <div class="footer-btn" @click="closePaySuccessDialog">我知道了（{{turnOffTime}}s）</div>
        </div>
    </el-dialog>
</div>

<script>
    $(function () {
        const payDialogAlertOptions = {
            data() {
                return {
                    // type：1黄金  2洞察  3 钻石 4求职快
                    type: 1,
                    uuid: 0,
                    packageConfig: {},
                    createOrderCallback: {},
                    isUpgrade: false,
                    currentPackageId: '',
                    selectPackageId: '',
                    currentPackage: {},
                    orderList: [],
                    appendList: [],
                    successVisible: false,
                    wechatVisible: false,
                    showWechatCode: true,
                    isQRcodePast: false,
                    orderLoading: false,
                    applySuccessMsg: '',
                    selectOrderId: 0,
                    timer: null,
                    urCodeUrl: '',
                    callback: () => {},

                    detainmentVisible: false,
                    isFirstClose: false,

                    paySuccessVisible: false,
                    turnOffTime: 5,
                    turnOffTimer: null,
                    noticeCard: {
                        contentList: []
                    },

                    packageSwiper: null
                }
            },

            computed: {
                isSpecial() {
                    const { type } = this
                    return type == 2
                },

                packageLength() {
                    const { length } = this.orderList
                    return length
                },

                detainment() {
                    const { detainment = { contentList: [] } } = this.currentPackage
                    return detainment
                },

                detainmentChart() {
                    const { type } = this
                    return type == 2 || type == 4
                }
            },

            methods: {
                handleRecommendTabClick(item) {
                    this.currentPackageId = item.equityPackageId
                    this.currentPackage = item
                    this.closeTimer()
                    this.createPreOrder()
                },
                showPayDialogAlert(successData, callback = () => {}) {
                    const { wxBindQrCodeImageUrl, applySuccessMsg } = successData
                    this.applySuccessMsg = applySuccessMsg
                    if (wxBindQrCodeImageUrl) {
                        this.urCodeUrl = wxBindQrCodeImageUrl
                        this.showWechatCode = true
                    }
                    this.wechatVisible = true
                    callback()
                },
                removeWechat() {
                    this.wechatVisible = false
                },
                handelWxClose() {
                    this.wechatVisible = false
                    this.closeTimer()
                },
                
                beforeClose(done = () => {}) {
                    const { isFirstClose } = this
                    if (isFirstClose) {
                        this.detainmentVisible = true
                        this.isFirstClose = false

                        this.closeDetainment()
                        return
                    }
                    this.packageSwiper && this.packageSwiper.destroy(true, true)
                    done()
                },

                getQRCode() {
                    const { currentPackageId } = this
                    this.handleRecommendTabClick(currentPackageId)
                    this.isQRcodePast = false
                },
                async createPreOrder() {
                    const { currentPackageId } = this
                    // this.orderLoading = true
                    const rs = await httpPost('/api/person/resume-order/pay', {
                        equityPackageId: currentPackageId,
                        uuid: this.uuid
                    })
                    this.wechatVisible = true
                    if (rs) {
                        this.showWechatCode = true
                        this.urCodeUrl = rs.codeUrl
                        this.selectOrderId = rs.orderId
                        this.createOrderCallback = rs
                    }
                    // this.orderLoading = false
                    this.selectPackageId = currentPackageId
                    this.checkOrderPay()
                },
                checkOrderPay() {
                    // 检查订单是否已经被支付了,支付就跳转到成功页面,这里要设置一个定时器,5秒检查一次
                    this.closeTimer()
                    this.timer = setInterval(async () => {
                        const rs = await httpGet('/api/person/resume-order/query', {
                            orderId: this.selectOrderId
                        })
                        if (rs.errorStatus === -1) {
                            window.location.reload()
                        }
                        switch (rs.status) {
                            case 0:
                                // 等待支付
                                break
                            case -1:
                                // 已取消
                                this.closeTimer()
                                // 二维码变灰
                                this.isQRcodePast = true
                                break
                            case 1:
                                // 支付成功
                                this.closeTimer()
                                // 关闭购买弹窗；显示下单成功弹窗；
                                this.paySuccess(rs.jobResourcesInfo, rs.tips, rs.noticeCard)
                                return
                                break
                            default:
                                // 异常
                                this.closeTimer()
                                this.isQRcodePast = true
                                break
                        }
                    }, 5000)
                },
                async initiativeCheckOrderPay() {
                    // 主动检查是否支付成功
                    const rs = await httpGet('/api/person/resume-order/query', {
                        orderId: this.selectOrderId
                    })
                    if (rs.errorStatus === -1) {
                        window.location.reload()
                    }
                    switch (rs.status) {
                        case 0:
                            // 出toast
                            this.$message({
                                message: rs.tips,
                                type: 'warning'
                            })
                            break
                        case -1:
                            // 已取消
                            this.closeTimer()
                            // 二维码变灰
                            this.isQRcodePast = true
                            break
                        case 1:
                            // 支付成功
                            this.closeTimer()
                            // 关闭购买弹窗；显示下单成功弹窗；
                            this.paySuccess(rs.jobResourcesInfo, rs.tips, rs.noticeCard)
                            break
                        default:
                            // 异常
                            this.closeTimer()
                            this.isQRcodePast = true
                            break
                    }
                },
                paySuccess(jobResourcesInfo = '', tips, noticeCard) {
                    if (jobResourcesInfo != '') {
                        this.wechatVisible = false
                        window.globalComponents.paymentSuccessDialogAlertComponent.paymentSuccessDialogAlert(jobResourcesInfo)
                        return false
                    }
                    this.wechatVisible = false
                    this.detainmentVisible = false
                    this.showPaySuccessDialog(noticeCard)
                    this.callback()
                },
                closeTimer() {
                    if (this.timer) {
                        clearInterval(this.timer)
                        this.timer = null
                    }
                },
                async show(api, type, index, uuid, position) {
                    this.type = type
                    this.uuid = uuid
                    // 请求数据
                    const resp = await httpGet(api + '?equityPackageCategoryId=' + type + '&uuid='+uuid + '&index='+index+ '&position='+position)
                    const id = resp.list[index].equityPackageId

                    this.orderList = resp.list
                    this.isUpgrade = resp.isUpgrade
                    this.packageConfig = resp.config
                    this.currentPackage = resp.list[index]
                    this.isFirstClose = resp.isFirstClose
                    this.currentPackageId = id

                    await this.createPreOrder()

                    await this.fetchAppendPackage()
                },

                setSwiper() {
                    this.packageSwiper = new Swiper('.packageSwiper', {
                        slidesPerView: 'auto',
                        navigation: {
                            nextEl: '.swiper-button-next',
                            prevEl: '.swiper-button-prev'
                        }
                    })
                },

                async fetchAppendPackage() {
                    const { type, isSpecial } = this
                    if (!isSpecial) return
                    const resp = await httpGet('/api/person/resume-equity-package/get-more-package', { equityPackageCategoryId: type })
                    this.appendList = [resp]

                    setTimeout(() => {
                        this.setSwiper()
                    }, 0)
                },

                closeDetainment() {
                    const { type } = this
                    httpPost('/api/person/resume-equity-package/close-package', { equityPackageCategoryId: type })
                },

                showPaySuccessDialog(noticeCard) {
                    this.noticeCard = noticeCard
                    this.paySuccessVisible = true
                    this.countDown()
                },

                countDown() {
                    this.turnOffTime = 5
                    clearInterval(this.turnOffTimer)
                    this.turnOffTimer = setInterval(() => {
                        const { turnOffTime } = this
                        this.turnOffTime = turnOffTime - 1
                        if (turnOffTime === 1) {
                            this.clearTurnOffTimer()
                        }
                    }, 1000)
                },

                closePaySuccessDialog() {
                    this.clearTurnOffTimer()
                },

                clearTurnOffTimer() {
                    this.paySuccessVisible = false
                    clearInterval(this.turnOffTimer)
                }
            },
            beforeDestroy() {
                this.closeTimer()
            }
        }

        const PayDialogAlertComponent = Vue.createApp(payDialogAlertOptions).use(ElementPlus).mount('#payDialogAlert')

        window.globalComponents = {
            ...window.globalComponents,
            PayDialogAlertComponent
        }
    })

</script>