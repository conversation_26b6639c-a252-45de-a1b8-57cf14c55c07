
<link rel="stylesheet" href="/static/css/subscribeJobDialog.css">

<div id="subscribeDialogTemplate" class="subscribe-dialog-template" v-cloak>
    <el-dialog v-model="dialogVisible" :center="true" @close="closeDialog">
        <div class="title">职位订阅</div>
        <div class="tips">创建您的专属订阅，我们将定时向您推送最新、最精准的职位信息，让您不再错过好工作！</div>

        <el-form :label-width="80" :model="formData" :rules="rules" ref="formRef">
            <el-form-item label="意向职位" prop="jobCategoryIds">
                <el-input readonly v-model="jobTypeText" class="center" placeholder="请选择意向职位"
                    @click="handleDialogActive('jobTypeList')">
                    <template #suffix>
                        <i class="el-select__caret el-input__icon el-icon-arrow-down"> </i>
                    </template>
                </el-input>
            </el-form-item>

            <el-form-item label="意向城市" prop="areaIds">
                <el-input readonly v-model="areaText" class="center" placeholder="请选择意向城市"
                    @click="handleDialogActive('areaList')">
                    <template #suffix>
                        <i class="el-select__caret el-input__icon el-icon-arrow-down"> </i>
                    </template>
                </el-input>
            </el-form-item>

            <el-form-item label="学历要求" prop="educationIds" class="education">
                <el-select v-model="formData.educationIds" multiple collapse-tags placeholder="学历要求" clearable>
                    <el-option v-for="{ k, v } in educationList" :label="v" :value="k" />
                </el-select>
            </el-form-item>

            <el-form-item label="推送渠道" prop="isSendWechat">
                <div class="email">
                    <el-checkbox v-model="formData.isSendEmail" true-label="1" false-label="2">邮箱</el-checkbox>
                    <el-input v-model="formData.sendEmail"></el-input>
                </div>

                <el-checkbox v-model="formData.isSendWechat" true-label="1" false-label="2">微信服务号
                    <span v-if="isBindWechat">(已绑定)</span>
                </el-checkbox>
                <div class="wechat-tips" v-if="!isFollowed">
                    请先关注服务号完成绑定哦~
                    <span class="follow" @click="bindWechat">去关注 →</span>
                </div>
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button type="primary" @click="handleSubscribe"> 订阅</el-button>
        </template>
    </el-dialog>

    <select-dialog ref="selectDialogRef" v-model="calcDialogValue" :title="calcDialogTitle"
        :search-placeholder="calcDialogPlaceholder" :list="calcDialogList" :name="calcDialogName" multiple
        :multiple-limit="5" @update="handleDialogChange"></select-dialog>

    <el-dialog custom-class="wechat-dialog" width="40%" v-model="bindWechatVisible" @close="handelWxClose">
        <div class="wechat-title">绑定微信</div>
        <div class="wechat-container">
            <div class="qr-code" v-loading="loading">
                <img :src="urCodeUrl" />
                <img class="logo" src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_square.png" alt="" />
            </div>
            <div class="wechat-footer">
                <div class="tips">
                    <span>请使用微信【扫一扫】</span>
                    <span>关注<strong>“高才-高校人才网服务号”</strong>完成绑定</span>
                    <span class="online-tips">实时接收求职反馈</span>
                </div>
            </div>
        </div>
    </el-dialog>

    <el-dialog custom-class="relogin-dialog" v-model="takeUpWechatVisible">
        <div class="relogin-title">提示</div>
        <div class="relogin-content">
            <p>{{ wechatRepeat }}</p>
        </div>
        <div class="footer">
            <el-button @click="toLogin"> 去登录 </el-button>
            <el-button type="primary" @click="changeCode"> 更换微信 </el-button>
        </div>
    </el-dialog>
</div>

<script>
    $(function () {
        const subscribeDialogOptions = {
            data() {
                const emailValidator = (val, rules, callback) => {
                     const {
                        validEmail,
                        formData: { jobCategoryIds, areaIds, educationIds, isSendEmail, sendEmail, isSendWechat }
                    } = this
                    const isCheckEmail = isSendEmail === '1'
                    const isValue = jobCategoryIds && areaIds && educationIds.length
                    const isEmail = isSendEmail && !validEmail(sendEmail)
                    const emailEmpty = isSendEmail && !sendEmail

                    if (!isValue) {
                        callback()
                        return
                    }

                    if (!isCheckEmail && isSendWechat !== '1') {
                        callback('请至少选择一种推送渠道')
                        return
                    }

                    if (emailEmpty) {
                        callback('请输入邮箱地址')
                        return
                    }

                    if (isEmail) {
                        callback('请输入正确的邮箱地址')
                        return
                    }

                    callback()
                }

                return {
                    educationList: [],
                    jobTypeText: [],
                    areaText: [],
                    jobTypeList: [],
                    areaList: [],
                    dialogVisible: false,
                    bindWechatVisible: false,
                    takeUpWechatVisible: false,
                    loading: false,
                    isBind: 0,
                    isSubscribe: 0,
                    formData: {
                        jobCategoryIds: [],
                        areaIds: [],
                        educationIds: [],
                        sendEmail: '',
                        isSendEmail: '2',
                        isSendWechat: '2'
                    },
                    rules: {
                        jobCategoryIds: [{ required: true, message: '请选择意向职位', trigger: 'change' }],
                        areaIds: [{ required: true, message: '请选择意向城市', trigger: 'change' }],
                        educationIds: [{ required: true, message: '请选择学历要求', trigger: 'change' }],
                        isSendWechat: [{ required: true }, { validator: emailValidator, trigger: 'blur' }]
                    },
                    dialogActive: '',
                    wechatRepeat: '',
                    urCodeUrl: '',
                    ticket: '',
                    token: '',
                    timer: null,
                    successCallback: () => { }
                }
            },

            computed: {
                isBindWechat() {
                    return this.isBind === 1
                },

                isFollowed() {
                    return this.isSubscribe === 1
                },

                calcDialogTitle() {
                    const { dialogActive } = this
                    const options = {
                        jobTypeList: '请选择职位类型',
                        areaList: '请选择地点'
                    }

                    return options[dialogActive] || '请选择'
                },

                calcDialogPlaceholder() {
                    const { dialogActive } = this
                    const options = {
                        jobTypeList: '请输入职位类型关键词',
                        areaList: '搜索城市'
                    }

                    return options[dialogActive] || '请输入关键词'
                },

                calcDialogValueTextKey() {
                    const { dialogActive } = this
                    const options = {
                        jobTypeList: ['jobCategoryIds', 'jobTypeText'],
                        areaList: ['areaIds', 'areaText']
                    }
                    const value = options[dialogActive]

                    if (value) {
                        return value
                    }
                    return ['', '']
                },

                calcDialogValue: {
                    get() {
                        const [valKey, textKey] = this.calcDialogValueTextKey

                        if (valKey) {
                            return this.formData[valKey]
                        }
                        return []
                    },
                    set(val) {
                        const [valKey, textKey] = this.calcDialogValueTextKey

                        if (valKey) {
                            this.formData[valKey] = val
                        }
                    }
                },

                calcDialogList() {
                    const { dialogActive } = this
                    return this[dialogActive] || []
                }
            },

            methods: {
                validEmail(email) {
                    const reg = /^[\w\-\\.]+@[\w\-\\.]+(\.\w+)+$/
                    return reg.test(email)
                },

                closeDialog() {
                    this.jobTypeText = []
                    this.areaText = []
                    this.formData={
                        jobCategoryIds: [''],
                        areaIds: [''],
                        educationIds: [],
                        sendEmail: '',
                        isSendEmail: '2',
                        isSendWechat: '2'
                    }
                    this.$refs.formRef.clearValidate()
                    this.dialogVisible = false
                },

                handleSubscribe() {

                    this.$refs.formRef.validate((valid) => {
                        if (valid) {
                            const postData = {
                                ...this.formData,
                                jobCategoryIds: this.formData.jobCategoryIds?.join(',').replace(/^,+|,+$/g, ''),
                                areaIds: this.formData.areaIds?.join(',').replace(/^,+|,+$/g, ''),
                                educationIds: this.formData.educationIds?.join(',')
                            }

                            httpPost('/api/person/job-subscribe/save', postData)
                                .then((res) => {
                                    const { title, content } = res
                                    this.closeDialog()
                                    ElementPlus.ElMessageBox.alert(content, title, {
                                        confirmButtonText: '确定',
                                        dangerouslyUseHTMLString: true
                                    })
                                    this.successCallback()
                                })
                                .catch(() => { })
                        }
                    })
                },

                handleDialogActive(name) {
                    this.dialogActive = name
                    this.$refs.selectDialogRef.handleOpen()
                },

                handleDialogChange(data) {
                    const [valKey, textKey] = this.calcDialogValueTextKey
                    const { label, value } = data
                    this.formData[valKey] = value
                    this[textKey] = label
                },

                resetCountDown() {
                    clearTimeout(this.timer)
                },

                handelWxClose() {
                    this.resetCountDown()
                    this.bindWechatVisible = false
                },

                urCodeOverdue() {
                    const postData = {
                        ticket: this.ticket,
                        token: this.token
                    }
                    this.timer = setTimeout(async () => {
                        try {
                            const { status, sno, tips, token } = await httpPost('/api/member/check-bind-qrcode', postData)
                            // 9 二维码过期
                            if (status === 9) {
                                this.resetCountDown()
                                this.bindWechat()
                            }
                            // 1 未扫码，继续轮询
                            if (status === 1) {
                                this.urCodeOverdue()
                            }

                            // 3的时候是扫码成功但是没有绑定
                            if (!this.isBind && status === 3) {
                                this.resetCountDown()
                                window.location.href = `/wx/register-bind?token=${sno}`
                                this.isBind = 1
                            }

                            // 2的时候是扫码成功
                            if (status === 2) {
                                this.resetCountDown()
                                ElementPlus.ElMessage.success('绑定成功')
                                this.bindWechatVisible = false
                                this.isBind = 1
                                this.isSubscribe = 1
                            }

                            if (this.isBind && status === 3) {
                                this.resetCountDown()
                                this.takeUpWechatVisible = true
                                this.wechatRepeat = tips
                            }
                        } catch (error) {
                            ElementPlus.ElMessage.error(error)
                        }
                    }, 2000)
                },

                bindWechat() {
                    this.bindWechatVisible = true
                    this.loading = true
                    httpGet('/api/member/create-bind-qrcode').then((res) => {
                        const { url, ticket, token } = res
                        this.urCodeUrl = url
                        this.ticket = ticket
                        this.token = token
                        this.loading = false
                        this.urCodeOverdue()
                    })
                },

                toLogin() {
                    window.localStorage.clear()
                    window.sessionStorage.clear()
                    removeToken()
                    window.location.href = '/member/person/login'
                },

                changeCode() {
                    this.takeUpWechatVisible = false
                    this.urCodeOverdue()
                },

                getSubscribeInfo() {
                    httpGet('/api/person/job-subscribe/get-info').then((res) => {
                        const { sendEmail, educationIds, isBind, isSubscribe } = res

                        this.isBind = isBind
                        this.isSubscribe = isSubscribe
                        this.formData.sendEmail = sendEmail
                        this.formData.educationIds = educationIds?.split(',')
                    })
                },

                getParams(callback) {
                    httpGet('/api/config/get-all-category-job-list').then((res) => {
                        this.jobTypeList = res
                    })

                    httpGet('/api/config/get-hierarchy-city-list').then((res) => {
                        this.areaList = res
                    })

                    httpGet('/api/config/get-education-list').then((res) => {
                        this.educationList = res
                    })

                    this.getSubscribeInfo()
                },

                showSelectDialog(callback) {
                    if (callback) {
                        this.successCallback = callback
                    }
                    this.getParams()
                    this.dialogVisible = true
                }
            }
        }

        const subscribeDialog = Vue.createApp(subscribeDialogOptions).component('select-dialog', selectDialogComponent).use(ElementPlus).mount('#subscribeDialogTemplate')

        window.globalComponents = { ...window.globalComponents, subscribeDialog }
    })
</script>

