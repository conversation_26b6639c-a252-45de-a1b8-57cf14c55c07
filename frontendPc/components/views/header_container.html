<div class="header-container">
    <nav class="header-nav">
        <a href="/" class="header-logo">
            <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
        </a>

        <a href="/" class="nav-link">首页</a>

        <div class="header-notice-container">
            <span class="nav-link">公告&amp;简章</span>

            <div class="notice-open-part is-open">
                <div class="notice-content">
                    <div class="nav-list">
                        <div class="nav-title">
                            <p>栏目导航</p>
                        </div>
                        <ul class="is-nav-container nav-container">
                            <?php foreach ($hotColumn as  $item ): ?>
                            <li>
                                <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
                            </li>
                            <?php endforeach ?>
                        </ul>
                    </div>
                    <div class="nav-list">
                        <div class="nav-title">
                            <p>省区导航</p>
                        </div>
                        <ul class="nav-container">
                            <?php foreach ($provinceColumn as  $item ): ?>
                            <li>
                                <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
                            </li>
                            <?php endforeach ?>
                            <a class="more" href="/region.html" target="_blank">更多</a>
                        </ul>
                    </div>
                    <div class="nav-list">
                        <div class="nav-title">
                            <p>城市导航</p>
                        </div>
                        <div class="nav-container">
                            <ul class="nav-container">
                                <?php foreach ($cityColumn as  $item ): ?>
                                <li>
                                    <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
                                </li>
                                <?php endforeach ?>
                                <a href="/region.html" target="_blank" class="more">更多</a>
                            </ul>
                        </div>
                    </div>
                    <div class="nav-list">
                        <div class="nav-title">
                            <p>学科导航</p>
                        </div>
                        <ul class="is-nav-container nav-container">
                            <?php foreach ($majorColumn as  $item ): ?>
                            <li>
                                <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
                            </li>
                            <?php endforeach ?>
                            <a href="/major.html" target="_blank" class="more">更多</a>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php if ($isJob): ?>
        <a href="/job" class="nav-link active">找职位</a>
        <?php else: ?>
        <a href="/job" class="nav-link">找职位</a>
        <?php endif; ?>

        <?php if ($isCompany): ?>
        <a href="/company" class="nav-link active">找单位</a>
        <?php else: ?>
        <a href="/company" class="nav-link">找单位</a>
        <?php endif; ?>

<!--        <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>-->
        <div class="vip">
            <a href="/vip.html" target="_blank" class="nav-link">求职VIP<span class="arrow"></span>
                <span class="artifact">神器</span>
            </a>
            <div class="more">
                <a href="/job-fast.html" target="_blank">简历曝光</a>
                <a href="/vip.html" target="_blank">上岸直通车</a>
                <a href="/competitive-power.html" target="_blank">竞争力分析</a>
<!--                <a href="http://t.jugaocai.com/SVWW" target="_blank">精选课程</a>-->
                <a href="/zhaopin/zhuanti/ycc_gaocaiyouke_v2/index.html" target="_blank">高校求职顾问</a>
                <a href="https://www.gaoxiaojob.com/zhaopin/zt/ycc_jianlifudao2025/index.html" target="_blank">简历诊断优化</a>
            </div>
        </div>

        <a class="nav-link join">小程序
            <div class="join-code">
                <div class="news-part">
                    <p class="scan-join">高才优聘小程序上线啦！</p>
                    <p class="explain">随时随地看机会</p>
                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/index.jpg" />
                </div>
            </div>
        </a>
    </nav>

    <div id="headerTemplate" class="header-main" v-cloak>
        <div class="header-search">
            <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                <template #prefix>
                    <el-select class="search-type" v-model="type">
                        <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value">
                        </el-option>
                    </el-select>
                </template>

                <template #suffix>
                    <i class="el-icon-search pointer" @click="handleSearch"></i>
                </template>
            </el-input>
        </div>

        <?php if(Yii::$app->params['user']['type']==1):?>
        <!-- 已登录 start -->
        <el-popover popper-class="header-popover-message" trigger="hover" :width="180">
            <template #reference>
                <span class="message">
                    <el-badge :value="total" :hidden="total === 0">
                        <i class="el-icon-bell"></i>
                    </el-badge>
                </span>
            </template>

            <ul class="message-options">
                <li v-for="(item, index) in messageOptions" :key="index" class="item pointer" @click="handleClick(item)">
                    <span class="label">{{ item.label }}</span>

                    <span v-if="item.value" class="value"> <span class="num"> {{ item.value }} </span>条未读 </span>
                </li>
            </ul>
        </el-popover>

        <el-dropdown popper-class="header-dropdown-popper">
            <div class="header-dropdown">
                <el-avatar :size="28" :src="avatar"></el-avatar>
                <div class="vip-logo" v-if="isVip"></div>
                <span>{{ username }}</span>
                <i class="el-icon-arrow-down el-icon--right"></i>
            </div>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item @click="() => (!Boolean(<?= $vipCardInfo['isServiceUrl']?>) ? openVip('/vip.html') : handleRoute('/job?tab=service'))">
                        <div class="dropdown-item__card">
                            <div class="dropdown-item__card-title">
                                <span><?=$vipCardInfo['type']?></span>
                                <span><?=$vipCardInfo['btnText']?></span>
                            </div>

                            <div class="dropdown-item__card-desc"><?=$vipCardInfo['describe']?></div>

                            <div class="dropdown-item__card-tips"><?=$vipCardInfo['content']?></div>
                        </div>
                    </el-dropdown-item>

                    <el-dropdown-item @click="() => handleRoute('/home')">
                        <div class="dropdown-item-cell">
                            <span class="name person">个人中心<i class="icon"></i></span>
                            <span class="tips">智能匹配职位、求职管理</span>
                        </div>
                    </el-dropdown-item>

                    <el-dropdown-item @click="() => handleRoute('/resume')">
                        <div class="dropdown-item-cell">
                            <span class="name">
                                我的简历
                                <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                            </span>
                            <span class="tips">完整度达75%可投全站职位</span>
                        </div>
                    </el-dropdown-item>

                    <el-dropdown-item @click="() => handleRoute('/delivery')">
                        <div class="dropdown-item-cell">
                            <span class="name">投递反馈</span>
                        </div>
                    </el-dropdown-item>

                    <el-dropdown-item @click="() => handleRoute('/view')">
                        <div class="dropdown-item-cell">
                            <span class="name">谁看过我</span>
                        </div>
                    </el-dropdown-item>

                    <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                        <div class="dropdown-item-cell">
                            <span class="name">
                                求职工具
                                <span class="complete"> NEW </span>
                            </span>
                            <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                        </div>
                    </el-dropdown-item>

                    <el-dropdown-item>
                        <div class="dropdown-item-cell">
                            <div class="nav-free-vip-enter" @click="fetchShareInfo">
                                <div class="nav-free-vip-header">
                                    <div class="title">免费薅羊毛</div>
                                    <div class="regulate" @click.stop="showRegulate">活动规则</div>
                                </div>
                                <div class="nav-free-vip-footer">点击<span class="invite">立即邀请>></span>,扫码获取专属分享海报</div>
                            </div>
                        </div>
                    </el-dropdown-item>

                    <el-dropdown-item @click="() => handleRoute('/setting')">
                        <div class="dropdown-item-cell">
                            <span class="name">账号设置</span>
                            <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                        </div>
                    </el-dropdown-item>

                    <el-dropdown-item @click="handleLogout">
                        <div class="dropdown-item-cell is-logout">
                            <span class="name">退出登录</span>
                        </div>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>

        <el-dialog custom-class="free-vip-share-dialog" append-to-body v-model="shareDialogVisible">
            <div class="main-container">
                <div class="title">扫码获取专属分享海报</div>
                <img class="qr-code" :src="qrCodeUrl" alt="" />
                <div class="step">
                    <div class="item scan">扫描二维码</div>
                    <div class="item save">保存海报</div>
                    <div class="item send">发送给好友</div>
                </div>
            </div>
        </el-dialog>

        <el-dialog custom-class="free-vip-regulate-dialog" append-to-body v-model="regulateDialogVisible">
            <div class="main-container">
                <div class="title">规则说明</div>

                <div class="wrapper">
                    <div class="name">活动内容</div>
                    <div class="content">向好友发送分享海报/分享链接，邀请好友注册并完善简历，双方均可获得黄金VIP会员 * 3天。</div>
                </div>

                <div class="wrapper">
                    <div class="name">邀请流程</div>
                    <div class="content">
                        1、点击“<span class="special">立即邀请>></span>”，扫描二维码获得专属邀请海报，分享给硕博好友。 <br />
                        2、好友在分享页面正确填写手机号完成注册，并于注册后<span class="special">7日内</span>登录该账号，完善在线简历至65%或以上，即视为邀请成功。<br />
                        *
                        若好友注册后，未于7日内将简历完整度完善至65%或以上，则视为邀请不成功。用户可在手机移动端网页&高才优聘小程序：【我的】-【免费领VIP会员】-【我的成就】模块查看好友邀请进度。<br />
                        3、每成功邀请一位好友，邀请方及被邀请方均可获得高校人才网黄金VIP会员*3天权益。<br />
                        4、邀请成功，奖励会于当天24:00前发放。奖励发放情况可在手机移动端网页&高才优聘小程序：【我的】-【免费领VIP会员】-【我的成就】模块查看。<br />
                        5、若发放奖励时，用户有生效中的钻石VIP套餐，奖励将发放失败，请务必知悉。<br />
                        6、同一用户最多可获取15次奖励，累计45天。超过上限后，邀请成功不再发放相应奖励。<br />
                        <span class="bold">7、本活动最终解释权归高校人才网所有。</span>
                    </div>
                </div>
            </div>
        </el-dialog>
        <!-- 已登录 end -->
        <?php else: ?>
        <!-- 未登录 start -->
        <div class="login-register-container">
            <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
            <span class="line">|</span>
            <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
        </div>
        <!-- 未登录 end -->
        <?php endif;?>
    </div>
    <script>
        $(function () {
            const headerOptions = {
                data() {
                    return {
                        basePath: '/member/person',
                        avatar: "<?=Yii::$app->params['user']['avatar']?>",
                        username: "<?= Yii::$app->params['user']['showName']; ?>",
                        resumeComplete: "<?=Yii::$app->params['user']['resumePercent']?>",
                        type: "<?= common\helpers\UrlHelper::getPathBelongType(Yii::$app->request->pathInfo)?>",
                        isVip: "<?= \frontendPc\models\Resume::checkVip(Yii::$app->user->id)?>",
                        vipInfo: <?= json_encode(\frontendPc\models\Resume::getVipInfo(Yii::$app->user->id))?>,

                        typeOptions: [
                            { label: '职位', value: '1', path: '/job' },
                            { label: '公告', value: '2', path: '/search' },
                            { label: '单位', value: '3', path: '/company' },
                            { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                        ],

                        keyword: "<?= addslashes(Yii::$app->request->get('keyword')) ?>",

                        total: "<?=$totalMessageAmount?:''?>",
                        messageOptions: [
                            {
                                label: '我的直聊',
                                value: "<?=$chatAmount?:''?>",
                                path: '/chat'
                            },
                            {
                                label: '求职动态',
                                value: "<?=$jobTrendAmount?:''?>",
                                path: '/message?read=2&type=0'
                            },
                            {
                                label: '系统通知',
                                value: "<?=$systemAmount?:''?>",
                                path: '/message?read=2&type=3'
                            }
                        ],

                        shareDialogVisible: false,
                        regulateDialogVisible: false,
                        qrCodeUrl: ''
                    }
                },

                methods: {
                    handleSearch() {
                        const { type, typeOptions, keyword } = this
                        const { path, query } = typeOptions.find((item) => item.value === type) || {
                            path: 'search'
                        }
                        window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                    },

                    openVip(path) {
                        window.location.href = path
                    },

                    handleRoute(path) {
                        window.location.href = '/member/person' + path
                    },

                    handleClick(data) {
                        this.handleRoute(data.path)
                    },

                    async handleLogout() {
                        await httpGet('/api/member/logout')
                        window.localStorage.clear()
                        window.sessionStorage.clear()
                        removeToken()
                        window.location.reload()
                    },

                    showRegulate() {
                        this.regulateDialogVisible = true
                    },

                    async fetchShareInfo() {
                        const { codeUrl } = await httpGet('/api/person/new-resume/create')

                        this.qrCodeUrl = codeUrl
                        this.shareDialogVisible = true
                    }
                }
            }
            Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
        })
    </script>
</div>