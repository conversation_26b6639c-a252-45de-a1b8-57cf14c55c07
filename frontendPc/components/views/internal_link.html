<div class="footer-link-for-spider <?php if($isColumn):?>is-column<?php endif?>">
  <div class="tab-nav">
    <span class="is-active">热门地区</span>
    <span>热门学科</span>
    <span>热门单位</span>
    <span>热门岗位</span>
    <span>附近职位</span>
  </div>

  <div class="tab-pane">
    <div class="pane is-active">
      <?php foreach($data['hotRegionList'] as $item):?>
      <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
      <?php endforeach;?>

    </div>

    <div class="pane">
      <?php foreach($data['hotMajorList'] as $item):?>
      <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
      <?php endforeach;?>
    </div>

    <div class="pane">
      <?php foreach($data['hotCompanyList'] as $item):?>
      <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
      <?php endforeach;?>

    </div>
    <div class="pane">
      <?php foreach($data['hotPostList'] as $item):?>
      <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
      <?php endforeach;?>

    </div>
    <div class="pane">
      <?php foreach($data['hotJobList'] as $item):?>
      <a href="<?=$item['url']?>" target="_blank"><?=$item['name']?></a>
      <?php endforeach;?>

    </div>
  </div>
</div>

<script>
  $(function () {
    $linkForSpider = $('.footer-link-for-spider')
    $nav = $linkForSpider.find('.tab-nav span')
    $pane = $linkForSpider.find('.tab-pane .pane')
    activeClassName = 'is-active'

    $nav.on('click', function () {
      $(this).addClass(activeClassName).siblings().removeClass(activeClassName)
      $pane.eq($(this).index()).addClass(activeClassName).siblings().removeClass(activeClassName)
    })
  })
</script>