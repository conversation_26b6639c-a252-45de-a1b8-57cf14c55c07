<link rel="stylesheet" href="/static/css/resumePerfectPanel.css">

<div id="resumePerfectPanelTemplate" class="resume-perfect-panel-template">
    <div class="resume-perfect-panel-container" v-if="visible">
        <div class="resume-perfect-tips">
            您当前简历完善度不足<spen class="percentage">
                <?=$completeResumePercent?>%
            </spen>，请完善简历，提升您的求职成功率！
        </div>

        <el-button class="resume-perfect-btn" @click="handlePerfect">
            去完善
        </el-button>

        <el-button class="close-button" @click="visible = false"><i class="el-icon el-icon-close"></i>
        </el-button>
    </div>
</div>

<script>
    $(function () {
        const resumePerfectPanelOptions = {
            data() {
                return {
                    visible: true,
                }
            },

            methods: {
                handlePerfect() {
                    // location.href = '/member/person/resume'
                    window.open('/member/person/resume', '_blank');
                }
            },

            mounted() {
            }
        }
        Vue.createApp(resumePerfectPanelOptions).use(ElementPlus).mount('#resumePerfectPanelTemplate')
    });
</script>