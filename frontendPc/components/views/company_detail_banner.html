<div class="detail-header"  <?php if($info['headBannerUrl']):?>style="background-image: url(<?=$info['headBannerUrl']?>);"<?php endif?>>
    <div class="modal">
        <div class="main">
            <div class="info">
                <div class="left">
                    <div class="logo">
                        <img src="<?=$info['logo']?>"
                             alt="">
                    </div>
                    <?php if($info['isCollect'] == 1):?>
                    <button class="el-button el-button--default company-collect-button">
                        <span>已关注</span>
                    </button>
                    <?php else:?>
                    <button class="el-button el-button--primary company-collect-button">
                        <span>立即关注</span>
                    </button>
                    <?php endif;?>
                </div>
                <div class="right">
                    <h1 title="<?=\common\helpers\StringHelper::changeQuotationMark($info['companyName'])?>"> <?= $info['companyName']?> </h1>
                    <h2 title="<?=\common\helpers\StringHelper::changeQuotationMark($info['englishName'])?>"> <?= $info['englishName']?>
                    <?php if(count($info['fuseArr']) > 0):?>
                        <div class="tags" id="tagsTemplate">
                            <?php foreach($info['fuseArr'] as $k=>$label):?>
                            <?php if($k < $welfareLabelViewAmount):?>
                            <span><?= $label?></span>
                            <?php endif?>
                            <?php endforeach;?>

                            <?php if(count($info['fuseArr']) > $welfareLabelViewAmount):?>
                                <el-popover placement="bottom" :width="430" trigger="hover" v-cloak>
                            <template #reference>
                                                    <span>
                                                        <i class="el-icon boon-more" style="--font-size: 12px">
                                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                                <path
                                                                        fill="currentColor"
                                                                        d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"
                                                                ></path>
                                                            </svg>
                                                        </i>
                                                    </span>
                            </template>
                                    <?php foreach($info['fuseArr'] as $k=> $item):?>
                                    <?php if($k >= $welfareLabelViewAmount):?>
                                    <span class="boon"><?=$item?></span>
                                    <?php endif;?>
                                    <?php endforeach?>
                        </el-popover>
                            <?php endif?>
                        </div>
                    <?php endif?>
                </div>
            </div>
        </div>

        <div class="bottom">
            <div class="tabs">
                <a class="tab-pane-common introduce <?php if($detailActive):?>active<?php endif?>" href="<?=$detailUrl?>">单位介绍</a>
                <a class="tab-pane-common announcement <?php if($announcementActive):?>active<?php endif?>" href="<?=$detailAnnouncementUrl?>">招聘公告（<?= $info['onlineAnnouncementCount']?>）</a>
                <a class="tab-pane-common job <?php if($jobActive):?>active<?php endif?>" href="<?=$detailJobUrl?>">招聘职位（<?= $info['onlineJobCount']?>）</a>
                <?php if($info['activityCount']>0):?>
                <a class="tab-pane-common activity <?php if($activityActive):?>active<?php endif?>" href="<?=$detailActivityUrl?>">引才活动（<?=$info['activityCount'] ?>）</a>
                <?php endif?>
            </div>
        </div>
    </div>
</div>