<link rel="stylesheet" href="/static/css/checkGenerateChatDialog.css">

<div id="checkGenerateChatDialog" class="chat-dialog-alert-template" v-cloak>
    <el-dialog v-model="dialogVisible" :close-on-click-modal="false">
        <div class="wechat-container">
            <div class="checktips">{{ title }}</div>
            <div class="tips-text">
                <div class="text-right">
                    <div class="tips-1" v-html="tips1"></div>
                    <div class="tips-2" v-html="tips2"></div>
                </div>
            </div>
            <div class="el-message-box__btns">
                <button class="el-button el-button--default el-button--large change" type="button" @click="handelDialogClose">
                    <span>{{cancelBtnTxt}}</span>
                </button>
                <button v-if="confirmBtnTxt"
                        class="el-button el-button--default el-button--large el-button--primary" type="button"
                        @click="confirm()">
                    <span>{{confirmBtnTxt}}</span>
                </button>
            </div>
        </div>
    </el-dialog>
</div>


<script>
    $(function () {
        const PromptDialogOptions = {
            data() {
                return {
                    dialogVisible: false,
                    title: '',
                    tips1: '',
                    tips2: '',
                    jumpUrl: '',
                    openTarget: '_self',
                    confirmBtnTxt: '',
                    cancelBtnTxt: '',
                    params: '',
                }
            },

            methods: {
                create(jobId, callback = () => {}) {
                    var _this = this
                    httpPost('/api/person/chat/create-room', {jobId}).then(function (data) {
                        const { title, jumpUrl, confirmBtnTxt, cancelBtnTxt, tips1, tips2, chatId, isTips, target = '_self' } = data

                        _this.jumpUrl = jumpUrl
                        _this.openTarget = target

                        callback(data)

                        if (isTips) {
                            _this.title = title
                            _this.tips1 = tips1
                            _this.tips2 = tips2
                            _this.confirmBtnTxt = confirmBtnTxt
                            _this.cancelBtnTxt = cancelBtnTxt
                            _this.dialogVisible = true
                            return
                        }
                        
                        window.open(jumpUrl, target)
                    })
                },
                confirm() {
                    this.dialogVisible = false
                    window.open(this.jumpUrl, this.openTarget)
                },
                handelDialogClose() {
                    this.dialogVisible = false
                }
            },
            mounted() {}
        }

        const ChatDialogComponent = Vue.createApp(PromptDialogOptions).use(ElementPlus).mount(
            '#checkGenerateChatDialog')

        window.globalComponents = {
            ...window.globalComponents,
            ChatDialogComponent
        }

    })

</script>