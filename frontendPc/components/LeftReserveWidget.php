<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Yii;
use yii\base\Widget;

class LeftReserveWidget extends BaseWidget
{

    public $list;
    public $columnId;
    public $limit = 8;
    public $templateType;

    public function init()
    {
        parent::init();

        $name               = BaseHomeColumn::carryColumnToPinyin($this->columnId);
        $name               = $name . "_B1";
        $positionId         = BaseHomePosition::findOneVal(['number' => $name], 'id');
        $this->list         = BaseShowcase::getByPositionConfig($positionId, $name, $this->limit);
        $this->templateType = BaseHomeColumn::findOneVal(['id' => $this->columnId], 'template_type');
    }

    public function run(): string
    {
        return $this->render('left_reserve.html', [
            'list'         => $this->list,
            'templateType' => $this->templateType,
        ]);
    }
}