<?php
namespace frontendPc\components;

use common\helpers\UrlHelper;
use frontendPc\models\HomeColumn;
use yii;

class HomeCitySwitchWidget extends BaseWidget
{
    // 这里需求是那数据而不是

    private $list           = [];
    private $ids            = [];
    private $showName       = '全国站';
    private $hotColumn      = [];
    private $provinceColumn = [];
    private $cityColumn     = [];
    private $majorColumn    = [];
    private $specialColumn  = [
        '15',
        '16',
        '17',
        '18',
        '19',
        '20',
        '21',
        '22',
        '23',
        '24',
        '25',
        '26',
        '27',
        '28',
        '29',
        '30',
        '31',
        '32',
        '33',
        '34',
        '35',
        '36',
        '37',
        '38',
        '39',
        '40',
        '41',
        '42',
        '43',
        '44',
        '11',
        '12',
        '13',
        '14',
        '45',
        '46',
        '47',
        '48',
        '49',
        '50',
        '51',
        '52',
        '53',
        '54',
        '55',
        '56',
        '57',
        '58',
        '59',
        '60',
        '61',
        '62',
        '63',
        '64',
        '65',
        '66',
        '67',
        '68',
        '69',
        '70',
        '71',
        '72',
        '73',
        '74',
        '75',
        '76',
        '77',
        '78',
        '79',
        '80',
        '81',
        '82',
        '83',
        '84',
        '85',
        '86',
        '87',
        '88',
        '89',
        '90',
        '91',
        '92',
        '93',
        '94',
        '95',
        '96',
        '97',
        '98',
        '99',
        '100',
        '101',
        '102',
        '103',
        '104',
        '105',
        '106',
        '107',
        '108',
        '109',
        '110',
        '111',
        '112',
        '113',
        '114',
        '115',
        '116',
        '117',
        '118',
    ];
    private $activityList   = [
        [
            'name' => '海外引才',
            'list' => [
                [
                    'name' => '海外宣讲会',
                    'url'  => 'https://haiwai.gaoxiaojob.com/chuhai/haiwaizhuanchang',
                ],
                //                [
                //                    'name' => '组团招聘',
                //                    'url'  => 'https://haiwai.gaoxiaojob.com/chuhai',
                //                ],
            ],
        ],
        [
            'name' => '招聘活动',
            'list' => [
                [
                    'name' => '现场招聘会',
                    //                    'url'  => 'https://www.gaoxiaojob.com/zhaopin/zt/yca_quanguoxunhui2025/index.html',
                    'url'  => 'https://zhaopinhui.gaoxiaojob.com',
                ],
                [
                    'name' => '线上RPO面试会',
                    'url'  => 'https://zhaopinhui.gaoxiaojob.com',
                    //                    'url'  => 'https://www.gaoxiaojob.com/zhaopin/zt/yca_rpoxianshangmianshi2025/index.html',
                ],
                [
                    'name' => '全球直播交流会',
                    'url'  => 'https://zhaopinhui.gaoxiaojob.com/yincai?activityType=16',
                    //                    'url'  => 'https://www.gaoxiaojob.com/zhaopin/zt/yca_quanqiuyincai2024/index.html',
                ],
            ],
        ],
        [
            'name' => '人才论坛',
            'list' => [
                [
                    'name' => '青年学者论坛',
                    'url'  => 'https://haiwai.gaoxiaojob.com/guiguo/xuezheluntan',
                ],
                [
                    'name' => '学子归国活动',
                    'url'  => 'https://haiwai.gaoxiaojob.com/guiguo',
                ],
            ],
        ],
        [
            'name' => '推介服务',
            'list' => [
                [
                    'name' => '高才猎头',
                    'url'  => 'https://www.gaoxiaojob.com/zhaopin/zt/ycc_lietoufuwu2023/index.html',
                ],
                [
                    'name' => '精准直荐',
                    'url'  => 'https://zhaopinhui.gaoxiaojob.com/xianshang/zhyczywlsbxksxzjh.html',
                ],
            ],
        ],
    ];

    public function init()
    {
        parent::init();
        $params = Yii::$app->params['homeStation'];
        foreach ($params as &$param) {
            $param['url'] = HomeColumn::getDetailUrl($param['id']);;
            $this->ids[] = $param['id'];
        }
        $route = Yii::$app->controller->getRoute();
        if ($route == 'home/column') {
            // 并且是上面这些栏目的id
            $id = Yii::$app->request->get('id');
            if (in_array($id, $this->ids)) {
                $key                       = array_keys($this->ids, $id);
                $params[$key[0]]['active'] = true;
            }
            // $this->showName            = $params[$key[0]]['name'];
            // 找到当前栏目的模板
            $homeColumn = HomeColumn::find()
                ->select('template_type,name,id')
                ->where(['id' => $id])
                ->asArray()
                ->one();
            if ($homeColumn['template_type'] == HomeColumn::TEMPLATE_TYPE_AREA) {
                $this->showName = $homeColumn['name'] . '站';
            } else {
                if (in_array($homeColumn['id'], $this->specialColumn)) {
                    $this->showName = $homeColumn['name'] . '站';
                } else {
                    // 特殊栏目设置
                    $this->showName = '';
                }
            }
            // 实际上这个时候已经是要拿栏目名称了

        } else {
            $this->showName = '';
        }

        // 公告和简章内容
        $announcementAndChaptersList = Yii::$app->params['announcementAndChapters'];
        $hotColumn                   = $announcementAndChaptersList['hotColumn'];
        $homeNavArea                 = Yii::$app->params['homeNavArea'];
        $provinceColumn              = $homeNavArea['province'];
        $cityColumn                  = $homeNavArea['city'];
        $majorColumn                 = $announcementAndChaptersList['majorColumn'];

        $this->list           = $params;
        $this->hotColumn      = $this->supplementUrl($hotColumn);
        $this->provinceColumn = $this->supplementUrl($provinceColumn);
        $this->cityColumn     = $this->supplementUrl($cityColumn);
        $this->majorColumn    = $this->supplementUrl($majorColumn);
    }

    public function supplementUrl($list)
    {
        foreach ($list as &$item) {
            if (isset($item['id']) && $item['id']) {
                $item['url'] = HomeColumn::getDetailUrl($item['id']);
            } elseif (isset($item['action']) && $item['action']) {
                $action      = $item['action'];
                $item['url'] = UrlHelper::$action();
            }
        }

        return $list;
    }

    public function run()
    {
        $isHome = Yii::$app->controller->getRoute() == 'home/index';

        if ($isHome) {
            $this->showName = '全国站';
        }

        return $this->render('home_city_switch.html', [
            'list'           => $this->list,
            'showName'       => $this->showName,
            'isHome'         => $isHome,
            'hotColumn'      => $this->hotColumn,
            'provinceColumn' => $this->provinceColumn,
            'cityColumn'     => $this->cityColumn,
            'majorColumn'    => $this->majorColumn,
            'activityList'   => $this->activityList,
        ]);
    }
}