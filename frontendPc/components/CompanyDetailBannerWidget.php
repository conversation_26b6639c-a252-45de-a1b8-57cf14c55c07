<?php

namespace frontendPc\components;

use common\base\models\BaseCompanyCollect;
use common\base\models\BaseMember;
use frontendPc\models\Company;
use Yii;
use yii\base\Widget;

class CompanyDetailBannerWidget extends Widget
{
    public $companyId;
    public $info;
    public $announcementActive;
    public $jobActive;
    public $activityActive;
    public $detailActive;

    public function init()
    {
        parent::init();
        $memberId = Yii::$app->user->id;
        $action   = Yii::$app->controller->action->id;
        switch ($action) {
            case 'detail-announcement-list':
                $this->announcementActive = true;
                break;
            case 'detail-job-list':
                $this->jobActive = true;
                break;
            case 'detail-activity-list':
                $this->activityActive = true;
                break;
            case 'detail':
                $this->detailActive = true;
                break;
        }

        $this->info = Company::getDetailBannerInfo($this->companyId);

        $this->info['isCollect'] = BaseCompanyCollect::getCollectStatus($this->info['companyId'], $memberId);
    }

    public function run()
    {
        $welfareLabelViewAmount = Yii::$app->params['welfareLabelViewAmount'];

        $detailUrl             = '/company/detail/' . $this->companyId . '.html';
        $detailJobUrl          = '/company/detail/' . $this->companyId . '_j.html';
        $detailAnnouncementUrl = '/company/detail/' . $this->companyId . '_a.html';
        $detailActivityUrl     = '/company/detail/' . $this->companyId . '_h.html';

        return $this->render('company_detail_banner.html', [
            'info'                   => $this->info,
            'welfareLabelViewAmount' => $welfareLabelViewAmount,
            'detailUrl'              => $detailUrl,
            'detailJobUrl'           => $detailJobUrl,
            'detailAnnouncementUrl'  => $detailAnnouncementUrl,
            'detailActivityUrl'      => $detailActivityUrl,
            'announcementActive'     => $this->announcementActive ?: '',
            'jobActive'              => $this->jobActive ?: '',
            'activityActive'         => $this->activityActive ?: '',
            'detailActive'           => $this->detailActive ?: '',
        ]);
    }
}