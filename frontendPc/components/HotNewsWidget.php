<?php
namespace frontendPc\components;

use common\helpers\TimeHelper;
use frontendPc\models\Article;
use frontendPc\models\ArticleAttribute;
use frontendPc\models\HomePosition;
use frontendPc\models\News;
use yii\base\Widget;

class HotNewsWidget extends BaseWidget
{
    // 这里需求是那数据而不是

    private $list = [];
    private $top  = [];

    public function init()
    {
        parent::init();
        $top = Article::find()
            ->alias('a')
            ->select('title,a.type,c.id,sort_time,cover_thumb')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'a.id=b.article_id')
            ->innerJoin(['c' => News::tableName()], 'a.id=c.article_id')
            ->where([
                'b.type'      => ArticleAttribute::ATTRIBUTE_HOT_IMAGE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'c.status'    => News::STATUS_ACTIVE,
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.type'      => Article::TYPE_NEWS,
            ])
            ->orderBy('b.sort_time desc')
            ->asArray()
            ->one();

        $top['url']  = News::getDetailUrl($top['id']);
        $top['time'] = TimeHelper::short($top['sort_time']);

        $list = Article::find()
            ->alias('a')
            ->select('title,a.type,c.id,sort_time')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'a.id=b.article_id')
            ->innerJoin(['c' => News::tableName()], 'a.id=c.article_id')
            ->where([
                'b.type'      => ArticleAttribute::ATTRIBUTE_HOT_ARTICLE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'c.status'    => News::STATUS_ACTIVE,
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.type'      => Article::TYPE_NEWS,
            ])
            ->andWhere([
                '<>',
                'c.id',
                $top['id'],
            ])
            ->limit(10)
            ->orderBy('b.sort_time desc')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['url']  = News::getDetailUrl($item['id']);
            $item['time'] = TimeHelper::short($item['sort_time']);
        }

        $this->top  = $top;
        $this->list = $list;
    }

    public function run()
    {
        return $this->render('hot_news.html', [
            'list' => $this->list,
            'top'  => $this->top,
        ]);
    }
}