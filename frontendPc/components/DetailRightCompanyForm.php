<?php

namespace frontendPc\components;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseTrade;
use frontendPc\models\Trade;

class DetailRightCompanyForm extends BaseWidget
{
    public $companyId;

    public function run()
    {
        $info       = BaseCompany::find()
            ->where(['id' => $this->companyId])
            ->select([
                'id',
                'full_name',
                'english_name',
                'industry_id',
                'scale',
                'type',
                'logo_url',
                'is_cooperation',
            ])
            ->asArray()
            ->one();
        $companyUrl = BaseCompany::getDetailUrl($info['id']);

        if ($info['is_cooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
            $companyAnnouncementUrl = '/company/detail/' . $info['id'] . '_a.html';
            $companyJobUrl          = '/company/detail/' . $info['id'] . '_j.html';
        } else {
            $companyAnnouncementUrl = $companyUrl . '#announcement';
            $companyJobUrl          = $companyUrl . '#job';
        }

        $data = [
            'companyLogo'                      => BaseCompany::getLogoFullUrl($info['logo_url']),
            'companyName'                      => $info['full_name'],
            'companyTrade'                     => BaseTrade::getIndustryName($info['industry_id']),
            'companyScale'                     => BaseDictionary::getCompanyScaleName($info['scale']),
            'companyType'                      => BaseDictionary::getCompanyTypeName($info['type']),
            'companyAnnouncementRecruitAmount' => BaseAnnouncement::getCompanyOnLineAnnouncementAmount($info['id']),
            'companyJobRecruitAmount'          => BaseJob::getCompanyJobAmount($info['id']),
            'companyUrl'                       => $companyUrl,
            'companyAnnouncementUrl'           => $companyAnnouncementUrl,
            'companyJobUrl'                    => $companyJobUrl,
        ];

        return $this->render('detail_right_company_form.html', ['info' => $data]);
    }

}