<?php
namespace frontendPc\components;

use frontendPc\models\HomeColumn;
use yii\base\Widget;

class RegionNavWidget extends BaseWidget
{
    private array $list = [];
    public        $columnId;
    public        $is_region_nav;

    public function init()
    {
        parent::init();

        //地区栏目导航
        $this->is_region_nav = true;
        switch ($this->columnId) {
            case 1:
                $list = \Yii::$app->params['configure']['college_nav'];
                break;
            case 2:
                $list = \Yii::$app->params['configure']['school_nav'];
                break;
            case 3:
                $list = \Yii::$app->params['configure']['scientific_nav'];
                break;
            case 4:
                $list = \Yii::$app->params['configure']['office_nav'];
                break;
            case 5:
                $list = \Yii::$app->params['configure']['medical_nav'];
                break;
            case 6:
                $list = \Yii::$app->params['configure']['enterprise_nav'];
                break;
            default:
                $this->is_region_nav = false;
                $list                = [];
        }

        foreach ($list as $k => $v) {
            $list[$k]['link'] = HomeColumn::getDetailUrl($v['id']);
        }
        $this->list = $list;
    }

    public function run()
    {
        return $this->render('region_nav.html', [
            'list'          => $this->list,
            'is_region_nav' => $this->is_region_nav,
        ]);
    }
}