<?php
namespace frontendPc\components;

use common\base\models\BaseMemberMessage;
use common\helpers\UrlHelper;
use frontendPc\models\HomeColumn;
use frontendPc\models\MemberMessage;
use frontendPc\models\Resume;
use yii\base\Widget;
use Yii;

class HeaderContainerWidget extends BaseWidget
{
    private $hotColumn;
    private $provinceColumn;
    private $cityColumn;
    private $majorColumn;
    private $vipCardInfo;
    private $unreadCount    = 0;
    private $unreadCountArr = [];

    public function init()
    {
        parent::init();

        // 公告和简章内容
        $announcementAndChaptersList = Yii::$app->params['announcementAndChapters'];
        $homeSubNav                  = Yii::$app->params['homeSubNav'];
        $hotColumn                   = $announcementAndChaptersList['hotColumn'];
        $homeNavArea                 = Yii::$app->params['homeNavArea'];
        $provinceColumn              = $homeNavArea['province'];
        $cityColumn                  = $homeNavArea['city'];
        $majorColumn                 = $announcementAndChaptersList['majorColumn'];

        $this->hotColumn      = $this->supplementUrl($hotColumn);
        $this->provinceColumn = $this->supplementUrl($provinceColumn);
        $this->cityColumn     = $this->supplementUrl($cityColumn);
        $this->majorColumn    = $this->supplementUrl($majorColumn);
        $memberId             = Yii::$app->user->id;
        if ($memberId) {
            $this->unreadCountArr = BaseMemberMessage::getUnreadData($memberId);
            $this->vipCardInfo    = Resume::getVipCardInfo($memberId);
            //            $this->unreadCount = MemberMessage::getUnreadCount($memberId);
        }
    }

    public function supplementUrl($list)
    {
        foreach ($list as &$item) {
            if (isset($item['id']) && $item['id']) {
                $item['url'] = HomeColumn::getDetailUrl($item['id']);
            } elseif (isset($item['action']) && $item['action']) {
                $action      = $item['action'];
                $item['url'] = UrlHelper::$action();
            }
        }

        return $list;
    }

    public function run()
    {
        $isJob     = in_array(Yii::$app->controller->getRoute(), [
            'job/index',
            'job/new-index',
        ]);
        $isCompany = Yii::$app->controller->getRoute() === 'company/index';

        return $this->render('header_container.html', [
            'hotColumn'          => $this->hotColumn,
            'provinceColumn'     => $this->provinceColumn,
            'cityColumn'         => $this->cityColumn,
            'majorColumn'        => $this->majorColumn,
            'isJob'              => $isJob,
            'isCompany'          => $isCompany,
            'vipCardInfo'        => $this->vipCardInfo,
            'totalMessageAmount' => $this->unreadCountArr['totalAmount'] > 99 ? '99+' : $this->unreadCountArr['totalAmount'],
            'jobTrendAmount'     => $this->unreadCountArr['jobTrendAmount'] > 99 ? '99+' : $this->unreadCountArr['jobTrendAmount'],
            'systemAmount'       => $this->unreadCountArr['systemAmount'] > 99 ? '99+' : $this->unreadCountArr['systemAmount'],
            'chatAmount'         => $this->unreadCountArr['chatAmount'] > 99 ? '99+' : $this->unreadCountArr['chatAmount'],

        ]);
    }
}