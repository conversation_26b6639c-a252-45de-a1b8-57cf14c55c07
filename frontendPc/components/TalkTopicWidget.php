<?php
namespace frontendPc\components;

use common\base\models\BaseTopic;
use common\helpers\FileHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use yii\base\Widget;

class TalkTopicWidget extends BaseWidget
{

    private $list = [];
    private $top  = [];

    public function init()
    {
        parent::init();

        $key = Cache::PC_HOME_TALK_KEY;

        if (Cache::get($key)) {
            $data = json_decode(Cache::get($key), true);
        } else {
            // 拿话题的数据
            $detail = BaseTopic::find()
                ->where(['status' => BaseTopic::STATUS_ACTIVE])
                ->orderBy('id desc')
                ->asArray()
                ->one();

            $list = json_decode($detail['news_list_json'], true);
            // 只要前10条
            $list = array_slice($list, 0, 10);

            foreach ($list as &$item) {
                $item['url'] = UrlHelper::fix($item['links']);
            }

            $data = [
                'list' => $list,
                'top'  => $detail,
            ];

            Cache::set($key, json_encode($data), 7200);
        }

        $this->list = $data['list'];

        $top = $data['top'];

        $top = [
            'title'    => $top['title'],
            'subTitle' => $top['sub_title'],
            'img'      => FileHelper::getFullUrl($top['cover_url']),
            'url'      => UrlHelper::fix($top['target_url']),
        ];

        $this->top = $top;
    }

    public function run()
    {
        return $this->render('talk_topic.html', [
            'list' => $this->list,
            'top'  => $this->top,
        ]);
    }
}