<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Yii;
use yii\base\Widget;

class RightRecommendUnitWidget extends BaseWidget
{
    public $list;
    public $columnId;
    public $templateType;
    public $data;
    public $limit = 24;

    public function init()
    {
        parent::init();

        $numberName = BaseHomeColumn::carryColumnToPinyin($this->columnId) . "_tuijiandanwei";
        $positionId = BaseHomePosition::findOneVal(['number' => $numberName], 'id');

        $this->list = BaseShowcase::getByPositionConfig($positionId, $numberName, $this->limit);
    }

    public function run()
    {
        return $this->render('right_recommend_unit.html', ['list' => $this->list]);
    }
}