<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

class HotUnitBWidget extends BaseWidget
{
    public $list;
    public $columnId;
    public $tapList;

    public function init()
    {
        parent::init();

        $name         = BaseHomeColumn::carryColumnToPinyin($this->columnId);
        $tapList      = Yii::$app->params['configure']['columnToPosition']['hot_unit_b'];
        $showcaseList = [];
        foreach ($tapList as $v) {
            $rangeName  = $name . "_" . $v['number'];
            $positionId = BaseHomePosition::findOneVal(['number' => $rangeName], 'id');
            $result     = BaseShowcase::getByPositionConfig($positionId, $rangeName);

            //todo 这里做个分拆页 数量可根据实际调整
            $temp           = array_chunk($result, 30);
            $showcaseList[] = $temp;
        }

        $this->list    = $showcaseList;
        $this->tapList = $tapList;
    }

    public function run(): string
    {
        return $this->render('hot_unit_b.html', [
            'list'    => $this->list,
            'tapList' => $this->tapList,
        ]);
    }
}