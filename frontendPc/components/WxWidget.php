<?php
namespace frontendPc\components;

use common\helpers\FileHelper;
use common\helpers\UrlHelper;
use frontendPc\models\HomePosition;
use frontendPc\models\ShowCase;
use yii\base\Widget;

class WxWidget extends BaseWidget
{

    private $positionKey = 'wx';
    private $list        = [];
    private $top         = [];

    public function init()
    {
        parent::init();
        $positionId = HomePosition::findOneVal([
            'number'        => $this->positionKey,
            'status'        => HomePosition::STATUS_ACTIVE,
            'platform_type' => HomePosition::PLATFORM_PC_HOME,
        ], 'id');
        $list       = ShowCase::getByPositionConfig($positionId, $this->positionKey, 3);
        $top        = array_slice($list, 0, 1)[0];
        $list       = array_slice($list, 1, 2);

        $top['url'] = UrlHelper::fix($top['target_link']);
        $top['img'] = FileHelper::getFullUrl($top['image_url']);

        foreach ($list as &$item) {
            $item['url'] = UrlHelper::fix($item['target_link']);
            $item['img'] = FileHelper::getFullUrl($item['image_url']);
        }

        $this->top  = $top;
        $this->list = $list;
    }

    public function run()
    {

        return $this->render('wx.html', [
            'list' => $this->list,
            'top'  => $this->top,
        ]);
    }
}