<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

class LatestAnnouncementWidget extends BaseWidget
{
    public $list;
    public $columnId;
    public $limit = 12;
    public $majorId;
    public $educationType;
    public $cityId;
    public $jobCategoryId;
    public $page;
    public $count;

    /**
     * @throws \Exception
     */
    public function init()
    {
        parent::init();
        $keywords = [
            'columnId' => $this->columnId,
            'key'      => 'latest_announcement',
            'limit'    => $this->limit,
            'page'     => $this->page ?: 1,
        ];

        $announcementList = BaseAnnouncement::changeSecondColumnAnnouncementList($keywords);

        $this->list = $announcementList;
    }

    public function run(): string
    {
        return $this->render('latest_announcement.html', [
            'list' => $this->list,
        ]);
    }
}