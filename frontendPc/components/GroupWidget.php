<?php
namespace frontendPc\components;

use common\helpers\StringHelper;
use yii\base\Widget;
use Yii;

class GroupWidget extends BaseWidget
{
    private $qq           = [];
    private $wx           = [];
    private $showPosition = 'right';

    public function init()
    {
        parent::init();
        // 拿配置文件做为list

        $homeGroup  = Yii::$app->params['homeGroup'];
        $controller = \Yii::$app->controller->id;
        // 当前的action
        $action = \Yii::$app->controller->action->id;
        if ($controller == 'home' && $action == 'column') {
            if (Yii::$app->request->get('id') == '10') {
                $this->showPosition = 'left';
            }
        }
        $this->qq = $homeGroup['qq'];
        $this->wx = $homeGroup['wx'];
    }

    public function run()
    {
        return $this->render('group.html', [
            'qq'           => $this->qq,
            'wx'           => $this->wx,
            'showPosition' => $this->showPosition,
        ]);
    }
}