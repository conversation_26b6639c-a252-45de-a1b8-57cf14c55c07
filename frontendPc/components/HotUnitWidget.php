<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Yii;
use yii\base\Widget;

class HotUnitWidget extends BaseWidget
{

    public $target;
    public $list;
    public $tapList;
    public $columnId;
    public $templateType;
    public $count;

    public function init()
    {
        parent::init();

        $name            = BaseHomeColumn::carryColumnToPinyin($this->columnId);
        $homeColumnModel = BaseHomeColumn::findOne(['id' => $this->columnId]);
        if ($homeColumnModel['template_type'] == BaseHomeColumn::TEMPLATE_TYPE_AREA) {
            $tapList = Yii::$app->params['configure']['columnToPosition']['hot_unit_province'];
        } else {
            $tapList = Yii::$app->params['configure']['columnToPosition']['hot_unit'];
        }

        $numberList = [];
        foreach ($tapList as $v) {
            $numberList[] = $name . "_" . $v['number'];
        }

        $showcaseList = [];

        $count = 0;
        foreach ($numberList as $key => $value) {
            $homePosition          = BaseHomePosition::findOne(['number' => $value]);
            $positionId            = $homePosition['id'];
            $temp                  = BaseShowcase::getByPositionConfig($positionId, $value);
            $count                 += sizeof($temp);
            $showcaseList[]        = array_chunk($temp, 48);
            $tapList[$key]['name'] = $homePosition['chinese_name'];
        }

        $this->list    = $showcaseList;
        $this->tapList = $tapList;
        $this->count   = $count;
    }

    public function run(): string
    {
        return $this->render('hot_unit.html', [
            'count'   => $this->count,
            'list'    => $this->list,
            'tapList' => $this->tapList,
        ]);
    }
}