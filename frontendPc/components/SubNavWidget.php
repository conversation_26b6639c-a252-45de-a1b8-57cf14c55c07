<?php
namespace frontendPc\components;

use common\base\models\BaseSystemConfig;
use frontendPc\models\HomeColumn;
use yii;

class SubNavWidget extends BaseWidget
{

    // 省区导航
    private $provinceList = [];
    // 城市导航
    private $cityList = [];
    // 学科分类
    private $majorList = [];
    // 特色专栏
    private $columnList = [];
    // 职位分类
    private $jobList = [];
    // 单位分类
    private $companyList = [];

    public function init()
    {
        parent::init();

        $config   = Yii::$app->params['homeSubNav'];
        $province = Yii::$app->params['homeNavArea']['province'];
        $city     = Yii::$app->params['homeNavArea']['city'];
        $job      = $config['job'];
        $company  = $config['company'];
        $major    = [];

        foreach ($province as &$provinceItem) {
            $provinceItem['url'] = HomeColumn::getDetailUrl($provinceItem['id']);
        }

        foreach ($city as &$cityItem) {
            $cityItem['url'] = HomeColumn::getDetailUrl($cityItem['id']);
        }

        foreach ($config['major'] as $item) {
            $list = [];
            foreach ($item['list'] as $i) {
                $list[] = [
                    'url'  => HomeColumn::getDetailUrl($i['id']),
                    'name' => $i['name'],
                ];
            }
            $major[] = [
                'name' => $item['name'],
                'list' => $list,
            ];
        }

        foreach ($job as $k1 => $item) {
            foreach ($item['list'] as $k2 => $jobItem) {
                $job[$k1]['list'][$k2]['url'] = $this->getJobUrl($jobItem);
            }
        }

        foreach ($company as $k1 => $item) {
            foreach ($item['list'] as $k2 => $jobItem) {
                $company[$k1]['list'][$k2]['url'] = $this->getCompanyUrl($jobItem);
            }
        }

        $this->provinceList = $province;
        $this->cityList     = $city;

        // 这一块重构了,原来是从配置文件里面取现在可以从数据库取(有一份暂存在redis)
        // $this->columnList   = $config['column'];
        $this->columnList = self::getPcHomeSubNavColumn();

        $this->majorList   = $major;
        $this->jobList     = $job;
        $this->companyList = $company;
    }

    private function getJobUrl($item)
    {
        if ($item['categoryJobId']) {
            return '/job?jobType=' . $item['categoryJobId'];
        }

        if ($item['fullUrl']) {
            // 因为这里暂时只有博士后站点，所以直接返回全fullUrl
            return $item['fullUrl'];
        }
    }

    private function getCompanyUrl($item)
    {
        if ($item['typeId']) {
            return '/company?companyType=' . $item['typeId'];
        }
        if ($item['areaId']) {
            return '/company?areaId=' . $item['areaId'];
        }
    }

    private function getPcHomeSubNavColumn()
    {
        return [
            BaseSystemConfig::getPcHomeSubNavColumnSpecial(),
            BaseSystemConfig::getPcHomeSubNavColumnCIA(),
            BaseSystemConfig::getPcHomeSubNavColumnServiceStation(),
        ];
    }

    public function run()
    {
        return $this->render('sub_nav.html', [
            'provinceList' => $this->provinceList,
            'cityList'     => $this->cityList,
            'majorList'    => $this->majorList,
            'columnList'   => $this->columnList,
            'jobList'      => $this->jobList,
            'companyList'  => $this->companyList,
        ]);
    }
}