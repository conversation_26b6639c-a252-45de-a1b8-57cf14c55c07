<?php

namespace frontendPc\components;

use yii\base\Widget;

class HomeLoginFormWidget extends BaseWidget
{
    public function run()
    {
        $privacyPolicyUrl    = \Yii::$app->params['privacyPolicyUrl'];
        $serviceAgreementUrl = \Yii::$app->params['serviceAgreementUrl'];
        $pcDefaultLoginType  = \Yii::$app->params['pcDefaultLoginType'];

        return $this->render('home_login_form.html', [
            'privacyPolicyUrl'    => $privacyPolicyUrl,
            'serviceAgreementUrl' => $serviceAgreementUrl,
            'pcDefaultLoginType'  => $pcDefaultLoginType,
        ]);
    }
}