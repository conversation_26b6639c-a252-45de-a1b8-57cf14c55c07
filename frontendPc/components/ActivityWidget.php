<?php
namespace frontendPc\components;

use common\base\models\BaseFile;
use common\base\models\BaseSystemConfig;
use common\helpers\FileHelper;
use common\helpers\UrlHelper;

class ActivityWidget extends BaseWidget
{

    private $positionKey = 'activity';
    private $list        = [];

    public function init()
    {
        parent::init();
        // 拿配置文件做为list

        // $list = Yii::$app->params['homePosition']['otherShowcase'][$this->positionKey];
        $list = BaseSystemConfig::getPcHomeActivity();

        foreach ($list as &$item) {
            $item['img'] = FileHelper::getFullUrl($item['img'], BaseFile::PLATFORM_TYPE_LOCAL);
            $item['url'] = UrlHelper::fix($item['url']);
        }

        $this->list = $list;
    }

    public function run()
    {
        return $this->render('activity.html', ['list' => $this->list]);
    }
}