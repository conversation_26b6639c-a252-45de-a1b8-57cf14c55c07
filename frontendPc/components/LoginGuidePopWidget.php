<?php

namespace frontendPc\components;

use common\helpers\IpHelper;
use frontendPc\models\Resume;
use Yii;

class LoginGuidePopWidget extends BaseWidget
{
    public function run(): string
    {
        //求职者未登录状态，打开非首页的任意页面，登录入口下自动展示提示面板
        //判断url是否首页
        $url     = Yii::$app->request->url;
        $isIndex = $url == '/';

        $ip = IpHelper::getIpInt();

        $isShow = Resume::isShowLoginTipsAmount($ip);

        $showPop = false;
        if ($isShow && !$isIndex) {
            $showPop = true;
        }

        return $this->render('login_guide_pop.html', ['showPop' => $showPop]);
    }

}