<?php
/**
 * create user：shannon
 * create time：2024/12/11 下午12:02
 */
namespace frontendPc\components;

use common\base\models\BaseAnnouncement;
use common\service\specialNeedService\AnnouncementInformationService;
use frontendPc\models\Job;

class AnnouncementJobListWidget extends BaseWidget
{
    public function run(): string
    {
        $searchData       = \Yii::$app->request->get();
        $jobInfo          = Job::getAnnouncementChildJobList($searchData);
        $announcementInfo = BaseAnnouncement::findOne($searchData['id']);
        if ($announcementInfo->all_job_amount > 5) {
            $jobFilter = BaseAnnouncement::getJobListFilter($searchData['id']);
        }

        // 特殊业务逻辑（之前是在controller中处理，现在放到service中处理）
        $jobInfo = (new AnnouncementInformationService())->handelJobList($jobInfo, $searchData['id']);

        return $this->render('announcement_job_list.html', [
            'jobList'   => $jobInfo['list'],
            'jobCount'  => $jobInfo['totalNum'],
            'jobFilter' => $jobFilter ?? [],
        ]);
    }
}