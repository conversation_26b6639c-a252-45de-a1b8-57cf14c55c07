<?php
namespace frontendPc\components;

use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseResumeStatData;
use common\helpers\FileHelper;
use common\models\ResumeRemind;
use common\service\resumeRemind\ResumeRemindApplication;
use frontendPc\models\JobApply;
use frontendPc\models\Member;
use Yii;
use yii\base\Widget;

class PersonalStateWidget extends BaseWidget
{

    public $data = [];

    /**
     * @throws \yii\base\Exception
     */
    public function init()
    {
        $info     = Yii::$app->params['user'];
        $resumeId = Member::getMainId();
        $complete = BaseResume::findOneVal([
            'id' => $resumeId,
        ], 'complete');
        if ($complete >= Yii::$app->params['completeResumePercent']) {
            $isComplete = true;
        } else {
            $isComplete = false;
        }
        // $applyAmount = BaseResumeStatData::getApplyTotalAmount($resumeId);
        //获取面试邀请次数
        // $interviewAmount = BaseResumeStatData::findOneVal(['resume_id' => $resumeId], 'interview_amount');
        // 还有获取强提醒的信息
        $remindApp  = ResumeRemindApplication::getInstance();
        $remindData = $remindApp->getAll($resumeId);
        $isFeedback = $remindData['job_apply_all_count'] > 0 ? true : false;
        $jobTrend   = BaseResume::getJobTrend(Yii::$app->user->id);
        $isVip      = BaseResume::checkVip(Yii::$app->user->id);
        //简历完成步数
        $resumeStepNum = BaseResumeComplete::getResumeStep($resumeId);
        if ($resumeStepNum > 3) {
            $isStep = true;
        } else {
            $isStep = false;
        }
        //简历置顶
        $isResumeTop = BaseResumeEquityPackage::isEquityEffect(BaseResumeEquitySetting::ID_RESUME_TOP, $resumeId);

        $data = [
            'applyAmount'             => $jobTrend['jobApplyAmount'] > 999 ? '999+' : $jobTrend['jobApplyAmount'],
            // 'interviewAmount'         => $jobTrend['jobInviteAmount'] > 999 ? '999+' : $interviewAmount,
            'jobInviteAmount'         => $jobTrend['jobInviteAmount'] > 999 ? '999+' : $jobTrend['jobInviteAmount'],
            'jobInviteRemindAmount'   => $remindData['job_invite_count'] > 99 ? '99+' : $remindData['job_invite_count'],
            'companyViewAmount'       => $jobTrend['resumeViewAmount'] > 999 ? '999+' : $jobTrend['resumeViewAmount'],
            'companyViewRemindAmount' => $remindData['company_view_count'] > 99 ? '99+' : $remindData['company_view_count'],
            'isFeedback'              => $isFeedback,
            'isComplete'              => $isComplete,
            'name'                    => $info['showName'],
            'avatar'                  => $info['avatar'],
            'complete'                => $complete,
            'isVip'                   => $isVip,
            'resumeStepNum'           => $resumeStepNum,
            'isStep'                  => $isStep,
            'isResumeTop'             => $isResumeTop,
        ];

        $this->data = $data;
    }

    public function run(): string
    {
        return $this->render('personal_state.html', [
            'data' => $this->data,
        ]);
    }
}