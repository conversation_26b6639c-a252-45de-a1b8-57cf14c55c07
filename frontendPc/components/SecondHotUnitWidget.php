<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Yii;
use yii\base\Widget;

class SecondHotUnitWidget extends BaseWidget
{

    public $target;
    public $list;
    public $columnId;
    public $templateType;
    public $count;

    public function init()
    {
        parent::init();

        $name         = BaseHomeColumn::carryColumnToPinyin($this->columnId);
        $number       = $name . '_remendanwei';
        $showcaseList = [];

        $homePosition          = BaseHomePosition::findOne(['number' => $number]);
        $positionId            = $homePosition['id'];
        $temp                  = BaseShowcase::getByPositionConfig($positionId, $number);
        $count                 = sizeof($temp);
        $showcaseList[]        = array_chunk($temp, 36);

        $this->list    = $showcaseList;
        $this->count   = $count;
    }

    public function run(): string
    {
        return $this->render('second_hot_unit.html', [
            'count'   => $this->count,
            'list'    => $this->list,
        ]);
    }
}