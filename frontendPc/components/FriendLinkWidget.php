<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseFriendLinkConfig;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

class FriendLinkWidget extends BaseWidget
{

    public $showcaseName;
    public $list;
    public $columnId;

    public function init()
    {
        parent::init();

        //        $showcaseList = Yii::$app->params['configure']['friend-link'];
        $showcaseList = BaseFriendLinkConfig::getList();

        $this->list = $showcaseList;
    }

    public function run(): string
    {
        // 非首页不渲染
        // 当前的控制器
        $controller = \Yii::$app->controller->id;
        // 当前的action
        $action = \Yii::$app->controller->action->id;

        if ($controller == 'home' && $action == 'index') {
            return $this->render('friend_link.html', [
                'list' => $this->list,
            ]);
        }

        return '';
    }
}