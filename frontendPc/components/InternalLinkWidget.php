<?php

namespace frontendPc\components;

use admin\models\Member;
use common\base\models\BaseCompany;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\libs\Cache;
use common\service\CommonService;
use common\service\engine\SearchService;
use frontendPc\models\HomeColumn;
use \Yii;

class InternalLinkWidget extends BaseWidget
{
    private array $data     = [];
    public bool   $isColumn = true;
    const LIMIT_NUM      = 15;
    const REGION_SUFFIX  = '高校人才网';
    const MAJOR_SUFFIX   = '招聘';
    const COMPANY_SUFFIX = '招聘';

    public function init()
    {
        parent::init();
        // 当前的action
        $controller = strtoupper(Yii::$app->controller->id);
        $action     = strtoupper(\Yii::$app->controller->action->id);
        $id         = Yii::$app->request->get('id');
        //key拼上个类型，以及id
        $key = Cache::PC_FOOTER_INTERNAL_LINK_SEO_KEYWORD . ':' . $controller . '_' . $action . ':' . $id;
        if (Cache::get($key)) {
            $this->data = json_decode(Cache::get($key), true);
        } else {
            //准备下三大板块数据
            //热门地区栏目
            $this->data['hotRegionList'] = self::getHotRegionList();

            //获取热门学科栏目（即学科表二级学科内容）
            $this->data['hotMajorList'] = self::getHotMajorList();

            //获取热门单位栏目
            $this->data['hotCompanyList'] = self::getHotCompanyList();
            Cache::set($key, json_encode($this->data), 3600 * 24 * 90);
        }
        //获取热门岗位与附近职位
        $params                    = [
            $controller,
            $action,
            $id,
        ];
        $search_model              = new SearchService();
        $post_job_data             = $search_model->setPlatform(CommonService::PLATFORM_WEB)->runHotData($params);
        $this->data['hotPostList'] = $post_job_data['post_hot'];
        $this->data['hotJobList']  = $post_job_data['job_hot'];
    }

    public function run(): string
    {
        return $this->render('internal_link.html', [
            'data'     => $this->data,
            'isColumn' => $this->isColumn,
        ]);
    }

    /**
     * 获取热门地区列表
     * @return array
     */
    private function getHotRegionList()
    {
        //热门地区内容，读取
        $hotRegionColumn = Yii::$app->params['homeRegionColumn'];
        $regionList      = $hotRegionColumn['list'];
        $hotRegionList   = [];

        foreach ($regionList as &$region) {
            foreach ($region['province'] as &$province) {
                $hotRegionList[] = [
                    'name' => $province['name'] . self::REGION_SUFFIX,
                    'id'   => $province['id'],
                    'url'  => HomeColumn::getDetailUrl($province['id']),
                ];
                foreach ($province['city'] as &$city) {
                    $hotRegionList[] = [
                        'name' => $city['name'] . self::REGION_SUFFIX,
                        'id'   => $city['id'],
                        'url'  => HomeColumn::getDetailUrl($city['id']),
                    ];
                }
            }
        }
        $tmp_arr = [];
        foreach ($hotRegionList as $k => $item) {
            if (in_array($item['id'], $tmp_arr))   //搜索$v[$key]是否在$tmp_arr数组中存在，若存在返回true
            {
                unset($hotRegionList[$k]); //销毁一个变量  如果$tmp_arr中已存在相同的值就删除该值
            } else {
                $tmp_arr[$k] = $item['id'];  //将不同的值放在该数组中保存
            }
        }
        $keyList    = array_rand($hotRegionList, self::LIMIT_NUM);
        $resultList = [];
        foreach ($keyList as $key => $keyNum) {
            $resultList[] = $hotRegionList[$keyNum];
        }

        return $resultList;
    }

    /**
     * 获取热门学科列表
     * @return array
     */
    private function getHotMajorList()
    {
        $majorList    = HomeColumn::getAllMajorColumn();
        $hotMajorList = [];
        foreach ($majorList as $item) {
            foreach ($item['list'] as $item2) {
                $item2['name']  .= self::MAJOR_SUFFIX;
                $hotMajorList[] = $item2;
            }
        }
        $keyList    = array_rand($hotMajorList, self::LIMIT_NUM);
        $resultList = [];
        foreach ($keyList as $key => $keyNum) {
            $resultList[] = $hotMajorList[$keyNum];
        }

        return $resultList;
    }

    private function getHotCompanyList()
    {
        $list = BaseCompany::find()
            ->alias('c')
            ->innerJoin(['m' => Member::tableName()], 'm.id = c.member_id')
            ->where([
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
                'c.package_type'   => [
                    BaseCompany::PACKAGE_TYPE_FREE,
                    BaseCompany::PACKAGE_TYPE_SENIOR,
                ],
                'c.status'         => BaseCompany::STATUS_ACTIVE,
                'm.status'         => [
                    BaseMember::STATUS_ACTIVE,
                    BaseMember::STATUS_ILLEGAL,
                ],
            ])
            ->select([
                'c.id',
                'c.full_name as name',
            ])
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['url']  = BaseCompany::getDetailUrl($item['id']);
            $item['name'] .= self::COMPANY_SUFFIX;
        }
        $keyList    = array_rand($list, self::LIMIT_NUM);
        $resultList = [];
        foreach ($keyList as $key => $keyNum) {
            $resultList[] = $list[$keyNum];
        }

        return $resultList;
    }

}