<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

class DialogResumePayWidget extends BaseWidget
{
    public $cid;

    public function init()
    {
        parent::init();
        // 检查一下cid

    }

    public function run(): string
    {
        return $this->render('resume_pay_dialog_widget.html', [
            'list' => $this->cid,
        ]);
    }
}