<?php

namespace frontendPc\components;

use frontendPc\models\Major;
use yii\base\Widget;

class DialogCascaderMajorWidget extends BaseWidget
{
    private array $list = [];

    public function init()
    {
        // 学科分类一二级回显
        $this->list = Major::getAllListByLevel2();
    }

    public function run(): string
    {
        return $this->render('dialog_cascader_major.html', [
            'list' => $this->list,
        ]);
    }
}