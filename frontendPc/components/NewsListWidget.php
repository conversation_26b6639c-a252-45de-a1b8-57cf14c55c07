<?php
namespace frontendPc\components;

use common\base\models\BaseArticle;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseNews;
use common\base\models\BaseNewsCollect;
use frontendPc\models\HomeColumn;
use frontendPc\models\News;
use yii\base\Widget;
use Yii;

class NewsListWidget extends BaseWidget
{
    public $list;
    public $tapList;
    public $columnId;

    public function init()
    {
        parent::init();
        $columnId      = $this->columnId;
        $config        = Yii::$app->params['firstLevelColumn']['news']['news_list'];
        $limit         = $config['count'];
        $subColumnList = HomeColumn::getSecondList($columnId);

        if (!$subColumnList) {
            $firstLevelColumnId = HomeColumn::findOneVal(['id' => $columnId], 'parent_id');
            $subColumnList      = HomeColumn::getSecondList($firstLevelColumnId);
        }

        $list = [];
        $name = [];
        $id   = [];
        foreach ($subColumnList as $key => $item) {
            $name[]          = BaseHomeColumn::findOneVal(['id' => $item['id']], 'name') ?: '';
            $id[]            = BaseHomeColumn::findOneVal(['id' => $item['id']], 'id') ?: '';
            $list['tapList'] = array_combine($id, $name);
            $list['list'][]  = BaseArticle::find()
                ->alias('ar')
                ->leftJoin(['n' => BaseNews::tableName()], 'ar.id = n.article_id')
                ->leftJoin(['ac' => BaseArticleColumn::tableName()], 'ac.article_id = ar.id')
                ->leftJoin(['nc' => BaseNewsCollect::tableName()], 'nc.news_id = n.id')
                ->select([
                    'ar.home_sub_column_ids',
                    'ar.release_time',
                    'ar.title',
                    'ar.seo_description',
                    'ar.click',
                    'ar.cover_thumb',
                    'ar.link_url',
                    'n.id',
                    'COUNT(nc.news_id) as collectNum',
                ])
                ->where([
                    'ac.column_id' => $item['id'],
                    'ar.status'    => BaseArticle::STATUS_ACTIVE,
                    'ar.is_delete' => BaseArticle::IS_DELETE_NO,
                    'ar.type'      => BaseArticle::TYPE_NEWS,
                ])
                ->groupBy('n.id')
                ->limit($limit)
                ->asArray()
                ->all();
        }

        foreach ($list['list'] as &$item) {
            foreach ($item as $k => &$v) {
                if (strlen($v['cover_thumb']) < 1) {
                    $v['cover_thumb'] = \Yii::$app->params['defaultCoverThumb'];
                }
                $v['url'] = News::getDetailUrl($v['id']);
            }
        }

        $this->list    = $list['list'];
        $this->tapList = $list['tapList'];
    }

    public function run()
    {
        return $this->render('news_list.html', [
            'tapList'  => $this->tapList,
            'list'     => $this->list,
            'columnId' => $this->columnId,
        ]);
    }
}