<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomeColumnDictionaryRelationship;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

class LatestJobClassificationWidget extends BaseWidget
{
    public $list;
    public $columnId;
    public $limit = 12;
    public $majorId;
    public $educationType;
    public $cityId;
    public $jobCategoryId;
    public $page  = 1;
    public $count = 0;

    /**
     * @throws \Exception
     */
    public function init()
    {
        parent::init();
        $keywords = [
            'columnId' => $this->columnId,
            'limit'    => $this->limit,
            'page'     => $this->page ?: 1,
        ];

        $columnDictionaryInfo = BaseHomeColumnDictionaryRelationship::find()
            ->select([
                'main_id',
                'type',
            ])
            ->where(['home_column_id' => $this->columnId])
            ->asArray()
            ->all();

        if ($columnDictionaryInfo) {
            foreach ($columnDictionaryInfo as $columnDictionaryInfoItem) {
                switch ($columnDictionaryInfoItem['type']) {
                    case BaseHomeColumnDictionaryRelationship::TYPE_MAJOR:
                        $columnDictionaryInfoMainId  = $columnDictionaryInfoItem['main_id'];
                        $columnDictionaryInfoType    = $columnDictionaryInfoItem['type'];
                        $columnDictionaryInfoMajorId = $columnDictionaryInfoItem['main_id'];

                        break;
                    case BaseHomeColumnDictionaryRelationship::TYPE_AREA:
                        $columnDictionaryInfoAreaId = $columnDictionaryInfoItem['main_id'];
                        break;
                }
            }
        }

        if ($columnDictionaryInfoMainId) {
            switch ($columnDictionaryInfoType) {
                case BaseHomeColumnDictionaryRelationship::TYPE_MAJOR:
                    $keywords['major_id'] = $columnDictionaryInfoMajorId;
                    break;
                case BaseHomeColumnDictionaryRelationship::TYPE_AREA:
                    break;
            }

        }

        $announcementList = BaseAnnouncement::changeSecondColumnJobList($keywords);

        $this->list  = $announcementList['list'];
        $this->count = $announcementList['count'];
    }

    public function run(): string
    {
        return $this->render('latest_job_classification.html', [
            'list'  => $this->list,
            'count' => $this->count,
        ]);
    }
}