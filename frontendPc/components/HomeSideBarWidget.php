<?php
namespace frontendPc\components;

use common\base\models\BaseChatMessage;
use common\service\resumeRemind\ResumeRemindApplication;
use frontendPc\models\Member;
use yii\base\Widget;
use Yii;

class HomeSideBarWidget extends BaseWidget
{

    public $isShow     = 1;
    public $noShowList = [
        'home/region',
        'home/search',
        'home/sitemap',
        'home/major',
    ];

    public function init()
    {
        parent::init();
    }

    public function run()
    {
        // 获取当前控制器和方法
        $controller = Yii::$app->controller->id;
        $action     = Yii::$app->controller->action->id;
        $isLogin    = 0;
        $thisAction = $controller . '/' . $action;
        if (in_array($thisAction, $this->noShowList)) {
            $this->isShow = 0;
        }

        $level = Yii::$app->request->get('level1');

        if ($level == 'xuqiuxueke') {
            $this->isShow = 0;
        }
        $info = Yii::$app->params['user'];
        if ($info['type'] == 1) {
            $resumeId   = Member::getMainId();
            $remindApp  = ResumeRemindApplication::getInstance();
            $remindData = $remindApp->getAll($resumeId);
            $memberId = Yii::$app->user->id;
            if ($memberId) {
                // 直聊总未读
                $chatAmount = BaseChatMessage::getUnreadAmount($memberId);
                $isLogin = 1;
            }
        }
        if (!$chatAmount) {
            $chatAmount = '';
        }

        return $this->render('home_side_bar.html', [
            'isShow'           => $this->isShow,
            'jobApplyAllCount' => $remindData['job_apply_all_count'] > 99 ? '99+' : $remindData['job_apply_all_count'],
            'jobInviteCount'   => $remindData['job_invite_count'] > 99 ? '99+' : $remindData['job_invite_count'],
            'chatAmount'       => $chatAmount > 99 ? '99+' : $chatAmount,
            'isLogin'          => $isLogin,
        ]);
    }
}