<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

class CurrentLocationWidget extends BaseWidget
{
    public $list;
    public $columnId;

    public function init()
    {
        parent::init();

        $list = BaseHomeColumn::find()
            ->alias('a')
            ->leftJoin(['b' => BaseHomeColumn::tableName()], 'b.id=a.parent_id')
            ->select([
                'a.id',
                'a.name',
                'b.id as parent_id',
                'b.name as parent_name',
            ])
            ->where([
                'a.id' => $this->columnId,
            ])
            ->asArray()
            ->one();

        $list['parent_link'] = BaseHomeColumn::getDetailUrl($list['parent_id']);
        $list['link']        = BaseHomeColumn::getDetailUrl($list['id']);
        $list['parent_name'] = $list['parent_name'] ? ' > ' . $list['parent_name'] : '';
        $this->list          = $list;
    }

    public function run(): string
    {
        return $this->render('current_location.html', [
            'list' => $this->list,
        ]);
    }
}