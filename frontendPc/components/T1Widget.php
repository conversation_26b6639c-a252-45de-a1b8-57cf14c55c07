<?php
namespace frontendPc\components;

use frontendPc\models\HomePosition;

class T1Widget extends BaseWidget
{

    public $showcaseList = [];
    public $position = 'none';

    public function init()
    {
        parent::init();

        // 找到T1的广告位
        $showcaseList = HomePosition::getJobShowcase2024();

        // 把1个复制成14个
        // for ($i = 0; $i < 14; $i++) {
        //     $showcaseList[] = $showcaseList[0];
        // }

        if (count($showcaseList) > 7 ) {
            $this->position = 'outside';
        }
        // 七个为一组，分成N组
        $showcaseList = array_chunk($showcaseList, 7);

        $this->showcaseList = $showcaseList;
    }

    public function run()
    {
        return $this->render('T1.html', [
            'showcaseList' => $this->showcaseList,
            'position'     => $this->position,
        ]);
    }
}