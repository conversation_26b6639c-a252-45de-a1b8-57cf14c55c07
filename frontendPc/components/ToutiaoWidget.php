<?php
namespace frontendPc\components;

class ToutiaoWidget extends BaseWidget
{

    public $isShow = false;

    const ROUTER_LIST = [
        'home/column',
        'announcement/detail',
        'announcement/job-list',
        'job/detail',
        'company/detail',
        'news/detail',
        'engine/index',
    ];

    // https://rw7zrjmygqi.feishu.cn/docx/ZwOAdBTZ5oUHLKxwfR2clp17nXd

    /**
     * 包括：栏目页、公告详情页、公告职位列表页、职位详情页、单位详情页、文章资讯页、seo职位中心页面。
     * ①栏目页：https://www.gaoxiaojob.com/column/*.html
     * ②公告详情页：https://www.gaoxiaojob.com/announcement/detail/*.html
     * ③公告职位列表页：https://www.gaoxiaojob.com/announcement/job-list?id=*
     * ④职位详情页：https://www.gaoxiaojob.com/job/detail/*.html
     * ⑤单位详情页：https://www.gaoxiaojob.com/company/detail/*.html
     * ⑥文章资讯页：https://www.gaoxiaojob.com/news/detail/*.html
     * ⑦seo职位中心页面：https://www.gaoxiaojob.com/rczhaopin*
     */
    public function init()
    {
        // 看一下是那个路由
        $route = \Yii::$app->controller->route;

        if (in_array($route, self::ROUTER_LIST)) {
            $this->isShow = true;
        }
    }

    public function run()
    {
        if (!$this->isShow) {
            return false;
        }
        return $this->render('toutiao.html', []);
    }

}