<?php
namespace frontendPc\components;

use admin\models\Major;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseMajor;
use common\helpers\ArrayHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use yii\base\Widget;

class HomeSelectJobWidget extends BaseWidget
{
    private $hotList;
    private $newList;
    private $majorList;

    public function init()
    {
        parent::init();
        $cacheKey = Cache::PC_HOME_POSITION_SELECT_JOB_KEY;
        $data     = Cache::get($cacheKey);
        if ($data) {
            $data = json_decode($data, true);
        }

        // 下面这里也会导致加载的问题,放缓存

        $hotList   = $data['hotList'];
        $newList   = $data['newList'];
        $majorList = $data['majorList'];

        $baseKey = Cache::PC_HOME_ONLINE_MAJOR_AMOUNT_KEY;

        foreach ($majorList as $k => &$item) {
            $item['index'] = $k < 9 ? '0' . ($k + 1) : ($k + 1);
            switch ($k) {
                case 0:
                    $item['class'] = 'serial-number number-bg number-one';
                    break;
                case 1:
                    $item['class'] = 'serial-number number-bg number-two';
                    break;
                case 2:
                    $item['class'] = 'serial-number number-bg number-three';
                    break;
                case 3:
                    $item['class'] = 'serial-number number-bg number-four';
                    break;
                default:
                    $item['class'] = 'serial-number';
                    break;
            }

            $key   = $baseKey . ':' . $item['majorId'];
            $count = Cache::get($key);
            if (!isset($count)) {
                $count = BaseJob::find()
                    ->select([
                        'id',
                        'name',
                        'job_category_id',
                        'release_time',
                        'click',
                    ])
                    ->where(['status' => BaseJob::STATUS_ONLINE])
                    ->andWhere("find_in_set({$item['majorId']}, major_id)")
                    ->orderBy('release_time DESC')
                    ->asArray()
                    ->count();

                Cache::set($key, $count, 3600);
            }

            $item['count'] = $count;
            // 这个东西是在是比较慢,放缓存里面,1个小时更新一次吧
        }

        $this->hotList   = $hotList;
        $this->newList   = $newList;
        $this->majorList = $majorList;
    }

    private static function getJobInfo($id)
    {
        $key = Cache::PC_HOME_POSITION_SELECT_JOB_DETAIL . ':' . $id;

        $data = Cache::get($key);

        if ($data) {
            return json_decode($data, true);
        }

        $job = BaseJob::find()
            ->alias('a')
            ->select('major_id,experience_type,education_type,announcement_id,wage_type,a.city_id,amount,min_wage,max_wage,b.full_name as company')
            ->leftJoin(['b' => BaseCompany::tableName()], 'a.company_id=b.id')
            ->where(['a.id' => $id])
            ->asArray()
            ->one();

        $majorIds = explode(',', $job['major_id']);

        $length = count($majorIds);
        if ($majorIds[0] && $length > 0) {
            $major = BaseMajor::getAllMajorName($majorIds[0]);
            if ($length > 1) {
                $major .= "等";
            }
        } else {
            $major = "专业不限";
        }
        $educationTypeList = BaseDictionary::getEducationList();
        $experienceList    = BaseDictionary::getExperienceList(BaseDictionary::ADD_UNLIMITED_YES);
        $companyTypeList   = BaseDictionary::getCompanyTypeList();
        $companyNatureList = BaseDictionary::getCompanyNatureList();

        $job['wage']                = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);
        $job['experienceTypeTitle'] = $experienceList[intval($job['experience_type'])] ?: '经验不限';

        if ($job['experienceTypeTitle'] == '不限') {
            $job['experienceTypeTitle'] = '经验不限';
        }
        $job['educationTypeTitle'] = $educationTypeList[$job['education_type']] ?: '学历不限';
        $job['city']               = BaseArea::getAreaName($job['city_id']) ?: '';
        $job['companyTypeTitle']   = $companyTypeList[$job['type']] ?: '';
        $job['companyNatureTitle'] = $companyNatureList[$job['type']] ?: '';
        $job['major']              = $major;
        $job['amount']             = $job['amount'] . '人';
        $job['refresh_time']       = TimeHelper::short($job['refresh_time']);
        $job['url']                = BaseJob::getDetailUrl($id);

        Cache::set($key, json_encode($job), 3600 * 3);

        return $job;
    }

    public function run()
    {
        return $this->render('home_select_job.html', [
            'hotList'   => $this->hotList,
            'newList'   => $this->newList,
            'majorList' => $this->majorList,
        ]);
    }
}