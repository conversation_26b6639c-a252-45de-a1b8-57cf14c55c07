<?php

namespace frontendPc\components;

use common\base\models\BaseArea;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyContact;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseMember;
use common\base\models\BaseTrade;
use common\helpers\FileHelper;
use common\service\CommonService;
use common\service\company\RecommendService;
use frontendPc\models\Company;
use yii\base\Widget;
use Yii;

class CompanyDetailRightWidget extends Widget
{
    public $companyId;
    public $predicationTalentPreviewList = [];
    public $info;

    public function init()
    {
        parent::init();
        $memberId = Yii::$app->user->id;
        $this->getRightNavInfo();
    }

    public function getRightNavInfo()
    {
        $info = BaseCompany::find()
            ->alias('c')
            ->leftJoin(['cc' => BaseCompanyContact::tableName()], 'cc.company_id = c.id')
            ->where(['c.id' => $this->companyId])
            ->select([
                'c.logo_url as logoUrl',
                'c.full_name as companyName',
                'c.english_name as englishName',
                'c.industry_id as industryId',
                'c.type',
                'c.nature',
                'c.scale',
                'c.website',
                'c.style_atlas as styleAtlas',
                'c.address',
                'c.province_id as provinceId',
                'c.city_id as cityId',
                'c.district_id as districtId',
                'c.is_cooperation as isCooperation',
            ])
            ->asArray()
            ->one();
        //获取单位logo
        $info['logo'] = BaseCompany::getLogoFullUrl($info['logoUrl']);
        //单位行业类型
        $info['industry'] = BaseTrade::getIndustryName($info['industryId']);
        //获取单位类型
        $info['type'] = BaseDictionary::getCompanyTypeName($info['type']);
        //获取单位性质
        $info['nature'] = BaseDictionary::getCompanyNatureName($info['nature']);
        //单位规模
        $info['scale'] = BaseDictionary::getCompanyScaleName($info['scale']);
        //单位风采图集
        $styleAtlas     = array_filter(explode(',', $info['styleAtlas']));
        $styleAtlasList = [];
        foreach ($styleAtlas as $key => $item) {
            $file                        = BaseFile::findOne(['id' => $item]);
            $styleAtlasList[$key]['url'] = FileHelper::getFullUrl($file['path'], $file['platform']);
            $styleAtlasList[$key]['id']  = $item;
        }
        $info['styleAtlasList'] = $styleAtlasList;
        //单位地址
        $info['address']                      = BaseCompany::getAddress($this->companyId);
        $info['companyId']                    = $this->companyId;
        $info['companyActivityUrl']           = '/company/detail/' . $this->companyId . '_h.html';
        $info['predicationTalentPreviewList'] = $this->predicationTalentPreviewList;
        //非合作单位显示--同类型雇主职位推荐
        if ($info['isCooperation'] == BaseCompany::COOPERATIVE_UNIT_NO) {
            try {
                $startTime = microtime(true);

                // 使用工厂类创建推荐服务实例
                $recommendService = \common\service\recommend\RecommendServiceFactory::createCompanyRecommendService();

                $recommendList = $recommendService->setPlatform(CommonService::PLATFORM_WEB)
                    ->companyTypeJob($this->companyId);
                //五个一组打断
                $info['recommendList'] = array_chunk($recommendList, 5);

                // 记录性能指标
                $responseTime = microtime(true) - $startTime;
                $version = get_class($recommendService) === 'common\service\company\RecommendServiceV2' ? 'v2' : 'v1';
                \common\service\recommend\RecommendServiceFactory::recordMetrics('company', $version, $responseTime, true);

            } catch (\Exception $e) {
                $responseTime = microtime(true) - $startTime;
                $version = isset($recommendService) ? (get_class($recommendService) === 'common\service\company\RecommendServiceV2' ? 'v2' : 'v1') : 'unknown';
                \common\service\recommend\RecommendServiceFactory::recordMetrics('company', $version, $responseTime, false);

                \Yii::error("单位推荐查询失败: " . $e->getMessage(), 'recommend');
                $info['recommendList'] = []; // 推荐失败不影响主功能
            }
        } else {
            $info['recommendList'] = [];
        }

        $this->info = $info;
    }

    public function run()
    {
        return $this->render('company_detail_right.html', [
            'info' => $this->info,
        ]);
    }

}