<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Yii;
use yii\base\Widget;

class HengFuWidget extends BaseWidget
{

    public $target;
    public $list;
    public $columnId;
    public $templateType;

    public function init()
    {
        parent::init();

        $name    = BaseHomeColumn::carryColumnToPinyin($this->columnId);
        $tapList = Yii::$app->params['configure']['columnToPosition']['rotation'];

        $numberList = [];
        foreach ($tapList as $v) {
            $numberList[] = $name . "_" . $v['number'];
        }

        $showcaseList = [];
        foreach ($numberList as $key => $value) {
            $homePosition          = BaseHomePosition::findOne(['number' => $value]);
            $positionId            = $homePosition['id'];
            $showcaseList          = BaseShowcase::getByPositionConfig($positionId, $value);
            $tapList[$key]['name'] = $homePosition['chinese_name'];
        }

        if (sizeof($showcaseList) < 1) {
            $showcaseList = [
                [
                    'image_link'  => 'http://img.gaoxiaojob.com/test/xbwyHF_01.jpg',
                    'target_link' => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/boshizhaopinhui2022/index.html',
                ],
                [
                    'image_link'  => 'http://img.gaoxiaojob.com/xbwyHF_03.jpg',
                    'target_link' => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/qutstanding-youth/index.html',
                ],
                [
                    'image_link'  => 'http://img.gaoxiaojob.com/xbwyHF_04.jpg',
                    'target_link' => 'http://www.gaoxiaojob.com/zhaopin/zhuanti/post-doctoral/index.html',
                ],
            ];
        }
        $this->list = $showcaseList;
    }

    public function run(): string
    {
        return $this->render('heng_fu.html', [
            'list' => $this->list,
        ]);
    }
}