<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

class LoginRegistrationWidget extends BaseWidget
{

    public $showcaseName;
    public $list;
    public $columnId;

    public function init()
    {
        parent::init();

        $showcaseList = Yii::$app->params['configure']['login_registration'];

        $this->list = $showcaseList;
    }

    public function run(): string
    {
        return $this->render('login_registration.html', [
            'img_url' => $this->list['img_url'],
            'list'    => $this->list['stream'],
        ]);
    }
}