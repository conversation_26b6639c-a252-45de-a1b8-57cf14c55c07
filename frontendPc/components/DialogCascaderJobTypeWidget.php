<?php

namespace frontendPc\components;

use common\base\models\BaseCategoryJob;
use common\helpers\ArrayHelper;
use yii\base\Widget;

class DialogCascaderJobTypeWidget extends BaseWidget
{
    private array $list = [];

    public function init()
    {
        //这里拿一下职位类型列表分级
        $categoryList = ArrayHelper::objMoreArr(BaseCategoryJob::getCompanyCategoryJobList());
        $topList      = self::getPopularType();
        foreach ($topList[0]['children'] as &$child_item){
            $child_item['v'] = BaseCategoryJob::getName($child_item['k']);
        }
        $list         = array_merge($topList, $categoryList);
        foreach ($list as &$item) {
            $children = $item['children'];
            foreach ($children as &$child) {
                $child['topParentId'] = $item['k'];
            }
            $item['children'] = $children;
        }

        $this->list = $list;
    }

    public function run(): string
    {
        return $this->render('dialog_cascader_job_type.html', [
            'list' => $this->list,
        ]);
    }

    /**
     * pc职位类型--热门类型
     */
    public function getPopularType(): array
    {
        return [
            0 => [
                'k'        => 0,
                'v'        => '热门类型',
                'children' => [
                    [
                        'k'        => 34,
                        'v'        => '辅导员岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 29,
                        'v'        => '博士后',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 33,
                        'v'        => '专职教师/教学科研岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 36,
                        'v'        => '实验技术岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 32,
                        'v'        => '教授/副教授',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 35,
                        'v'        => '教务岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 37,
                        'v'        => '图书馆岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 38,
                        'v'        => '党务行政岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 61,
                        'v'        => '专职科研岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 62,
                        'v'        => '科研助理岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 49,
                        'v'        => '中小学普通教师岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 48,
                        'v'        => '中小学骨干教师岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 85,
                        'v'        => '主治医师/住院医师/医生',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 84,
                        'v'        => '主任医师/副主任医师',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 92,
                        'v'        => '普通医技岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 88,
                        'v'        => '普通药师岗',
                        'parentId' => 0,
                    ],
                    [
                        'k'        => 90,
                        'v'        => '普通护理岗',
                        'parentId' => 0,
                    ],
                ],
            ],
        ];
    }
}