<?php

namespace frontendPc\models;

use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAcademicPage;
use common\base\models\BaseResumeAcademicReward;
use common\helpers\TimeHelper;
use common\libs\Cache;
use yii\base\BaseObject;
use yii\base\Exception;

class ResumeAcademicReward extends BaseResumeAcademicReward
{

    /**
     * 获取学术奖励列表
     * @param $memberId
     */
    public static function getRewardList($memberId): array
    {
        $list = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'obtain_date as obtainDate',
                'name',
                'level',
                'role',
            ])
            // ->limit(self::LIMIT_NUM)
            ->orderBy('obtain_date desc')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['obtainDate'] = TimeHelper::formatToYearMonth($item['obtainDate']);
        }

        return $list;
    }

    /**
     * 保存其他奖励
     * @param $data
     * @throws Exception
     */
    public static function saveInfo($data)
    {
        $name       = $data['name'];
        $obtainDate = $data['obtainDate'];
        $level      = $data['level'];
        $role       = $data['role'];

        //判断数据
        if (strlen($name) < 1 || strlen($obtainDate) < 1) {
            throw new Exception('数据出错!');
        }

        //判断是新增还是修改
        if (!empty($data['id'])) {
            //修改
            $model    = self::findOne($data['id']);
            $memberId = $model->member_id;

            if (empty($model) || $model->status != self::STATUS_ACTIVE) {
                throw new Exception('该奖励记录不存在');
            }
        } else {
            //新增
            $memberId = $data['memberId'];

            $model = new self();

            $model->resume_id = BaseMember::getMainId($memberId);
            $model->member_id = $memberId;
        }

        $model->name        = $name;
        $model->obtain_date = TimeHelper::formatAddDay($obtainDate);
        $model->level       = $level;
        $model->role        = $role;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);
        //新增操作日志
        $log_data = [
            'content' => '保存简历学术奖励信息，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
    }

    /**
     * 删除奖励
     * @param $id
     * @throws Exception
     */
    public static function delReward($id, $memberId)
    {
        $model = self::findOne($id);
        if ($model->member_id != $memberId) {
            throw new Exception('该记录与当前用户信息不匹配');
        }

        $model->status = self::STATUS_DELETE;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);

        //新增操作日志
        $data = [
            'content' => '删除学术奖励id：' . $id,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);
    }

}