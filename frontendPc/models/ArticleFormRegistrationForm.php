<?php

namespace frontendPc\models;

use common\base\models\BaseActivityForm;
use common\base\models\BaseActivityFormOptionSign;
use common\base\models\BaseActivityFormRegistrationForm;
use common\base\models\BaseActivityFormRegistrationFormLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseUploadForm;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use yii\base\Exception;
use Yii;

class ArticleFormRegistrationForm extends BaseActivityFormRegistrationForm
{
    /**
     * 活动表单报名
     * @throws Exception
     * @throws \Exception
     */
    public static function registrationResumeActivityForm($keywords): bool
    {
        $memberId = Yii::$app->user->id;
        if (!$memberId) {
            throw new Exception('请登录后报名');
        }

        $resumeId              = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $keywords['resumeId']  = $resumeId;
        $keywords['memberId']  = $memberId;
        $keywords['birthday']  = TimeHelper::formatAddDay($keywords['birthday']);
        $keywords['beginDate'] = TimeHelper::formatAddDay($keywords['beginDate']);
        $keywords['endDate']   = TimeHelper::formatAddDay($keywords['endDate']);
        $fileToken             = $keywords['fileToken'];
        $fileIds               = '';

        // if (!self::checkSumit($resumeId, $keywords['activityFormId'], $keywords['optionIds'])) {
        //     throw new Exception('超出场次限制');
        // }

        foreach ($fileToken as $item) {
            if ($item['uploadType'] == 1) {
                $fileIds = $fileIds . $item['value'] . ',';
            } else {
                $model    = new BaseUploadForm();
                $fileData = $model->resumeCopyToFile($item['value'], $memberId);
                $fileIds  = $fileIds . $fileData['id'] . ',';
            }
        }
        // 去掉两边逗号字符串
        $fileIds               = trim($fileIds, ',');
        $keywords['fileToken'] = $fileIds;

        $registrationActivityFormId = BaseActivityFormRegistrationForm::registrationActivityForm($keywords);
        if (!$registrationActivityFormId) {
            throw new Exception('报名失败');
        }
        /** 新增活动表单场次签到信息*/
        $optionIds = explode(',', $keywords['optionIds']);
        foreach ($optionIds as $id) {
            $signModel = BaseActivityFormOptionSign::findOne([
                'option_id' => $id,
                'resume_id' => $keywords['resumeId'],
            ]);
            if (!$signModel) {
                $serialNumber = BaseActivityFormOptionSign::find()
                                    ->select([
                                        'serial_number',
                                    ])
                                    ->where([
                                        'option_id' => $id,
                                    ])
                                    ->orderBy('serial_number desc')
                                    ->asArray()
                                    ->one()['serial_number'] + 1;

                $signData = [
                    'activityFormId'     => $keywords['activityFormId'],
                    'optionId'           => $id,
                    'registrationFormId' => $registrationActivityFormId,
                    'resumeId'           => $keywords['resumeId'],
                    'serialNumber'       => $serialNumber,
                    'signTime'           => TimeHelper::ZERO_TIME,
                ];
                BaseActivityFormOptionSign::setNewActivityFormOptionSign($signData);
            }
        }

        $resumeStep = BaseResumeComplete::getResumeStep($resumeId);
        if ($resumeStep < 4) {
            BaseResumeEducation::saveInfo(array_merge($keywords, [
                'studyBeginDate'  => $keywords['beginDate'],
                'studyEndDate'    => $keywords['endDate'],
                'isOverseasStudy' => $keywords['isAbroad'],
            ]));
            BaseResume::saveActivityFormUserBaseInfo($keywords);
            BaseResumeEducation::saveInfo(array_merge($keywords, [
                'studyBeginDate'  => $keywords['beginDate'],
                'studyEndDate'    => $keywords['endDate'],
                'isOverseasStudy' => $keywords['isAbroad'],
            ]));
            BaseResumeIntention::saveInfo($keywords);
            BaseResume::updateUserRealStatus();
        }

        $baseInformation = json_encode([
            'name'                  => $keywords['name'],
            'gender'                => $keywords['gender'],
            'birthday'              => $keywords['birthday'],
            'household_register_id' => $keywords['householdRegisterId'],
            'mobile'                => $keywords['mobile'],
            'email'                 => $keywords['email'],
            'political_status_id'   => $keywords['politicalStatusId'],
            'title_id'              => $keywords['titleId'],
            'residence'             => $keywords['residence'],
        ]);
        $topEducation    = json_encode([
            'school'            => $keywords['school'],
            'college'           => $keywords['college'],
            'education_id'      => $keywords['educationId'],
            'begin_date'        => $keywords['beginDate'],
            'end_date'          => $keywords['endDate'],
            'major_id'          => $keywords['majorId'],
            'major_custom'      => $keywords['majorCustom'],
            'is_recruitment'    => $keywords['isRecruitment'],
            'is_project_school' => $keywords['isProjectSchool'],
            'is_abroad'         => $keywords['isAbroad'],
        ]);
        $jobIntention    = json_encode([
            'work_status'      => $keywords['workStatus'],
            'arrive_date_type' => $keywords['arriveDateType'],
            'job_category_id'  => $keywords['jobCategoryId'],
            'nature_type'      => $keywords['natureType'],
            'area_id'          => $keywords['areaId'],
            'wage_type'        => $keywords['wageType'],
        ]);
        $keywords        = array_merge($keywords, [
            'baseInformation' => $baseInformation,
            'topEducation'    => $topEducation,
            'jobIntention'    => $jobIntention,
        ]);
        BaseActivityFormRegistrationFormLog::registrationActivityFormLog($keywords);

        return true;
    }

    /**
     * 活动表单报名成功
     */
    public static function getResumeActivityFormSuccess($keywords): array
    {
        $list = BaseActivityForm::find()
            ->select([
                'id',
                'success_tips',
                'background_url',
                'community_file_ids',
                'name',
            ])
            ->where([
                'id' => $keywords['activityFormId'],
            ])
            ->asArray()
            ->one() ?: [];

        $list['resumeStep']          = $keywords['resumeStep'];
        $list['communityFileList']   = FileHelper::getKvListByIds($list['community_file_ids']);
        $list['communityFileNumber'] = sizeof($list['communityFileList']);

        return $list;
    }
}