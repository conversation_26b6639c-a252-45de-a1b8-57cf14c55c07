<?php

namespace frontendPc\models;

use common\base\models\BaseCompanyCooperationApply;
use yii\base\Exception;

class CompanyCooperationApply extends BaseCompanyCooperationApply
{

    /**
     * @throws Exception
     */
    public static function create($data)
    {
        if (!$data['name']) {
            throw new Exception('单位名称不能为空!');
        }
        if (!$data['contact']) {
            throw new Exception('联系人不能为空!');
        }
        if (!$data['department']) {
            throw new Exception('所属部门不能为空!');
        }
        if (!$data['mobile']) {
            throw new Exception('联系电话不能为空!');
        }
        if (!$data['job_name']) {
            throw new Exception('招聘岗位名称不能为空!');
        }
        if (!$data['education']) {
            throw new Exception('学历要求不能为空!');
        }
        $self              = new self();
        $self->name        = $data['name'];
        $self->type        = $data['type'];
        $self->contact     = $data['contact'];
        $self->department  = $data['department'];
        $self->mobile      = $data['mobile'];
        $self->job_name    = $data['job_name'];
        $self->education   = $data['education'];
        $self->recruitment = $data['recruitment'] ?: '';
        $self->tx_im       = $data['tx_im'] ?: '';
        $self->email       = $data['email'] ?: '';
        $self->content     = $data['content'] ?: '';

        if (!$self->save()){
            throw new Exception($self->getFirstErrorsMessage());
        }

    }
}