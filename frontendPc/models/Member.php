<?php

namespace frontendPc\models;

use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAnnouncementFootprint;
use common\base\models\BaseResumeJobFootprint;
use common\base\models\BaseResumeStatData;
use common\base\models\BaseResumeWork;
use common\base\models\BaseResumeWxBind;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\libs\Cache;
use common\service\commonResume\CommonResumeApplication;
use Egulias\EmailValidator\Result\Reason\DotAtEnd;
use frontendPc\models\Resume;
use common\base\models\BaseMember;
use common\helpers\ValidateHelper;
use Yii;
use yii\base\Exception;

class Member extends BaseMember
{
    /**
     * 获取用户设置信息
     * @param $memberId
     * @return array|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public static function getSettingInfo($memberId)
    {
        if (empty($memberId)) {
            throw new Exception('用户登录状态错误');
        }
        $member = self::find()
            ->where(['id' => $memberId])
            ->select([
                'type',
                'email',
                'mobile',
                'mobile_code as mobileCode',
                'password',
                'username',
                'email_register_status as emailRegisterStatus',
            ])
            ->asArray()
            ->one();
        if (empty($member)) {
            throw new Exception('用户信息不存在');
        }
        $member['isSetPassword']       = 0;
        $member['isSetEmail']          = 0;
        $member['emailRegisterStatus'] = (int)$member['emailRegisterStatus'];
        if (!empty($member['mobile'])) {
            $member['isSetMobile'] = 1;
        }
        if (!empty($member['email'])) {
            $member['isSetEmail'] = 1;
        }
        if (!empty($member['password'])) {
            $member['isSetPassword'] = 1;
        }
        unset($member['password']);
        if ($member['type'] == BaseMember::TYPE_PERSON) {
            $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
            if (empty($resumeId)) {
                throw new Exception('用户信息不存在');
            }
            $member['bind'] = BaseResumeWxBind::checkWxBind($resumeId);
        }
        $member['mobile']     = StringHelper::strMobileDes($member['mobile']);
        $member['mobileCode'] = StringHelper::getFullMobileCode($member['mobileCode']);
        //获取拼接好的手机号
        $member['fullMobile'] = StringHelper::strMobileDes(BaseMember::getFullMobile($memberId));
        //是否微信绑定
        $member['isWxBind'] = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'is_wx_bind');
        //绑定文案
        $member['isWxBindText'] = $member['isWxBind'] == BaseCompanyMemberInfo::IS_WX_BIND_YES ? '已绑定' : '未绑定';

        return $member;
    }

    /**
     * 修改用户名
     * @param $memberId
     * @param $newUsername
     * @throws Exception
     */
    public static function changeUsername($memberId, $newUsername)
    {
        //判断新用户名是否被占用了
        $hasUsername = self::find()
            ->where(['username' => $newUsername])
            ->select('id')
            ->asArray()
            ->one();
        if (!empty($hasUsername)) {
            throw new Exception('用户名已被占用');
        }
        if (empty($memberId)) {
            throw new Exception('用户登录状态错误');
        }
        //校验新用户名是否正确
        if (!ValidateHelper::isUsername($newUsername)) {
            throw new Exception('用户名格式不正确');
        }
        $member = Member::findOne($memberId);

        $member->username = $newUsername;
        $member->save();
    }

    /**
     * 保存用户密码
     * @param $memberId
     * @param $password
     * @throws Exception
     */
    public static function SavePassword($memberId, $password)
    {
        if (empty($password)) {
            throw new Exception('密码不能为空');
        }
        //验证密码格式
        if (!ValidateHelper::isPassword($password)) {
            throw new Exception('密码格式不正确');
        }
        //判断当前用户是否已经有密码了
        $memberModel = self::findOne($memberId);
        if (!empty($memberModel->password)) {
            throw new Exception('当前用户已设置密码');
        }

        $memberModel->password = \Yii::$app->getSecurity()
            ->generatePasswordHash($password);
        $memberModel->save();
    }

    /**
     * 获取个人中心页面默认显示内容
     * @param $memberId
     * @return array
     * @throws \Exception
     */
    public static function getPersonCenterInfo($memberId)
    {
        //获取站内投递
        $onSiteApplyList = JobApply::getApplyList([
            'memberId' => $memberId,
            'pageSize' => JobApply::PERSONAL_SHOW_LIST,
        ]);
        //获取关注单位
        $followCompanyList = CompanyCollect::getList([
            'memberId' => $memberId,
            'pageSize' => CompanyCollect::PERSONAL_SHOW_LIST,
        ]);
        //获取推荐职位
        // $recommendJobList = Job::getRecommendList(['pageSize' => Job::PERSONAL_SHOW_LIST]);
        //获取用户信息
        $userInfo = Member::getMemberInfo($memberId);
        //获取求职动态
        $statData             = Member::getJobData($memberId);
        $userInfo['statData'] = $statData;
        //获取简历资料
        $resumeInfo = Resume::getPersonCenterInfo($memberId);

        //获取日程列表
        $today              = date('Y-m-d', time());
        $scheduleSearchData = [
            'date_start' => $today,
            'date_end'   => $today,
        ];
        $scheduleList       = MemberSchedule::getMemberSchedule($scheduleSearchData, $memberId);
        $scheduleInfo       = [
            'date' => $today,
            'list' => $scheduleList,
        ];
        //获取职位投递邀请列表
        $searchData['memberId'] = $memberId;
        $searchData['resumeId'] = BaseMember::getMainId($memberId);
        $searchData['limit']    = 4;

        $service    = CommonResumeApplication::getInstance();
        $inviteList = $service->getPersonCenterInviteJobList($searchData);

        $list = [
            'onSiteApplyList'   => $onSiteApplyList,
            'followCompanyList' => $followCompanyList,
            'recommendJobList'  => $recommendJobList,
            'userInfo'          => $userInfo,
            'resumeInfo'        => $resumeInfo,
            'scheduleInfo'      => $scheduleInfo,
            'inviteList'        => $inviteList,
        ];

        return $list;
    }

    /**
     * 获取用户求职动态数据
     * @param $memberId
     * @return array
     */
    public static function getJobData($memberId)
    {
        $resumeId = Member::getMainId();
        //获取用户最新消息数量
        $messageAmount = MemberMessage::getUnreadCount($memberId);
        //获取我的投递总数
        //        $applyAmount = JobApply::getMemberApplyAmount($memberId);
        $applyAmount = BaseResumeStatData::getApplyTotalAmount($resumeId);
        //获取面试邀请次数
        $interviewAmount = BaseResumeStatData::findOneVal(['resume_id' => $resumeId], 'interview_amount');
        //        $interviewAmount = JobApply::getInterviewAmount(['resume_member_id' => $memberId]);
        //获取关注单位数量
        $companyCollectAmount = CompanyCollect::getAmount($memberId);
        //获取收藏公告数量
        $announcementCollectAmount = AnnouncementCollect::getAmount($memberId);
        //获取职位收藏数量
        $jobCollectAmount = JobCollect::getAmount($memberId);

        $data = [
            'messageAmount'             => $messageAmount,
            'applyAmount'               => $applyAmount,
            'interviewAmount'           => $interviewAmount,
            'companyCollectAmount'      => $companyCollectAmount,
            'announcementCollectAmount' => $announcementCollectAmount,
            'jobCollectAmount'          => $jobCollectAmount,
        ];

        return $data;
    }

    /**
     * 获取列表页码用户信息
     * @param $memberId
     * @return array
     * @throws \Exception
     */
    public static function getListPageMemberInfo($memberId)
    {
        //获取用户信息
        $userInfo = self::find()
            ->alias('m')
            ->leftJoin(['r' => Resume::tableName()], 'r.member_id = m.id')
            ->leftJoin(['e' => ResumeEducation::tableName()], 'e.id = r.last_education_id')
            ->where(['m.id' => $memberId])
            ->select([
                'r.id as resumeId',
                'm.id',
                'm.username',
                'm.avatar',
                'r.gender',
                'r.name',
                'r.age',
                'r.work_experience',
                'r.work_status',
                'r.arrive_date_type',
                'r.arrive_date',
                'r.last_education_id',
                'e.education_id',
            ])
            ->asArray()
            ->one();
        //获取到岗时间
        $workStatus = Dictionary::getJobStatusName($userInfo['work_status']);
        if ($userInfo['arrive_date_type'] != Resume::CUSTOM_ARRIVE_DATE_TYPE) {
            $arriveDate = Dictionary::getArriveDateName($userInfo['arrive_date_type']);
        } else {
            $arriveDate = $userInfo['arrive_date'];
        }
        $userInfo['intentionInfo'] = $workStatus . '-' . $arriveDate;

        //获取我的投递总数
        $statData['applyAmount'] = BaseResumeStatData::getApplyTotalAmount($userInfo['resumeId']);
        //获取面试邀请次数
        $statData['interviewAmount'] = BaseResumeStatData::findOneVal(['resume_id' => $userInfo['resumeId']],
            'interview_amount');

        //获取职位收藏数量
        $statData['jobCollectAmount'] = JobCollect::getAmount($memberId);
        //获取公告收藏数量
        $statData['announcementCollectAmount'] = AnnouncementCollect::getAmount($memberId);
        //获取单位收藏数量
        $statData['companyCollectAmount'] = CompanyCollect::getAmount($memberId);
        //获取资讯收藏数量
        $statData['newsCollectAmount'] = NewsCollect::getAmount($memberId);
        //获取站内投递数量
        $statData['onSiteApplyAmount'] = BaseResumeStatData::findOneVal(['resume_id' => $userInfo['resumeId']],
            'on_site_apply_amount');
        //获取站外投递数量
        $statData['offSiteApplyAmount'] = BaseResumeStatData::findOneVal(['resume_id' => $userInfo['resumeId']],
            'off_site_apply_amount');

        // 获取足迹相关数据(90天内的)
        $ninetyDay                      = date('Y-m-d', strtotime('-90 days'));
        $statData['jobFootprintAmount'] = BaseResumeJobFootprint::find()
            ->select('job_id,date')
            ->where(['resume_id' => $userInfo['resumeId']])
            ->andWhere([
                '>=',
                'date',
                $ninetyDay,
            ])
            ->count();

        $statData['announcementFootprintAmount'] = BaseResumeAnnouncementFootprint::find()
            ->select('job_id,date')
            ->where(['resume_id' => $userInfo['resumeId']])
            ->andWhere([
                '>=',
                'date',
                $ninetyDay,
            ])
            ->count();

        $userInfo['statData'] = $statData;
        //获取最高学历
        $userInfo['education']       = Dictionary::getEducationName($userInfo['education_id']);
        $userInfo['work_experience'] = BaseResumeWork::getWorkExperienceText($userInfo['resumeId']);

        //获取简历资料
        $resumeInfo = Resume::getPersonCenterInfo($memberId);
        //获取头像全路径
        $userInfo['avatar'] = FileHelper::getFullUrl($userInfo['avatar']);

        $data = [
            'userInfo'   => $userInfo,
            'resumeInfo' => $resumeInfo,
        ];

        return $data;
    }

    /**
     * 单位端账号设置
     * 个人资料编辑
     * @throws Exception
     */
    public static function editCompanyMemberInfo($data)
    {
        //判断新用户名是否被占用了
        $memberId    = Yii::$app->user->id;
        $hasUsername = self::find()
            ->where(['username' => $data['username']])
            ->andWhere([
                '<>',
                'id',
                $data['memberId'],
            ])
            ->select('id')
            ->asArray()
            ->one();

        if (!empty($hasUsername)) {
            throw new Exception('用户名已被占用');
        }
        if (empty($memberId)) {
            throw new Exception('用户登录状态错误');
        }
        //校验新用户名是否正确
        if (!ValidateHelper::isUsername($data['username'])) {
            throw new Exception('用户名格式不正确');
        }

        //用户名
        $memberModel           = Member::findOne($memberId);
        $memberModel->username = $data['username'];
        $memberModel->avatar   = $data['avatar'];
        $memberModel->save();

        //姓名、所在部门
        $companyMemberInfoModel             = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
        $companyMemberInfoModel->contact    = $data['contact'];
        $companyMemberInfoModel->department = $data['department'];
        $companyMemberInfoModel->save();
    }

    /**
     * 验证手机号码
     * @param $mobile
     * @throws Exception
     */
    public static function verifyMemberMobile($mobile)
    {
        //获取用户ID
        $memberId = Yii::$app->user->id;
        //如果$mobile含有+号，则拼接一下
        //含有+号
        $fullMobile = BaseMember::getFullMobile($memberId);
        if ($fullMobile != $mobile) {
            throw new Exception('验证失败，请重新输入');
        }
        $authKey = StringHelper::randText(10);
        Cache::set(Cache::PC_MEMBER_MOBILE_BIND_KEY . ':' . $memberId, $authKey, 300);

        return ['authKey' => $authKey];
    }
}