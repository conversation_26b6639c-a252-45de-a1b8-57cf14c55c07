<?php

namespace frontendPc\models;

use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResumeResearchDirection;
use common\helpers\ArrayHelper;
use common\libs\Cache;
use yii\base\BaseObject;
use yii\base\Exception;

class ResumeResearchDirection extends BaseResumeResearchDirection
{


    public static function delDirection($id, $memberId)
    {
        $model = self::findOne($id);
        if ($model->member_id != $memberId) {
            throw new Exception('该记录与当前用户信息不匹配');
        }

        $model->status = self::STATUS_DELETE;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        //新增操作日志
        $data = [
            'content' => '删除研究方向id：' . $id,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);
    }

}