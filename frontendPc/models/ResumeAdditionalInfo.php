<?php

namespace frontendPc\models;

use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAdditionalInfo;
use common\libs\Cache;
use yii\base\Exception;

class ResumeAdditionalInfo extends BaseResumeAdditionalInfo
{
    /**
     * 保存/编辑
     * @param $data
     * @throws Exception
     */
    public static function saveInfo($data)
    {
        //判断参数
        if (strlen($data['content']) < 1) {
            throw new Exception('主题描述不能为空!');
        }
        if (strlen($data['themeId']) < 1 && strlen($data['themeName']) < 1) {
            throw new Exception('主题不能为空!');
        }

        if (!empty($data['id'])) {
            //说明是编辑
            $model = self::findOne($data['id']);
            if (empty($model) || $model->status != self::STATUS_ACTIVE) {
                throw new Exception('附加信息记录不存在!');
            }
        } else {
            //说明是新增
            $model = new self;
            //新增需保存用户id，简历id
            $memberId = $data['memberId'];

            $model->resume_id = BaseMember::getMainId($memberId);
            $model->member_id = $memberId;
        }

        $model->theme_id   = $data['themeId'];
        $model->content    = $data['content'];
        $model->theme_name = $data['themeName'];

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //新增操作日志
        $log_data = [
            'content' => '保存简历附加信息，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
        //更新简历最后更新时间
        BaseResume::updateLastUpdateTime($model->resume_id);
    }

    /**
     * 删除
     * @param $id
     * @throws Exception
     */
    public static function delInfo($id, $memberId)
    {
        $model = self::findOne($id);
        if ($model->member_id != $memberId) {
            throw new Exception('该记录与当前用户信息不匹配');
        }

        $model->status = self::STATUS_DELETE;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        //新增操作日志
        $data = [
            'content' => '删除附加信息id：' . $id,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);
        //更新简历最后更新时间
        BaseResume::updateLastUpdateTime($model->resume_id);
    }

    /**
     * 获取附加信息列表
     * @param $memberId
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getInfoList($memberId)
    {
        $list = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'theme_id as themeId',
                'theme_name as themeName',
                'content',
            ])
            //            ->limit(self::LIMIT_NUM)
            ->asArray()
            ->all();
        foreach ($list as $k => &$v) {
            $v['themeIdName'] = Dictionary::getThemeName($v['themeId']);
            if ($v['themeId'] == 0 || $v['themeId'] == '0') {
                $v['themeId'] = '';
            }
        }

        return $list;
    }

}