<?php

namespace frontendPc\models;

use common\base\models\BaseArticleClickLog;
use common\base\models\BaseDailyAnnouncementSummary;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;

class DailyAnnouncementSummary extends BaseDailyAnnouncementSummary
{
    public static function getDetailUrl($id)
    {
        return UrlHelper::toRoute([
            '/daily-announcement-summary/detail',
            'id' => $id,
        ]);
    }

    public static function getDetail($id)
    {
        $rs = self::find()
            ->select([
                'title',
                'daily_announcement_summary.status',
                'content',
                'seo_description',
                'seo_keywords',
                'author',
                'belong_date',
                'article_id',
            ])
            ->innerJoin(['article' => Article::tableName()], 'article.id = daily_announcement_summary.article_id')
            ->where(['daily_announcement_summary.id' => $id])
            ->asArray()
            ->one();
        if (!$rs) {
            self::ThrowException('数据不存在');
        }

        $memberId = \Yii::$app->user->id ?? 0;
        BaseArticleClickLog::create($rs['article_id'], $memberId);

        return $rs;
    }

    public static function getList($page, $pageSize)
    {
        $query = self::find()
            ->select('daily_announcement_summary.id,article.title,daily_announcement_summary.belong_date,article.click')
            ->innerJoin(['article' => Article::tableName()], 'article.id = daily_announcement_summary.article_id')
            ->where(['daily_announcement_summary.status' => self::STATUS_ACTIVE]);

        $count = $query->count();

        $pages = self::setPage($count, $page, $pageSize);

        $list = $query->orderBy('belong_date desc')
            ->offset($pages['offset'])
            ->limit($pageSize)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url']   = self::getDetailUrl($item['id']);
            $item['month'] = str_replace('-', '.', substr($item['belong_date'], 0, 7));
            $item['date']  = substr($item['belong_date'], 8, 2);
        }

        $pages['count'] = $count;

        return [
            'list'  => $list,
            'pages' => $pages,
        ];
    }

    public static function getSimpleList($count = 10)
    {
        $list = self::find()
            ->select('daily_announcement_summary.id,article.title,daily_announcement_summary.belong_date,article.click')
            ->innerJoin(['article' => Article::tableName()], 'article.id = daily_announcement_summary.article_id')
            ->where(['daily_announcement_summary.status' => self::STATUS_ACTIVE])
            ->orderBy('belong_date desc')
            ->limit($count)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url']   = self::getDetailUrl($item['id']);
            $item['month'] = str_replace('-', '.', substr($item['belong_date'], 0, 7));
            $item['date']  = substr($item['belong_date'], 8, 2);
        }

        return $list;
    }

    /**
     * 每日汇总上下篇
     */
    public static function getUpAndDownList($id): array
    {
        $belongDate = BaseDailyAnnouncementSummary::findOneVal([
            'id' => $id,
        ], 'belong_date');

        $upOne = self::find()
            ->select('daily_announcement_summary.id,article.title,daily_announcement_summary.belong_date')
            ->innerJoin(['article' => Article::tableName()], 'article.id = daily_announcement_summary.article_id')
            ->where(['daily_announcement_summary.status' => self::STATUS_ACTIVE])
            ->andWhere([
                '>',
                'daily_announcement_summary.belong_date',
                $belongDate,
            ])
            ->orderBy('belong_date asc')
            ->asArray()
            ->one();

        $downOne = self::find()
            ->select('daily_announcement_summary.id,article.title,daily_announcement_summary.belong_date')
            ->innerJoin(['article' => Article::tableName()], 'article.id = daily_announcement_summary.article_id')
            ->where(['daily_announcement_summary.status' => self::STATUS_ACTIVE])
            ->andWhere([
                '<',
                'daily_announcement_summary.belong_date',
                $belongDate,
            ])
            ->orderBy('belong_date desc')
            ->asArray()
            ->one();

        $list = [];
        if ($upOne) {
            $list['up'] = $upOne;
        }
        if ($downOne) {
            $list['down'] = $downOne;
        }

        foreach ($list as $k => $item) {
            $list[$k]['url'] = self::getDetailUrl($item['id']);
        }

        return $list;
    }

}