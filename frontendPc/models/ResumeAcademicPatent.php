<?php

namespace frontendPc\models;

use common\base\models\BaseDictionary;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAcademicPatent;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use yii\base\Exception;

class ResumeAcademicPatent extends BaseResumeAcademicPatent
{
    /**
     * 获取学术专利列表
     * @param $memberId
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getPatentList($memberId): array
    {
        $list = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'number',
                'name',
                'position',
                'authorization_date as authorizationDate',
                'finish_status as finishStatus',
                'description',
            ])
            //            ->limit(self::LIMIT_NUM)
            ->orderBy('authorization_date desc')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['authorizationDate'] = TimeHelper::formatToYearMonth($item['authorizationDate']);
            $item['positionText']      = BaseDictionary::getPatentRankName($item['position']);
        }

        return $list;
    }

    /**
     * 保存/修改信息
     * @param $data
     * @throws Exception
     */
    public static function saveInfo($data)
    {
        $name              = $data['name'];
        $number            = $data['number'];
        $authorizationDate = $data['authorizationDate'];
        $finishStatus      = $data['finishStatus'];
        $position          = $data['position'];
        $description       = $data['description'];

        //判断参数
        if (strlen($name) < 1 || strlen($number) < 1 || strlen($authorizationDate) < 1 || strlen($finishStatus) < 1 || mb_strlen($description,
                'UTF-8') > 500) {
            throw new Exception('数据出错!');
        }

        //判断是新增还是修改
        if (!empty($data['id'])) {
            //编辑
            $model    = self::findOne($data['id']);
            $memberId = $model->member_id;
            if (empty($model) || $model->status != self::STATUS_ACTIVE) {
                throw new Exception('该学术专利记录不存在');
            }
        } else {
            //新增
            $memberId = $data['memberId'];

            $model = new self();

            $model->resume_id = BaseMember::getMainId($memberId);
            $model->member_id = $memberId;
        }

        $model->name               = $name;
        $model->number             = $number;
        $model->authorization_date = TimeHelper::formatAddDay($authorizationDate);
        $model->finish_status      = $finishStatus;
        $model->position           = $position;
        $model->description        = $description;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);
        //新增操作日志
        $log_data = [
            'content' => '保存简历学术专利信息，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
    }

    public static function delPatent($id, $memberId)
    {
        $model = self::findOne($id);
        if ($model->member_id != $memberId) {
            throw new Exception('该记录与当前用户信息不匹配');
        }

        $model->status = self::STATUS_DELETE;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);

        //新增操作日志
        $data = [
            'content' => '删除学术专利id：' . $id,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);
    }

}